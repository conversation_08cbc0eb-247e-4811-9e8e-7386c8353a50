FROM node:16-alpine
WORKDIR /app

RUN apk add --no-cache \
    chromium \
    nss \
    freetype \
    harfbuzz \
    ca-certificates \
    ttf-freefont

ENV PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium-browser
COPY package*.json ./
RUN npm install
RUN npm install -g typescript
RUN npm install -g nodemon

COPY . .
RUN tsc

COPY ./utils/PdfUtils/images/logo.png /app/dist/utils/PdfUtils/images/logo.png
EXPOSE 5009
CMD ["nodemon", "dist/server.js"]
