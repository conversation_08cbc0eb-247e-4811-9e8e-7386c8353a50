import { FilterQuery, Types } from "mongoose";
import { BookStockDocument } from "../db/models/BookStock";
import { RoleCode, UserDocument } from "../db/models/User";
import BookStockRepo from "../db/repositories/BookStockRepo";
import { BadRequestError, NotFoundError } from "../helpers/ApiError";

export default class BookStockService {
  public static async create(bookStock: Partial<BookStockDocument>) {
    // Unique Book + Office
    const foundBookOffice = await BookStockRepo.findOne({
      book: bookStock?.book,
      office: bookStock?.office,
    });

    if (foundBookOffice) {
      throw new BadRequestError(
        "A stock entry for the specified book and office already exists. Please update the existing entry or choose a different combination."
      );
    }

    const newBookStock = await BookStockRepo.create(bookStock);
    return newBookStock;
  }

  public static async findOne(
    user: Partial<UserDocument>,
    bookStock: FilterQuery<BookStockDocument>
  ): Promise<BookStockDocument | null> {
    let filtredBookStock = bookStock;

    // Filter by office if user has access to it. Only super admins can access all books.
    if (user?.role !== RoleCode.SUPER_ADMIN) {
      if (user?.office) {
        filtredBookStock = { ...filtredBookStock, office: user?.office?._id };
      } else {
        throw new NotFoundError("User must have an office!");
      }
    }

    const foundBookStock = await BookStockRepo.findOne(filtredBookStock);
    if (!foundBookStock) {
      throw new NotFoundError("Book Stock not found !");
    }
    return foundBookStock;
  }

  public static async find(
    user: Partial<UserDocument>,
    query: any
  ): Promise<any> {
    let filter = {};

    // Filter by office if user has access to it. Only super admins can access all books.
    if (user?.role !== RoleCode.SUPER_ADMIN) {
      if (user?.office) {
        filter = {
          ...filter,
          office: user?.office._id,
        };
      } else {
        throw new NotFoundError("User must have an office!");
      }
    }
    const bookStocks = await BookStockRepo.find(filter, query);
    const { docs, ...meta } = bookStocks;
    return { docs, meta };
  }

  public static async findOneAndUpdate(
    bookStock: FilterQuery<BookStockDocument>,
    update: FilterQuery<BookStockDocument>
  ): Promise<BookStockDocument | null> {
    const updatedBookStock = await BookStockRepo.findOneAndUpdate(
      bookStock,
      update
    );

    if (!updatedBookStock) {
      throw new NotFoundError("Book Stock not found !");
    }

    return updatedBookStock;
  }
  public static async deleteOne(bookStock: FilterQuery<BookStockDocument>) {
    const deletedBookStock = await BookStockRepo.deleteOne(bookStock);

    if (!deletedBookStock) {
      throw new NotFoundError("Book Stock not found !");
    }
    return;
  }

  public static async availableStockPerOffice(
    user: Partial<UserDocument>,
    query: any
  ): Promise<any> {
    let filter: FilterQuery<BookStockDocument> = { quantity: { $gt: 0 } };

    // Super admin must have office
    if (user?.role === RoleCode.SUPER_ADMIN) {
      if (query.office) {
        filter = { ...filter, office: new Types.ObjectId(query.office) };
      } else {
        throw new BadRequestError("Must pass an office!");
      }
    }

    // Filter by office if user has access to it. Only super admins can access all books.
    if (user?.role !== RoleCode.SUPER_ADMIN) {
      if (user?.office) {
        filter = {
          ...filter,
          office: user?.office._id,
        };
      } else {
        throw new BadRequestError("User must have an office!");
      }
    }

    const bookStocks = await BookStockRepo.findAvailable(filter, query);
    const { docs, ...meta } = bookStocks;
    return { docs, meta };
  }
}
