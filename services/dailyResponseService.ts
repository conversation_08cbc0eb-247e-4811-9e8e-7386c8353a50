import { endOfDay, startOfDay } from "date-fns";
import { Types } from "mongoose";
import DailyResponse from "../db/models/DailyResponse";

export default class dailyResponseService {
  public static handleQuery(reqQuery: any) {
    const dailyKpis: { dailyKpi: string; response: [] }[] =
      reqQuery.dailyKpis as {
        dailyKpi: string;
        response: [];
      }[];
    const {
      clientName,
      clientContact,
      office,
      dailyQuestion,
      admin,
      startDate,
      endDate,
    } = reqQuery;
    const query: any = {};

    if (dailyKpis && dailyKpis.length > 0) {
      const kpiFilters = dailyKpis.map(({ dailyKpi, response }) => {
        // Handle boolean values
        const processedResponse = response.map((value) =>
          value === "true" ? true : value === "false" ? false : value
        );

        return {
          dailyKpis: {
            $elemMatch: {
              dailyKpi: dailyKpi,
              response: { $all: processedResponse },
            },
          },
        };
      });

      query["$and"] = kpiFilters;
    }

    if (clientContact) {
      query["clientContact"] = clientContact;
    }

    if (clientName) {
      query["clientName"] = clientName;
    }

    if (office) {
      query["office"] = office;
    }

    if (dailyQuestion) {
      query["dailyQuestion"] = dailyQuestion;
    }

    if (admin) {
      query["admin"] = { $in: admin };
    }

    if (startDate && endDate) {
      const convertedStartDate = startOfDay(startDate);
      const convertedEndDate = endOfDay(endDate);
      query["createdAt"] = { $gte: convertedStartDate, $lte: convertedEndDate };
    }
    return query;
  }

  public static getNumberDailyResponseByUser(
    startDate: Date,
    endDate: Date,
    user: Types.ObjectId
  ) {
    return DailyResponse.countDocuments({
      admin: user,
      createdAt: {
        $gte: startDate,
        $lte: endDate,
      },
    });
  }
}
