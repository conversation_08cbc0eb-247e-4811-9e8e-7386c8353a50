import { FilterQuery } from "mongoose";
import { SectionDocument } from "../db/models/Section";
import SectionRepo from "../db/repositories/SectionRepo";
import { BadRequestError, NotFoundError } from "../helpers/ApiError";

export default class SectionService {
  public static async create(section: Partial<SectionDocument>) {
    // Unique name
    const existSection = await SectionRepo.findOne({ name: section.name });

    if (existSection) {
      throw new BadRequestError(
        `Section name "${section.name}" already exists!`
      );
    }

    const newSection = await SectionRepo.create(section);
    return newSection;
  }

  public static async findOne(
    section: Partial<SectionDocument>
  ): Promise<SectionDocument | null> {
    const foundSection = await SectionRepo.findOne(section);
    if (!foundSection) {
      throw new NotFoundError("Section not found !");
    }
    return foundSection;
  }

  public static async find(query: any): Promise<any> {
    const sections = await SectionRepo.find({}, query);
    const { docs, ...meta } = sections;
    return { docs, meta };
  }

  public static async findOneAndUpdate(
    section: FilterQuery<SectionDocument>,
    update: Partial<SectionDocument>
  ): Promise<SectionDocument | null> {
    const updatedSection = await SectionRepo.findOneAndUpdate(section, update);

    if (!updatedSection) {
      throw new NotFoundError("Section not found !");
    }

    return updatedSection;
  }
  public static async deleteOne(section: FilterQuery<SectionDocument>) {
    const deletedSection = await SectionRepo.deleteOne(section);

    if (!deletedSection) {
      throw new NotFoundError("Section not found !");
    }
    return;
  }
}
