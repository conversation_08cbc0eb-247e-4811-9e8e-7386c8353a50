import { Response } from "express";
import { Types } from "mongoose";
import CashDeskPaymentRepo from "../db/repositories/CashDeskPaymentRepo";
import { pdfGenerator } from "../utils/PdfUtils/pdfGenerator";

export default class cashDeskPaymentService {
  // get total cash desk payments
  public static async totalCashDeskPayments(
    office?: Types.ObjectId,
    startDate?: Date,
    endDate?: Date
  ) {
    let matchStage: any = {};

    if (office) {
      matchStage.office = office;
    }

    if (startDate && endDate) {
      matchStage.createdAt = {
        $gte: startDate,
        $lte: endDate,
      };
    }

    const result = await CashDeskPaymentRepo.aggregate([
      { $match: matchStage },
      {
        $group: {
          _id: null,
          total: {
            $sum: "$amount",
          },
        },
      },
    ]);

    return result[0]?.total || 0;
  }
}
