import { Types } from "mongoose";
import { OperationType } from "../db/models/Operation";

import cashDeskPaymentService from "./cashDeskPaymentService";
import codeService from "./codeService";
import operationService from "./operationService";

export default class calculationService {
  // get total desposit operations
  public static async totalBalance(
    office?: Types.ObjectId,
    startDate?: Date,
    endDate?: Date
  ) {
    // Get total codes balance
    const totalCodes = await codeService.totalCodesBalance(
      office,
      startDate,
      endDate
    );

    // Get total desposit
    const totalDeposit = await operationService.totalOperations(
      OperationType.DEPOSIT,
      office,
      startDate,
      endDate
    );

    // Get total credit
    const totalCredit = await operationService.totalOperations(
      OperationType.CREDIT,
      office,
      startDate,
      endDate
    );

    // Get total credit
    const totalCashDeskPayments =
      await cashDeskPaymentService.totalCashDeskPayments(
        office,
        startDate,
        endDate
      );

    // Calcul
    const result =
      totalCodes + totalDeposit - totalCredit - totalCashDeskPayments;
    return Math.round(result);
  }

  // get total incomes
  public static async totalIncomes(
    office?: Types.ObjectId,
    startDate?: Date,
    endDate?: Date
  ) {
    // Get total codes balance
    const totalCodes = await codeService.totalCodesBalance(
      office,
      startDate,
      endDate
    );

    // Get total desposit
    const totalDeposit = await operationService.totalOperations(
      OperationType.DEPOSIT,
      office,
      startDate,
      endDate
    );

    // Get total credit
    const totalCredit = await operationService.totalOperations(
      OperationType.CREDIT,
      office,
      startDate,
      endDate
    );

    // Calcul
    const result = totalCodes + totalDeposit - totalCredit;
    return Math.round(result);
  }
}
