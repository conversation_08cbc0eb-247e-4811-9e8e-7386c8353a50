import { Types } from "mongoose";
import { OperationType } from "../db/models/Operation";
import OperationRepo from "../db/repositories/OperationRepo";

export default class operationService {
  public static async totalOperations(
    type: OperationType,
    office?: Types.ObjectId,
    startDate?: Date,
    endDate?: Date
  ) {
    let matchStage: any = { type };

    if (office) {
      matchStage.office = office;
    }

    if (startDate && endDate) {
      matchStage.createdAt = {
        $gte: startDate,
        $lte: endDate,
      };
    }

    const result = await OperationRepo.aggregate([
      { $match: matchStage },
      {
        $group: {
          _id: null,
          total: {
            $sum: "$amount",
          },
        },
      },
    ]);

    return result[0]?.total || 0;
  }
}
