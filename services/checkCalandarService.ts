import { addDays, addHours, endOfDay, startOfToday } from "date-fns";
import { RoleCode, User } from "../db/models/User";
import { Worktime } from "../db/models/Worktime";
import UserRepo from "../db/repositories/UserRepo";

export default class checkCalandarService {
  //UPDATE AUTHORIZATION
  public static async updateAuthorization() {
    const currentDate = new Date();
    const dateRange = [];
    let worktimesNumber = 0;
    const startToday = addHours(startOfToday(), 2).toDateString();

    const usersWithExpiredAuthorizations = await User.find({
      role: RoleCode.ADMIN,
      office: { $ne: null },
      authorizedUntil: { $lte: startToday },
    });

    // Generate dates from current date to current date + 14 days
    for (let i = 0; i < 14; i++) {
      const date = new Date();
      date.setDate(currentDate.getDate() + i);
      date.setHours(0, 0, 0, 0); // Ensure the time part is set to 00:00:00
      dateRange.push(date);
    }

    for (let user of usersWithExpiredAuthorizations) {
      const userId = user?._id;

      for (let date of dateRange) {
        const worktime = await Worktime.findOne({
          userId,
          startDate: { $gte: date, $lte: endOfDay(date) },
        });

        if (worktime) {
          worktimesNumber++;
        }
      }

      if (worktimesNumber >= 12) {
        await UserRepo.update(userId, {
          isAuthorized: true,
          authorizedUntil: addDays(startOfToday(), 14),
        });
      } else {
        await UserRepo.update(userId, {
          isAuthorized: false,
        });
      }
    }
  }
}
