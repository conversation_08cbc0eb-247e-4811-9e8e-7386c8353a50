import { Types } from "mongoose";
import { Todo, TodoStatus } from "../db/models/Todo";

export function extractMentionedUsers(description: string): string[] {
  const mentionRegex = /@(\S+)/g;

  const matches = description.match(mentionRegex);

  if (!matches) {
    return [];
  }

  const mentionedUsers = matches.map((match) => match.slice(1));

  return mentionedUsers;
}

export function combineMetadata(userMeta: any, officeMeta: any) {
  return {
    limit: userMeta.limit + officeMeta.limit,
    hasPrevPage: userMeta.hasPrevPage || officeMeta.hasPrevPage,
    hasNextPage: userMeta.hasNextPage || officeMeta.hasNextPage,
    hasMore: userMeta.hasMore || officeMeta.hasMore,
    totalDocs: userMeta.totalDocs + officeMeta.totalDocs,
    totalPages: undefined,
    page: undefined,
    pagingCounter: undefined,
    prevPage: undefined,
    nextPage: undefined,
  };
}

export default class todoService {
  public static getNumberOFDoneToDo(
    startDate: Date,
    endDate: Date,
    user: Types.ObjectId
  ) {
    return Todo.countDocuments({
      completedBy: user, //  add completed By better dont use mentions or the author
      status: TodoStatus.COMPLETED,
      completedAt: {
        $gte: startDate,
        $lte: endDate,
      },
    });
  }
}
