import { OperationType } from "../db/models/Operation";
import OfficeRepo from "../db/repositories/OfficeRepo";
import { client } from "../utils/redis";
import calculationService from "./calculationService";
import cashDeskPaymentService from "./cashDeskPaymentService";
import codeService from "./codeService";
import operationService from "./operationService";

export default class statisticService {
  public static async balanceAnalyticsPerMonthCron(year: number) {
    const currentYear = new Date().getFullYear();
    const currentMonth = new Date().getMonth();

    const startMonth = year === 2024 ? 3 : 0; // Start from April for 2024, else January
    const endMonth = year === currentYear ? currentMonth : 12; // End with current month for current year, else December

    let yearBalanceAnalytics = [];

    // Array to hold promises
    let promises = [];

    for (let month = startMonth; month < endMonth; month++) {
      const isCurrentMonth = currentMonth === month;

      if (isCurrentMonth) return;

      const startDate = new Date(Date.UTC(year, month, 1));
      const endDate = new Date(Date.UTC(year, month + 1, 0, 23, 59, 59, 999));

      // sort the offices by officeId
      let officesQuery = { sort: "officeId" };
      const officesResponse = await OfficeRepo.findWithPagination(
        {},
        officesQuery
      );
      const offices = officesResponse.docs;

      let monthBalanceAnalytics = [];

      // Create an array of promises for each month
      const monthPromises = offices.map(async (office) => {
        const totalCodes = await codeService.totalCodesBalance(
          office._id,
          startDate,
          endDate
        );
        const totalCredit = await operationService.totalOperations(
          OperationType.CREDIT,
          office._id,
          startDate,
          endDate
        );
        const totalDeposit = await operationService.totalOperations(
          OperationType.DEPOSIT,
          office._id,
          startDate,
          endDate
        );
        const totalCashDesk =
          await cashDeskPaymentService.totalCashDeskPayments(
            office._id,
            startDate,
            endDate
          );

        const totalBalance = await calculationService.totalBalance(
          office._id,
          new Date(null),
          endDate
        );
        return {
          office: office.name,
          totalCodes,
          totalDeposit,
          totalCredit,
          totalCashDesk,
          totalBalance,
        };
      });

      // Add promises for each month to the main promises array
      monthBalanceAnalytics = await Promise.all(monthPromises);

      // Set Redis
      await client.set(
        `balance-per-month-${month}-${year}`,
        JSON.stringify(monthBalanceAnalytics)
      );

      yearBalanceAnalytics.push({
        month: startDate.toLocaleString("default", { month: "long" }),
        data: monthBalanceAnalytics,
      });
    }

    // Wait for all promises to resolve
    await Promise.all(promises);
  }
}
