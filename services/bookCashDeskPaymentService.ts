import { FilterQuery, Types } from "mongoose";
import { BookCashDeskPaymentDocument } from "../db/models/BookCashDeskPayment";
import BookCashDeskPaymentRepo from "../db/repositories/BookCashDeskPaymentRepo";
import { BadRequestError, NotFoundError } from "../helpers/ApiError";
import { RoleCode, UserDocument } from "../db/models/User";

export default class BookCashDeskPaymentService {
  public static async create(
    bookCashDeskPayment: Partial<BookCashDeskPaymentDocument>
  ) {
    const newBookCashDeskPayment = await BookCashDeskPaymentRepo.create(
      bookCashDeskPayment
    );
    return newBookCashDeskPayment;
  }

  public static async findAll(
    bookCashDeskPayment: FilterQuery<BookCashDeskPaymentDocument>
  ) {
    return await BookCashDeskPaymentRepo.find(bookCashDeskPayment);
  }

  public static async findOne(
    booksCashDeskPayment: FilterQuery<BookCashDeskPaymentDocument>
  ): Promise<BookCashDeskPaymentDocument | null> {
    const foundBookCashDeskPayment = await BookCashDeskPaymentRepo.findOne(
      booksCashDeskPayment
    );
    if (!foundBookCashDeskPayment) {
      throw new NotFoundError("Book Cash Desk Payment not found !");
    }
    return foundBookCashDeskPayment;
  }

  public static async update(
    user: Partial<UserDocument>,
    foundBookCashDeskPayment: Partial<BookCashDeskPaymentDocument>,
    bookCashDeskPaymentBody: Partial<BookCashDeskPaymentDocument>
  ) {
    // check fileds to update
    if (
      user?.role !== RoleCode.SUPER_ADMIN &&
      bookCashDeskPaymentBody?.office
    ) {
      throw new BadRequestError("you are not allowed to select an office !");
    }

    // Dont belong to the office
    if (
      user?.role !== RoleCode.SUPER_ADMIN &&
      !user?.office?._id.equals(foundBookCashDeskPayment.office?._id)
    ) {
      throw new BadRequestError(
        "you are not allowed to edit this cash desk payment !"
      );
    }

    // update
    const updatedBookCashDeskPayment =
      await BookCashDeskPaymentRepo.findOneAndUpdate(
        { _id: foundBookCashDeskPayment?._id },
        bookCashDeskPaymentBody
      );
    return updatedBookCashDeskPayment;
  }

  public static async deleteOne(
    bookCashDeskPayment: FilterQuery<BookCashDeskPaymentDocument>
  ) {
    const deletedPayment = await BookCashDeskPaymentRepo.deleteOne(
      bookCashDeskPayment
    );

    if (!deletedPayment) {
      throw new NotFoundError("Book Cash Desk Payment not found !");
    }
    return;
  }
}
