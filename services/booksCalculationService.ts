import { Types } from "mongoose";
import { OperationType } from "../db/models/Operation";

import cashDeskPaymentService from "./cashDeskPaymentService";
import codeService from "./codeService";
import operationService from "./operationService";
import BookCashDeskPaymentRepo from "../db/repositories/BookCashDeskPaymentRepo";
import BookSaleTransactionRepo from "../db/repositories/BookSaleTransactionRepo";

export default class booksCalculationService {
  // get total desposit operations
  public static async totalSales(
    office?: Types.ObjectId,
    startDate?: Date | null,
    endDate?: Date | null
  ) {
    const totalBalance = await BookSaleTransactionRepo.totalAmount({
      office,
    });

    return totalBalance.length > 0 ? totalBalance[0].totalAmount : 0;
  }

  // get total desposit operations
  public static async totalBooksCashDeskPayments(
    office?: Types.ObjectId,
    startDate?: Date | null,
    endDate?: Date | null
  ) {
    const totalPayments = await BookCashDeskPaymentRepo.totalPayments({
      office,
    });

    return totalPayments.length > 0 ? totalPayments[0].totalAmount : 0;
  }

  // get total desposit operations
  public static async validateSufficientFunds(
    office: Types.ObjectId,
    amount: number,
    startDate?: Date,
    endDate?: Date
  ): Promise<boolean> {
    // Get total sales
    const totalSales = await this.totalSales(office, startDate, endDate);

    // Get total books cash desk payments
    const totalBooksCashDeskPayments = await this.totalBooksCashDeskPayments(
      office,
      startDate,
      endDate
    );

    // Calcul
    const result = totalSales - totalBooksCashDeskPayments;

    return result >= amount;
  }
}
