import { FilterQuery, Types } from "mongoose";
import {
  BookSaleDocument,
  BookSaleTransactionDocument,
} from "../db/models/BookSaleTransaction";
import { OfficeDocument } from "../db/models/Office";
import { RoleCode, UserDocument } from "../db/models/User";
import BookSaleTransactionRepo from "../db/repositories/BookSaleTransactionRepo";
import { BadRequestError, NotFoundError } from "../helpers/ApiError";
import BookStockService from "./bookStockService";

export default class BookSaleTransactionService {
  public static async create(
    bookSaleTransaction: Partial<BookSaleTransactionDocument>,
    user: Partial<UserDocument>
  ) {
    let convertedOffice = null;

    // Super Admin check
    if (user?.role === RoleCode.SUPER_ADMIN) {
      if (bookSaleTransaction?.office) {
        convertedOffice = bookSaleTransaction?.office;
      } else {
        throw new BadRequestError("please tell us office !");
      }
    }

    // !Super Admin check
    if (user?.role !== RoleCode.SUPER_ADMIN) {
      if (bookSaleTransaction?.office) {
        throw new BadRequestError("you are not allowed to select an office");
      } else if (user?.office?._id) {
        convertedOffice = user?.office?._id;
      } else {
        throw new BadRequestError("you are not allowed !");
      }
    }

    // Check Stock
    for (const book of bookSaleTransaction.items) {
      const availableBookStock = await BookStockService.findOne(user, {
        book: book.book,
        quantity: { $gte: book.quantitySold },
      });

      if (!availableBookStock) {
        throw new Error(`Not enough stock for books`);
      }
    }

    // Update Stock
    await this.updateStock(bookSaleTransaction.items, convertedOffice);

    const totalAmount = this.calculTotalAmount(bookSaleTransaction.items);

    const newBookSaleTransaction = await BookSaleTransactionRepo.create({
      ...bookSaleTransaction,
      office: convertedOffice,
      createdBy: user?._id,
      totalAmount,
    });

    return newBookSaleTransaction;
  }

  public static async findOne(
    bookSaleTransaction: FilterQuery<BookSaleTransactionDocument>,
    user: Partial<UserDocument>
  ): Promise<BookSaleTransactionDocument | null> {
    let isBelongingToOffice = true;
    const foundBookSaleTransaction = await BookSaleTransactionRepo.findOne(
      bookSaleTransaction
    );

    if (user?.role !== RoleCode.SUPER_ADMIN) {
      isBelongingToOffice =
        user.office &&
        foundBookSaleTransaction.office &&
        user.office.equals(foundBookSaleTransaction.office?._id);
    }

    if (!foundBookSaleTransaction || !isBelongingToOffice) {
      throw new NotFoundError("Book Sale Transaction not found !");
    }
    return foundBookSaleTransaction;
  }

  public static async find(
    query: FilterQuery<BookSaleTransactionDocument>,
    user: Partial<UserDocument>
  ) {
    let updatedQuery = query;

    if (user?.role !== RoleCode.SUPER_ADMIN) {
      updatedQuery = { ...query, office: user.office };
    }

    const bookSaleTransactions = await BookSaleTransactionRepo.find(
      {},
      updatedQuery
    );
    const { docs, ...meta } = bookSaleTransactions;
    return { docs, meta };
  }

  public static async findOneAndUpdate(
    bookSaleTransaction: FilterQuery<BookSaleTransactionDocument>,
    update: Partial<BookSaleTransactionDocument>
  ): Promise<BookSaleTransactionDocument | null> {
    const updatedBookSaleTransaction =
      await BookSaleTransactionRepo.findOneAndUpdate(
        bookSaleTransaction,
        update
      );

    if (!updatedBookSaleTransaction) {
      throw new NotFoundError("Book Sale Transaction not found !");
    }

    return updatedBookSaleTransaction;
  }
  public static async deleteOne(
    bookSaleTransaction: FilterQuery<BookSaleTransactionDocument>
  ) {
    const foundBookSaleTransaction = await BookSaleTransactionRepo.findOne(
      bookSaleTransaction
    );

    if (!foundBookSaleTransaction) {
      throw new NotFoundError("Book Sale Transaction not found !");
    }

    const deletedBookSaleTransaction = await BookSaleTransactionRepo.deleteOne(
      bookSaleTransaction
    );

    if (!deletedBookSaleTransaction) {
      throw new NotFoundError("Book Sale Transaction not found !");
    }
    return;
  }

  public static calculTotalAmount(items: BookSaleDocument[]): number {
    let totalAmount = 0;
    items.forEach((item) => {
      totalAmount += item.quantitySold * item.salePrice;
    });

    return totalAmount;
  }

  public static async updateStock(
    bookSales: BookSaleDocument[],
    office: OfficeDocument | Types.ObjectId
  ) {
    bookSales.forEach(async (book) => {
      const bookToUpdate = await BookStockService.findOneAndUpdate(
        { book: book?.book, office },
        { $inc: { quantity: -book.quantitySold } }
      );

      if (!bookToUpdate) {
        throw new NotFoundError(`Book with id ${book.book} not found.`);
      }
    });
  }
}
