import NotificationRepo from '../db/repositories/NotificationRepo'

export default class notifService {
  public static async send({ currentUser, doc, docModel, concernedUsers }) {
    const message = `${currentUser.username} has mentioned you in a ${docModel}`
    const notifications = concernedUsers.map((user) => ({
      from: currentUser._id,
      to: user._id,
      message,
      doc,
      docModel,
    }))

    await NotificationRepo.createMany(notifications)
  }
}
