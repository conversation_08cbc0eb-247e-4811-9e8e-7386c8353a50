import { startOfDay, startOfToday } from "date-fns";
import { Types } from "mongoose";
import { agentWorkHoursPerDay } from "../config";
import { WorktimeDocument, WorktimeType } from "../db/models/Worktime";
import WorktimeRepo from "../db/repositories/WortimeRepo";

export default class workTimeService {
  public static validateEndDate(value, helpers) {
    const startDate = helpers.state.ancestors[0].startDate;
    const endDate = value;

    const diffInHours =
      (new Date(endDate).getTime() - new Date(startDate).getTime()) /
      (60 * 60 * 1000);
    if (diffInHours > 1) {
      return helpers.error("any.invalid", {
        message: '"endDate" must be one hour or less greater than "startDate"',
      });
    }
    return value;
  }

  public static convertDateStringsToDateObjects(queryObj) {
    const dateFields = ["createdAt", "updatedAt", "startDate", "endDate"];

    dateFields.forEach((field) => {
      if (queryObj[field]) {
        if (typeof queryObj[field] === "string") {
          queryObj[field] = new Date(queryObj[field]);
        } else if (typeof queryObj[field] === "object") {
          for (const [key, value] of Object.entries(queryObj[field])) {
            if (key === "$gte" || key === "$lte") {
              queryObj[field][key] = new Date(value as string);
            }
          }
        }
      }
    });

    // Adjust endDate if present
    if (queryObj.endDate && queryObj.endDate["$lte"]) {
      queryObj.endDate["$lte"] = new Date(
        new Date(queryObj.endDate["$lte"]).setHours(23, 59, 59)
      );
    }

    // Adjust startDate if present
    if (queryObj.startDate && queryObj.startDate["$gte"]) {
      queryObj.startDate["$gte"] = new Date(
        new Date(queryObj.startDate["$gte"]).setHours(0, 0, 0)
      );
    }

    return queryObj;
  }

  public static appendTimestampsToObjects(data, userId) {
    const currentDate = new Date();

    return data.map((obj) => ({
      ...obj,
      userId,
      createdAt: currentDate,
      updatedAt: currentDate,
    }));
  }

  public static hasOverlap(worktimes: WorktimeDocument[]): boolean {
    const sortedWorktimes = worktimes.sort(
      (a, b) =>
        new Date(a.startDate).getTime() - new Date(b.startDate).getTime()
    );

    for (let i = 0; i < sortedWorktimes.length - 1; i++) {
      const currentEnd = new Date(sortedWorktimes[i].endDate).getTime();
      const nextStart = new Date(sortedWorktimes[i + 1].startDate).getTime();

      if (currentEnd > nextStart) {
        return true;
      }
    }

    return false;
  }

  public static youngerThanToday(worktimes: WorktimeDocument[]) {
    const today = startOfToday();
    return worktimes.find((worktime) => startOfDay(worktime.startDate) < today);
  }

  public static async caculatePeriodByWorktimeType(
    type: WorktimeType,
    userId: string,
    startDate: Date,
    endDate: Date
  ) {
    const result = await WorktimeRepo.aggregate([
      {
        $match: {
          userId: new Types.ObjectId(userId),
          type: type,
          startDate: { $gte: startDate },
          endDate: { $lte: endDate },
        },
      },
      {
        $project: {
          duration: { $subtract: ["$endDate", "$startDate"] },
        },
      },
      {
        $group: {
          _id: null,
          totalDuration: { $sum: "$duration" },
        },
      },
    ]);

    if (result.length > 0) {
      const totalDurationMs = result[0].totalDuration;
      return totalDurationMs;
    }

    return 0;
  }

  public static async calculateAgentDaysOfLeave(userId, startDate, endDate) {
    const leaveRecords = await WorktimeRepo.aggregate([
      {
        $match: {
          userId: new Types.ObjectId(userId),
          type: WorktimeType.LEAVE,
          startDate: { $gte: new Date(startDate) },
          endDate: { $lte: new Date(endDate) },
        },
      },
      {
        $project: {
          _id: 0,
          startDate: 1,
          endDate: 1,
          day: {
            $dateToString: { format: "%Y-%m-%d", date: "$startDate" },
          },
          leaveDurationInHours: {
            $divide: [
              { $subtract: ["$endDate", "$startDate"] },
              1000 * 60 * 60,
            ],
          },
        },
      },
      {
        $group: {
          _id: "$day",
          totalLeaveDurationInHours: { $sum: "$leaveDurationInHours" },
        },
      },
      {
        $project: {
          _id: 0,
          day: "$_id",
          leaveDays: {
            $cond: {
              if: {
                $lte: ["$totalLeaveDurationInHours", agentWorkHoursPerDay / 2],
              },
              then: 0.5,
              else: 1,
            },
          },
        },
      },
      {
        $group: {
          _id: null,
          totalLeaveDays: { $sum: "$leaveDays" },
        },
      },
    ]);

    const totalLeaveDays =
      leaveRecords.length > 0 ? leaveRecords[0].totalLeaveDays : 0;

    return totalLeaveDays;
  }
}
