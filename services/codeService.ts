import axios from "axios";
import jwt from "jsonwebtoken";
import { Types } from "mongoose";
import { apiInfo, codeTakiacademyApiUrl } from "../config";
import { CodeDocument, PaymentMethod } from "../db/models/Code";
import CodeRepo from "../db/repositories/CodeRepo";
import OfficeRepo from "../db/repositories/OfficeRepo";
import UserRepo from "../db/repositories/UserRepo";

export default class codeService {
  // Reload codes
  public static async reloadCodes(date?: string) {
    try {
      // generate token
      const jwtToken = await codeService.generateToken();
      // get codes from original database
      let apiUrl = codeTakiacademyApiUrl;

      if (date) {
        apiUrl = `${apiUrl}?createdAt=${date}`;
      }

      const headers = {
        "Content-Type": "application/json",
        "API-KEY": `${jwtToken}`,
      };

      const response = await axios.get(apiUrl, { headers });
      const codes = response.data.payload;

      // check codes (update & insert if necessary)
      await Promise.all(
        codes.map(async (code: any) => {
          let offer = null;
          // get  & affect offer
          if (code.user?.affiliations && code.user?.affiliations.length !== 0) {
            offer = code.user?.affiliations?.slice(
              code.user?.affiliations.length - 1
            )[0].group.name;
          }
          code.offer = offer;

          // format code to code model format
          const formattedCode = codeService.formatCode(code);

          // search code in databse
          let existingCode: any = await CodeRepo.findOne({
            code: code.reference,
          });

          if (existingCode) {
            existingCode = codeService.formatCodeFromResponse(existingCode);
          }

          // update code if exist but not same data
          if (
            existingCode &&
            JSON.stringify(formattedCode) !== JSON.stringify(existingCode)
          ) {
            await this.updateCode(existingCode, formattedCode);
          }

          // insert code if not found
          if (!existingCode) {
            // Insert code
            await this.insertNewCode(formattedCode);
          }
        })
      );
      console.log("Success reloading code");
      return true;
    } catch (error) {
      console.error("Error fetching data:", error);
      return false;
    }
  }

  // Update code for reload
  private static async updateCode(existingCode, formattedCode) {
    let office;
    let admin;

    // Check if office changed
    if (formattedCode?.office?.name !== existingCode?.office?.name) {
      office = await codeService.getOfficeId(formattedCode?.office?.name);
    }

    // Check if admin changed
    if (formattedCode?.admin?.username !== existingCode?.admin?.username) {
      admin = await codeService.getAdminId(formattedCode?.admin?.username);
    }

    let dataToInserted: object = {
      clientName: formattedCode.clientName,
      clientPhoneNumber: formattedCode.clientPhoneNumber,
      clientLevel: formattedCode.clientLevel,
      clientOffer: formattedCode.clientOffer,
      paymentMethod: formattedCode.paymentMethod,
      amount: formattedCode.amount,
      code: formattedCode.code,
      checkNumber: formattedCode.checkNumber,
      checkDate: formattedCode.checkDate,
      notes: formattedCode.notes,
      deleted: formattedCode.deleted,
      deletedAt: formattedCode.deletedAt,
      createdAt: formattedCode.createdAt,
      updatedAt: formattedCode.updatedAt,
      // bill of exchange
      billOfExchangeNum: formattedCode.billOfExchangeNum,
      billOfExchangeName: formattedCode.billOfExchangeName,
      billOfExchangePhone: formattedCode.billOfExchangePhone,
      billOfExchangeDueDate: formattedCode.billOfExchangeDueDate,
      billOfExchangePayed: formattedCode.billOfExchangePayed,
    };

    if (office) {
      dataToInserted = { ...dataToInserted, office };
    }
    if (admin) {
      dataToInserted = { ...dataToInserted, admin };
    }

    // update
    await CodeRepo.findOneAndUpdate(
      { code: existingCode.code },
      dataToInserted
    );
  }

  // Insert new code for reload
  private static async insertNewCode(formattedCode) {
    formattedCode.admin = await codeService.getAdminId(
      formattedCode.admin?.username
    );
    formattedCode.office = await codeService.getOfficeId(
      formattedCode.office?.name
    );
    // Insert code
    await CodeRepo.create(formattedCode as unknown as CodeDocument);
  }

  // Function to encrypt data
  public static async generateToken(): Promise<string> {
    const locauxCredentials: string = apiInfo.key + ":" + apiInfo.secret;

    const payload = {
      iss: locauxCredentials,
      exp: Math.floor(Date.now() / 1000) + 600, // Expires in 1 minute
    };

    return jwt.sign(payload, apiInfo.password, { algorithm: "HS256" });
  }

  public static async getAdminId(username: string) {
    if (!username) return null;
    const admin = await UserRepo.findOne({ username });
    return admin ? admin._id : null;
  }

  public static async getOfficeId(name: string) {
    if (!name) return null;
    const office = await OfficeRepo.findOne({ name });
    return office ? office._id : null;
  }

  public static formatCode(code) {
    return {
      code: code?.reference,
      paymentMethod: code?.type,
      admin: code?.admin_user?.username
        ? { username: code.admin_user.username }
        : null,
      office: code?.office?.name ? { name: code.office.name } : null,
      clientName:
        code?.user?.name && code?.user?.last_name
          ? `${code.user.name} ${code.user.last_name}`
          : null,
      clientPhoneNumber: code?.user?.phone || null,
      clientLevel: code?.division?.name || null,
      clientOffer: code?.offer || null,
      amount: code?.amount,
      checkNumber: code?.check_id?.num_check || null,
      checkDate: code?.check_id?.check_date || null,
      notes: code?.comment || null,
      deleted: code?.deleted_at === null ? false : true,
      deletedAt: code?.deleted_at,
      createdAt: code?.created_at,
      updatedAt: code?.updated_at,
      // bill of exchange
      billOfExchangeNum:
        code?.bill_of_exchange_id?.num_bill_of_exchange || null,
      billOfExchangeName: code?.bill_of_exchange_id?.fullname || null,
      billOfExchangePhone: code?.bill_of_exchange_id?.phone || null,
      billOfExchangeDueDate: code?.bill_of_exchange_id?.due_date || null,
      billOfExchangePayed:
        code?.bill_of_exchange_id?.bill_of_exchange_payed || false,
    };
  }

  public static formatCodeFromResponse(code) {
    return {
      code: code?.code,
      paymentMethod: code?.paymentMethod,
      admin: code?.admin?.username ? { username: code?.admin?.username } : null,
      office: code?.office?.name ? { name: code?.office?.name } : null,
      clientName: code?.clientName,
      clientPhoneNumber: code?.clientPhoneNumber || null,
      clientLevel: code?.clientLevel || null,
      clientOffer: code?.clientOffer || null,
      amount: code?.amount,
      checkNumber: code?.checkNumber || null,
      checkDate: code?.checkDate || null,
      notes: code?.notes || null,
      deleted: code?.deleted === null ? false : true,
      deletedAt: code?.deletedAt,
      createdAt: code?.createdAt,
      updatedAt: code?.updatedAt,
      // bill of exchange
      billOfExchangeNum: code?.billOfExchangeNum || null,
      billOfExchangeName: code?.billOfExchangeName || null,
      billOfExchangePhone: code?.billOfExchangePhone || null,
      billOfExchangeDueDate: code?.billOfExchangeDueDate || null,
      billOfExchangePayed: code?.billOfExchangePayed || false,
    };
  }

  public static async totalCodesBalance(
    office?: Types.ObjectId,
    startDate?: Date,
    endDate?: Date,
    paymentMethod: string = PaymentMethod.CASH,
    isPayed: boolean = true,
    checkPayable?: boolean,
    checkPayed?: boolean
  ) {
    let matchStage: any =
      paymentMethod === PaymentMethod.CHECK
        ? { paymentMethod, checkPayable, checkPayed }
        : { paymentMethod, isPayed };

    if (office) {
      matchStage.office = office;
    }

    if (startDate && endDate) {
      matchStage.$expr = {
        $and: [
          {
            $gte: [{ $ifNull: ["$payedAt", "$createdAt"] }, startDate],
          },
          {
            $lte: [{ $ifNull: ["$payedAt", "$createdAt"] }, endDate],
          },
        ],
      };
    }

    const result = await CodeRepo.aggregate([
      { $match: matchStage },
      {
        $group: {
          _id: null,
          total: {
            $sum: "$amount",
          },
        },
      },
    ]);

    return result[0]?.total || 0;
  }

  public static async checksNumber(
    office?: Types.ObjectId,
    startDate?: Date,
    endDate?: Date,
    checkPayable?: boolean,
    checkPayed?: boolean
  ) {
    let matchStage: any = {
      paymentMethod: PaymentMethod.CHECK,
      checkPayable,
      checkPayed,
    };

    if (office) {
      matchStage.office = office;
    }

    if (startDate && endDate) {
      matchStage.createdAt = {
        $gte: startDate,
        $lte: endDate,
      };
    }

    const result = await CodeRepo.count(matchStage);
    return result;
  }

  public static async billOfExchangeNumber(
    office?: Types.ObjectId,
    startDate?: Date,
    endDate?: Date
  ) {
    let matchStage: any = {
      paymentMethod: PaymentMethod.BILL_OF_EXCHANGE,
    };

    if (office) {
      matchStage.office = office;
    }

    if (startDate && endDate) {
      matchStage.createdAt = {
        $gte: startDate,
        $lte: endDate,
      };
    }

    const result = await CodeRepo.count(matchStage);
    return result;
  }
}
