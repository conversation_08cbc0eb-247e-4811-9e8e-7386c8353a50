import { endOfDay, startOfDay } from "date-fns";
import { FilterQuery, Types } from "mongoose";
import { BookDocument } from "../db/models/Book";
import { BookSaleTransactionDocument } from "../db/models/BookSaleTransaction";
import { BookStockDocument } from "../db/models/BookStock";
import { OfficeDocument } from "../db/models/Office";
import { RoleCode, UserDocument } from "../db/models/User";
import BookRepo from "../db/repositories/BookRepo";
import BookSaleTransactionRepo from "../db/repositories/BookSaleTransactionRepo";
import BookStockRepo from "../db/repositories/BookStockRepo";
import OfficeRepo from "../db/repositories/OfficeRepo";
import { ForbiddenError, NotFoundError } from "../helpers/ApiError";
import { BookCashDeskPaymentDocument } from "../db/models/BookCashDeskPayment";
import BookCashDeskPaymentRepo from "../db/repositories/BookCashDeskPaymentRepo";

export default class BooksAnalyticsService {
  public static async allBooksNumber(
    user: Partial<UserDocument>,
    query: FilterQuery<BookStockDocument>
  ) {
    let filter: FilterQuery<BookStockDocument> = query;

    // Check Role
    if (user?.role === RoleCode.SUPER_ADMIN) {
      if (query.office) {
        filter = { ...filter, office: query.office };
      }
    } else {
      if (!user?.office) {
        throw new NotFoundError("User must have an office !");
      } else {
        filter = { ...filter, office: user?.office?._id };
      }
    }

    const booksNumber = await BookStockRepo.stockSumQuantity(filter);
    return booksNumber;
  }

  public static async booksStockNumberPerBook(
    user: Partial<UserDocument>,
    query: FilterQuery<BookStockDocument>
  ) {
    let filter: FilterQuery<BookStockDocument> = query;
    let office: OfficeDocument = null;

    // Check Role
    if (user?.role === RoleCode.SUPER_ADMIN) {
      if (query.office) {
        filter = { ...filter, office: new Types.ObjectId(query?.office) };
      }
    } else {
      if (!user?.office) {
        throw new NotFoundError("User must have an office !");
      } else {
        filter = { ...filter, office: user?.office?._id };
      }
    }

    if (filter?.office) {
      office = await OfficeRepo.findOne({ _id: filter.office });
      if (!office) {
        throw new NotFoundError("Office not found!");
      }
    }

    const stockPerBook = await BookStockRepo.stockSumQuantityPerBook(filter);
    return office
      ? { stockPerBook, office: office?.name }
      : { stockPerBook, office: "All Offices" };
  }

  public static async calculateRevenuePerOffice(
    user: Partial<UserDocument>,
    filter: FilterQuery<BookSaleTransactionDocument>
  ) {
    // Check Role
    if (user?.role !== RoleCode.SUPER_ADMIN) {
      throw new ForbiddenError("Role is not allowed");
    }

    // Extract properties
    let { startDate, endDate, office } = filter;

    // Filter By Date
    if (startDate && endDate) {
      filter.createdAt = {
        $gte: startOfDay(startDate),
        $lte: endOfDay(endDate),
      };

      delete filter.startDate;
      delete filter.endDate;
    }

    // Filter by Office
    if (office) {
      filter.office = new Types.ObjectId(office);
    }

    const revenue = await BookSaleTransactionRepo.totalAmountPerOffice(filter);

    return revenue;
  }

  public static async calculateCashDeskPaymentsPerOffice(
    user: Partial<UserDocument>,
    filter: FilterQuery<BookCashDeskPaymentDocument>
  ) {
    // Check Role
    if (user?.role !== RoleCode.SUPER_ADMIN) {
      throw new ForbiddenError("Role is not allowed");
    }

    // Extract properties
    let { startDate, endDate, office } = filter;

    // Filter By Date
    if (startDate && endDate) {
      filter.createdAt = {
        $gte: startOfDay(startDate),
        $lte: endOfDay(endDate),
      };

      delete filter.startDate;
      delete filter.endDate;
    }

    // Filter by Office
    if (office) {
      filter.office = new Types.ObjectId(office);
    }

    const totalPayments = await BookCashDeskPaymentRepo.totalPaymentsPerOffice(
      filter
    );

    return totalPayments;
  }

  public static async generalAnalyticsPerOffice(
    user: Partial<UserDocument>,
    filter: FilterQuery<BookSaleTransactionDocument>
  ) {
    const analytics = [];

    const offices = await OfficeRepo.findAll({});

    const totalRevenues = await this.calculateRevenuePerOffice(user, filter);

    const totalPayments = await this.calculateCashDeskPaymentsPerOffice(
      user,
      filter
    );

    for (let office of offices) {
      const revenue = totalRevenues.find(
        (revenue) => revenue.office === office.name
      );

      const payment = totalPayments.find(
        (payment) => payment.office === office.name
      );

      if (revenue) {
        if (payment) {
          analytics.push({
            office: office.name,
            totalRevenues: revenue.totalRevenue,
            totalPayments: payment.totalPayments,
            currentBalance: revenue.totalRevenue - payment.totalPayments,
          });
        } else {
          analytics.push({
            office: office.name,
            totalRevenues: revenue.totalRevenue,
            totalPayments: 0,
            currentBalance: revenue.totalRevenue,
          });
        }
      } else {
        analytics.push({
          office: office.name,
          totalRevenues: 0,
          totalPayments: 0,
          currentBalance: 0,
        });
      }
    }

    return analytics;
  }

  // get total desposit operations
  public static async totalBooksCashDeskPayments(
    user: Partial<UserDocument>,
    filter: FilterQuery<BookCashDeskPaymentDocument>
  ) {
    // Check Role
    if (user?.role === RoleCode.SUPER_ADMIN) {
      if (filter.office) {
        filter = { ...filter, office: new Types.ObjectId(filter?.office) };
      }
    } else {
      if (filter.office) {
        throw new ForbiddenError("Office not allowed for this user !");
      } else if (!user?.office) {
        throw new NotFoundError("User must have an office !");
      } else {
        filter = { ...filter, office: user?.office?._id };
      }
    }

    // Extract properties
    let { startDate, endDate, office } = filter;

    // Filter by Office
    if (office) {
      filter.office = new Types.ObjectId(office);
    }

    // Filter By Date
    if (startDate && endDate) {
      filter.createdAt = {
        $gte: startOfDay(startDate),
        $lte: endOfDay(endDate),
      };

      delete filter.startDate;
      delete filter.endDate;
    }

    const totalPayments = await BookCashDeskPaymentRepo.totalPayments(filter);

    return totalPayments.length > 0 ? totalPayments[0].totalAmount : 0;
  }

  // get total revenue
  public static async totalRevenue(
    user: Partial<UserDocument>,
    filter: FilterQuery<BookSaleTransactionDocument>
  ) {
    // Check Role
    if (user?.role === RoleCode.SUPER_ADMIN) {
      if (filter.office) {
        filter = { ...filter, office: new Types.ObjectId(filter?.office) };
      }
    } else {
      if (filter.office) {
        throw new ForbiddenError("Office not allowed for this user !");
      } else if (!user?.office) {
        throw new NotFoundError("User must have an office !");
      } else {
        filter = { ...filter, office: user?.office?._id };
      }
    }

    // Extract properties
    let { startDate, endDate, office } = filter;

    // Filter by Office
    if (office) {
      filter.office = new Types.ObjectId(office);
    }

    // Filter By Date
    if (startDate && endDate) {
      filter.createdAt = {
        $gte: startOfDay(startDate),
        $lte: endOfDay(endDate),
      };

      delete filter.startDate;
      delete filter.endDate;
    }

    const totalRevenues = await BookSaleTransactionRepo.totalRevenue(filter);

    return totalRevenues.length > 0 ? totalRevenues[0].totalRevenue : 0;
  }

  public static async booksStockNumberPerOfficeAndBook(
    user: Partial<UserDocument>,
    query: FilterQuery<BookStockDocument>
  ) {
    const offices = await OfficeRepo.findAll({});

    let stockPerOfficePerBook: any[] =
      await BookStockRepo.stockSumQuantityPerBookAndOffice(query);

    for (let office of offices) {
      const stock = stockPerOfficePerBook.find(
        (stock: any) => stock.office === office.name
      );

      if (!stock) {
        stockPerOfficePerBook.push({
          office: office.name,
          stock: [],
        });
      }
    }

    return stockPerOfficePerBook;
  }
}
