import { Types } from "mongoose";
import { Call } from "../db/models/Call";

export default class callsService {
  public static async getMaxMinCallsForAllOffice(
    startDate: Date,
    endDate: Date
  ) {
    const resultArray = await Call.aggregate([
      {
        $match: {
          date: {
            $gte: startDate,
            $lte: endDate,
          },
        },
      },
      {
        $group: {
          _id: "$user",
          totalCalls: { $sum: "$totalCalls" },
        },
      },
      {
        $group: {
          _id: null,
          maxTotalCalls: { $max: "$totalCalls" },
          minTotalCalls: { $min: "$totalCalls" },
        },
      },
      {
        $project: {
          _id: 0,
          maxTotalCalls: 1,
          minTotalCalls: 1,
        },
      },
    ]);

    return resultArray.length > 0 ? resultArray[0] : {};
  }

  public static getMaxMinCallsByOffice(
    officeId: Types.ObjectId,
    startDate: Date,
    endDate: Date
  ) {
    return Call.aggregate([
      {
        $match: {
          office: officeId,
          date: { $gte: startDate, $lte: endDate },
        },
      },
      {
        $group: {
          _id: "$user",
          totalCalls: { $sum: "$totalCalls" },
        },
      },
      {
        $group: {
          _id: null,
          maxTotalCalls: { $max: "$totalCalls" },
          minTotalCalls: { $min: "$totalCalls" },
        },
      },
      {
        $project: {
          _id: 0,
          maxTotalCalls: 1,
          minTotalCalls: 1,
        },
      },
    ]).then((result) => result[0]);
  }

  public static getTotalCallsForUser(
    user: Types.ObjectId,
    startDate: Date,
    endDate: Date
  ) {
    return Call.aggregate([
      {
        $match: {
          user,
          date: {
            $gte: startDate,
            $lte: endDate,
          },
        },
      },
      {
        $group: {
          _id: "$user",
          totalCalls: { $sum: "$totalCalls" },
        },
      },
    ]).then((result) => (result.length > 0 ? result[0].totalCalls : null));
  }
}
