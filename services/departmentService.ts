import { FilterQuery } from "mongoose";
import { DepartmentDocument } from "../db/models/Department";
import DepartmentRepo from "../db/repositories/DepartmentRepo";
import { NotFoundError } from "../helpers/ApiError";

export default class DepartmentService {
  public static async create(department: Partial<DepartmentDocument>) {
    const newDepartment = await DepartmentRepo.create(department);
    return newDepartment;
  }

  public static async findOne(department: Partial<DepartmentDocument>) {
    const foundDepartment = await DepartmentRepo.findOne(department);
    if (!foundDepartment) {
      throw new NotFoundError("Department not found !");
    }
    return foundDepartment;
  }

  public static async find(query: any): Promise<any> {
    const departments = await DepartmentRepo.find({}, query);
    const { docs, ...meta } = departments;
    return { docs, meta };
  }

  public static async findOneAndUpdate(
    department: FilterQuery<DepartmentDocument>,
    update: Partial<DepartmentDocument>
  ): Promise<DepartmentDocument | null> {
    const updatedDepartment = await DepartmentRepo.findOneAndUpdate(
      department,
      update
    );

    if (!updatedDepartment) {
      throw new NotFoundError("Department not found !");
    }

    return updatedDepartment;
  }
  public static async deleteOne(department: FilterQuery<DepartmentDocument>) {
    const deletedDepartment = await DepartmentRepo.deleteOne(department);

    if (!deletedDepartment) {
      throw new NotFoundError("Department not found !");
    }
    return;
  }
}
