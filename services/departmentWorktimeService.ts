import { FilterQuery } from "mongoose";
import { DepartmentWorktimeDocument } from "../db/models/DepartmentWorktime";
import DepartmentWorktimeRepo from "../db/repositories/DepartmentWorktimeRepo";
import { BadRequestError, NotFoundError } from "../helpers/ApiError";
import { formatDateToTime, parseTimeToDate } from "../utils/time";

export default class DepartmentWorktimeService {
  public static async create(
    departmentWorktime: Partial<DepartmentWorktimeDocument>
  ) {
    const { startDate, endDate, department } = departmentWorktime;

    const overlappedPeriod = await this.checkOverlappedPeriod(
      new Date(startDate),
      new Date(endDate),
      department as string[]
    );

    if (overlappedPeriod.length > 0) {
      throw new BadRequestError(
        "Cannot create department worktime. Overlapping department worktime periods found."
      );
    }

    const newDepartmentWorktime = await DepartmentWorktimeRepo.create({
      ...departmentWorktime,
    });
    return newDepartmentWorktime;
  }

  public static async findOne(
    departmentWorktime:
      | Partial<DepartmentWorktimeDocument>
      | Record<string, any>
  ) {
    const foundDepartmentWorktime = await DepartmentWorktimeRepo.findOne(
      departmentWorktime
    );
    if (!foundDepartmentWorktime) {
      throw new NotFoundError("Department Worktime not found !");
    }

    return foundDepartmentWorktime;
  }

  public static async find(
    query: FilterQuery<DepartmentWorktimeDocument>
  ): Promise<any> {
    const departmentWorktimes = await DepartmentWorktimeRepo.find({}, query);
    const { docs, ...meta } = departmentWorktimes;
    return { docs, meta };
  }

  public static async findOneAndUpdate(
    departmentWorktime: FilterQuery<DepartmentWorktimeDocument>,
    update: Partial<DepartmentWorktimeDocument>
  ): Promise<DepartmentWorktimeDocument | null> {
    const updatedDepartmentWorktime =
      await DepartmentWorktimeRepo.findOneAndUpdate(departmentWorktime, update);

    if (!updatedDepartmentWorktime) {
      throw new NotFoundError("Department Worktime not found !");
    }

    return updatedDepartmentWorktime;
  }

  public static async deleteOne(
    departmentWorktime: FilterQuery<DepartmentWorktimeDocument>
  ) {
    const foundDepartmentWorktime = await DepartmentWorktimeRepo.findOne(
      departmentWorktime
    );

    if (!foundDepartmentWorktime) {
      throw new NotFoundError("Department Worktime not found !");
    }

    const deletedDepartmentWorktime = await DepartmentWorktimeRepo.deleteOne(
      departmentWorktime
    );

    if (!deletedDepartmentWorktime) {
      throw new NotFoundError("Department Worktime not found !");
    }
    return;
  }

  public static async checkOverlappedPeriod(
    startDate: Date,
    endDate: Date,
    department: string[]
  ) {
    // Validate if there are any overlapping worktime periods
    const overlappedPeriod = await DepartmentWorktimeRepo.simpleFind({
      department: { $in: department },
      $and: [
        {
          startDate: { $lt: new Date(endDate) },
        },
        {
          endDate: { $gt: new Date(startDate) },
        },
      ],
    });

    return overlappedPeriod;
  }
}
