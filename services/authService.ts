import { RoleCode, UserDocument } from "../db/models/User";
import OfficeRepo from "../db/repositories/OfficeRepo";
import UserRepo from "../db/repositories/UserRepo";
import { client } from "../utils/redis";

export default class authService {
  public static async removeAllTokens(users: UserDocument[]) {
    const keys = users.map((user) => `token-${user._id}`);
    await client.del(keys);
  }

  public static async updateUserInfo(
    externalUser: any,
    user: Partial<UserDocument>
  ) {
    const {
      name: externalName,
      username: externalUsername,
      email: externalEmail,
      offices: externalOffices,
      phone: externalPhone,
    } = externalUser;

    const { name, username, email, office, role, phone, _id: userId } = user;

    externalUser = {
      name: externalName,
      username: externalUsername,
      email: externalEmail,
      office: externalOffices,
      phone: externalPhone,
    };

    user = {
      name,
      username,
      email,
      office,
      phone,
    };

    //Check Office
    if (externalUser.office?.length !== 0) {
      let createdOffice;
      const officeFromDb = await OfficeRepo.findOne({
        name: externalUser?.office[0]?.name,
      });

      if (!officeFromDb) {
        createdOffice = await OfficeRepo.create({
          name: externalUser?.office[0]?.name,
          address: externalUser?.office[0]?.address,
          officeId: externalUser?.office[0]?.id,
        });
      }

      externalUser.office = officeFromDb ? officeFromDb : createdOffice;
    } else {
      externalUser.office = null;
    }

    if (JSON.stringify(externalUser) !== JSON.stringify(user)) {
      await UserRepo.update(userId, externalUser);
      return;
    } else {
      return;
    }
  }
}
