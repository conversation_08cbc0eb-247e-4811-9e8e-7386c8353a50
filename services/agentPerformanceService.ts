import { Types } from "mongoose";
import { startMontAgentPerformance } from "../config";
import { RoleCode, UserDocument } from "../db/models/User";
import { WorktimeType } from "../db/models/Worktime";
import AgentPerformanceRepo from "../db/repositories/AgentPerformanceRepo";
import UserRepo from "../db/repositories/UserRepo";
import { BadRequestError, NotFoundError } from "../helpers/ApiError";
import { client } from "../utils/redis";
import callsService from "./callsService";
import dailyResponseService from "./dailyResponseService";
import statClientResponseService from "./statClientResponseService";
import todoService from "./todoService";
import userService from "./userService";
import workTimeService from "./worktimeService";

export default class agentPerformanceService {
  public static async refreshAgentPerformances(year: number, month: number) {
    const startDate = new Date(Date.UTC(year, month, 1));
    const endDate = new Date(Date.UTC(year, month + 1, 0, 23, 59, 59, 999));

    const allUsers = await UserRepo.findAll({});

    allUsers.map(async (user) => {
      const [
        seniority,
        lateness,
        leave,
        authorization,
        calls,
        todos,
        statClientResponses,
        dailyResponses,
      ] = await Promise.all([
        userService.calculateSeniority(user, startDate),
        userService.getUserLateness(user._id, startDate, endDate),
        workTimeService.calculateAgentDaysOfLeave(user._id, startDate, endDate),
        workTimeService.caculatePeriodByWorktimeType(
          WorktimeType.AUTHORIZATION,
          user._id,
          startDate,
          endDate
        ),
        callsService.getTotalCallsForUser(user._id, startDate, endDate),
        todoService.getNumberOFDoneToDo(startDate, endDate, user._id),
        statClientResponseService.getNumberStatClientResponseByUser(
          startDate,
          endDate,
          user._id
        ),
        dailyResponseService.getNumberDailyResponseByUser(
          startDate,
          endDate,
          user._id
        ),
      ]);
      const performanceData = {
        seniority,
        lateness,
        leave,
        authorization,
        calls,
        todos,
        statClientResponses,
        dailyResponses,
      };

      await client.set(
        `agent-performances-${user?._id.toString()}-${month}-${year}`,
        JSON.stringify({
          user,
          date: startDate,
          ...performanceData,
        })
      );

      const foundAgentPerformance = await AgentPerformanceRepo.findOne({
        user: user._id,
        date: startDate,
      });
      if (foundAgentPerformance) {
        const hasChanges = [
          "seniority",
          "lateness",
          "leave",
          "authorization",
          "calls",
          "todos",
          "statClientResponses",
          "dailyResponses",
        ].some(
          (field) => foundAgentPerformance[field] !== performanceData[field]
        );

        if (hasChanges) {
          await AgentPerformanceRepo.updateOne(
            { user: user._id, date: startDate },
            performanceData
          );
        }
      }

      return {
        user,
        date: startDate,
        ...performanceData,
      };
    });
  }

  public static calculateAgentBonusAmount(
    bonusAmount: number,
    bonusAmountPct: number
  ) {
    return (bonusAmount * bonusAmountPct) / 100;
  }

  public static async getAllAgentPerfomancePerMonth(
    month: number,
    year: number
  ) {
    const currentYear = new Date().getFullYear();
    const agentPerformancesRedis = [];

    let startMonth;

    if (year > currentYear) {
      throw new BadRequestError(
        "Invalid year, The year cannot be greater than the current year"
      );
    }

    if (!month) {
      startMonth = year === 2024 ? startMontAgentPerformance : 0; // Start from April for 2024, else January
    } else {
      startMonth = month;
    }

    const startDate = new Date(Date.UTC(year, startMonth, 1));
    const endDate = new Date(
      Date.UTC(year, startMonth + 1, 0, 23, 59, 59, 999)
    );

    // agents performances from db
    const agentPerformancesDb = await AgentPerformanceRepo.findAll({
      date: startDate,
    });

    // users not stored in db
    const allUsers = await UserRepo.findAll({});

    const usersWithPerformance = agentPerformancesDb.map((ap) =>
      ap.user._id.toString()
    );

    const usersWithoutPerformanceDb = allUsers.filter(
      (user) => !usersWithPerformance.includes(user._id.toString())
    );

    // agents performances from redis
    for (const user of usersWithoutPerformanceDb) {
      const agentPerformance = await client.get(
        `agent-performances-${user?._id.toString()}-${month}-${year}`
      );

      if (agentPerformance) {
        agentPerformancesRedis.push(JSON.parse(agentPerformance));
      }
    }

    // agents performances real time calculation
    const redisUserIds = agentPerformancesRedis.map((performance) =>
      performance.user._id.toString()
    );

    const usersWithoutPerformanceRedis = usersWithoutPerformanceDb.filter(
      (user) => !redisUserIds.includes(user._id.toString())
    );

    let calculatedPerformances = [];
    if (usersWithoutPerformanceRedis.length > 0) {
      calculatedPerformances = await Promise.all(
        usersWithoutPerformanceRedis.map(async (user) => {
          const [
            seniority,
            lateness,
            leave,
            authorization,
            calls,
            todos,
            statClientResponses,
          ] = await Promise.all([
            userService.calculateSeniority(user, startDate),
            userService.getUserLateness(user._id, startDate, endDate),
            workTimeService.calculateAgentDaysOfLeave(
              user._id,
              startDate,
              endDate
            ),
            workTimeService.caculatePeriodByWorktimeType(
              WorktimeType.AUTHORIZATION,
              user._id,
              startDate,
              endDate
            ),
            callsService.getTotalCallsForUser(
              new Types.ObjectId(user?._id),
              startDate,
              endDate
            ),
            todoService.getNumberOFDoneToDo(
              startDate,
              endDate,
              new Types.ObjectId(user._id)
            ),
            statClientResponseService.getNumberStatClientResponseByUser(
              startDate,
              endDate,
              user._id
            ),
          ]);

          await client.set(
            `agent-performances-${user?._id.toString()}-${month}-${year}`,
            JSON.stringify({
              user,
              date: startDate,
              seniority,
              lateness,
              leave,
              authorization,
              calls,
              todos,
              statClientResponses,
            })
          );

          return {
            user,
            date: startDate,
            seniority,
            lateness,
            leave,
            authorization,
            calls,
            todos,
            statClientResponses,
          };
        })
      );
    }

    const allPerformances = [
      ...agentPerformancesDb,
      ...agentPerformancesRedis,
      ...calculatedPerformances,
    ];
    const minMaxOffices = await callsService.getMaxMinCallsForAllOffice(
      startDate,
      endDate
    );
    return { minMaxOffices, allPerformances };
  }

  public static async getPerformanceByUser(
    user: UserDocument,
    year: number,
    role: RoleCode,
    requestedUserId?: string
  ) {
    const currentYear = new Date().getFullYear();
    const currentMonth = new Date().getMonth();
    let agentPerformances = [];

    // Check if a specific user is requested and has permission
    if (requestedUserId) {
      if (role !== RoleCode.SUPER_ADMIN) {
        throw new BadRequestError("You don't have permission to select user");
      } else {
        user = await UserRepo.findOne({ _id: requestedUserId });
        if (!user) throw new NotFoundError("User not found!");
      }
    }

    // Validate the year
    if (year > currentYear) {
      throw new BadRequestError(
        "Invalid year, The year cannot be greater than the current year"
      );
    }

    const startMonth = year === 2024 ? 3 : 0; // Start from April for 2024, else January
    const endMonth = year === currentYear ? currentMonth + 1 : 12; // End with current month for current year, else December

    // Loop through each month and get or calculate the performance
    for (let month = startMonth; month < endMonth; month++) {
      const startDate = new Date(Date.UTC(year, month, 1));
      const endDate = new Date(Date.UTC(year, month + 1, 0, 23, 59, 59, 999));

      const foundAgentPerformance = await AgentPerformanceRepo.findOne({
        date: startDate,
        user: user._id,
      });

      if (!foundAgentPerformance) {
        // Calculate the agent performance
        const [
          seniority,
          lateness,
          leave,
          authorization,
          calls,
          todos,
          statClientResponses,
          dailyResponses,
        ] = await Promise.all([
          userService.calculateSeniority(user, startDate),
          userService.getUserLateness(user._id, startDate, endDate),
          workTimeService.calculateAgentDaysOfLeave(
            user._id,
            startDate,
            endDate
          ),
          workTimeService.caculatePeriodByWorktimeType(
            WorktimeType.AUTHORIZATION,
            user._id,
            startDate,
            endDate
          ),
          callsService.getTotalCallsForUser(
            new Types.ObjectId(user._id),
            startDate,
            endDate
          ),
          todoService.getNumberOFDoneToDo(startDate, endDate, user._id),
          statClientResponseService.getNumberStatClientResponseByUser(
            startDate,
            endDate,
            user._id
          ),
          dailyResponseService.getNumberDailyResponseByUser(
            startDate,
            endDate,
            user._id
          ),
        ]);

        agentPerformances.push({
          month: startDate.toLocaleString("default", { month: "long" }),
          data: {
            seniority,
            lateness,
            leave,
            authorization,
            calls,
            todos,
            statClientResponses,
            dailyResponses,
            date: startDate,
          },
        });
      } else {
        agentPerformances.push({
          month: startDate.toLocaleString("default", { month: "long" }),
          data: foundAgentPerformance,
        });
      }
    }

    return agentPerformances;
  }
}
