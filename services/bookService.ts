import { FilterQuery } from "mongoose";
import { BookDocument } from "../db/models/Book";
import BookRepo from "../db/repositories/BookRepo";
import { NotFoundError } from "../helpers/ApiError";

export default class BookService {
  public static async create(book: Partial<BookDocument>) {
    const newBook = await BookRepo.create(book);
    return newBook;
  }

  public static async findOne(
    book: FilterQuery<BookDocument>
  ): Promise<BookDocument | null> {
    const foundBook = await BookRepo.findOne(book);
    if (!foundBook) {
      throw new NotFoundError("Book not found !");
    }
    return foundBook;
  }

  public static async find(query: any): Promise<any> {
    const books = await BookRepo.find({}, query);
    const { docs, ...meta } = books;
    return { docs, meta };
  }

  public static async findOneAndUpdate(
    book: FilterQuery<BookDocument>,
    update: Partial<BookDocument>
  ): Promise<BookDocument | null> {
    const updatedBook = await BookRepo.findOneAndUpdate(book, update);

    if (!updatedBook) {
      throw new NotFoundError("Book not found !");
    }

    return updatedBook;
  }
  public static async deleteOne(book: FilterQuery<BookDocument>) {
    const deletedBook = await BookRepo.deleteOne(book);

    if (!deletedBook) {
      throw new NotFoundError("Book not found !");
    }
    return;
  }
}
