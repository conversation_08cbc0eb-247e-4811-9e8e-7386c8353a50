import { addDays, format, startOfToday } from "date-fns";
import { detect } from "detect-browser";
import { Request } from "express";
import { ObjectId, PipelineStage, Types } from "mongoose";
import { EventType, SessionDocument, SessionNotes } from "../db/models/Session";
import SessionRepo from "../db/repositories/SessionRepo";

export default class sessionService {
  public static async create(
    req: Request,
    userId: ObjectId,
    eventType: EventType,
    notes?: SessionNotes
  ) {
    let session: Partial<SessionDocument> = {
      user: userId,
      eventType,
      ...(notes && { notes }),
    };

    // Browser Info
    const browser = detect(req.headers["user-agent"]);

    if (browser) {
      session.browser = {
        name: browser?.name,
        version: browser?.version,
        os: browser?.os,
      };
    }

    // IP Address
    if (req.body.ipAddress) {
      session.ipAddress = req.body.ipAddress;
    }

    // save session
    await SessionRepo.create(session);
  }

  public static async getAdminsWithActiveSessions() {
    const today = startOfToday();
    const tomorrow = addDays(today, 1);

    const adminsWithActiveSessions = await SessionRepo.aggregate([
      {
        $match: {
          createdAt: { $gte: today, $lt: tomorrow },
        },
      },
      {
        $group: {
          _id: "$user",
          latestSession: { $last: "$$ROOT" },
        },
      },
      {
        $match: {
          "latestSession.eventType": EventType.LOGIN,
        },
      },
      {
        $lookup: {
          from: "users",
          localField: "_id",
          foreignField: "_id",
          as: "user",
        },
      },
      {
        $unwind: "$user",
      },
      {
        $match: {
          "user.role": "ADMIN",
        },
      },
      {
        $project: {
          _id: "$user._id",
        },
      },
    ]);

    return adminsWithActiveSessions;
  }

  public static generatePipelineToGetUserFirstLoginLastLogout(
    req: Request
  ): PipelineStage[] {
    const { startDate, endDate, user, ipAddress, eventType, sort } = req.query;
    // handle range date
    const matchConditions: any[] = [
      {
        $gte: [
          { $dateToString: { format: "%Y-%m-%d", date: "$createdAt" } },
          startDate || "1970-01-01",
        ],
      },
      {
        $lte: [
          { $dateToString: { format: "%Y-%m-%d", date: "$createdAt" } },
          endDate || new Date(),
        ],
      },
    ];
    // handle filter
    if (user) {
      matchConditions.push({
        $eq: ["$user", new Types.ObjectId(user as string)],
      });
    }

    if (ipAddress) {
      matchConditions.push({ $eq: ["$ipAddress", ipAddress] });
    }

    if (eventType) {
      matchConditions.push({ $eq: ["$eventType", eventType] });
    }
    const matchStage: PipelineStage.Match = {
      $match: {
        $expr: {
          $and: matchConditions,
        },
      },
    };

    // handle sort
    const sortStage: PipelineStage.Sort = {
      $sort: {},
    };

    if (sort) {
      const field = (sort as string).replace("-", "");
      const order = (sort as string).startsWith("-") ? -1 : 1;
      if (field === "createdAt") {
        sortStage.$sort["date"] = order;
      } else {
        sortStage.$sort[field] = order;
      }
    } else {
      sortStage.$sort = { date: -1 };
    }

    // pipeline
    const pipeline = [
      matchStage,
      {
        $project: {
          user: 1,
          eventType: 1,
          createdAt: 1,
          ipAddress: 1,
          browser: 1,
          notes: 1,
          date: { $dateToString: { format: "%Y-%m-%d", date: "$createdAt" } },
        },
      },
      {
        $group: {
          _id: { user: "$user", date: "$date" },
          events: { $push: "$$ROOT" },
        },
      },
      {
        $project: {
          user: "$_id.user",
          date: "$_id.date",
          events: 1,
          firstLogin: {
            $arrayElemAt: [
              {
                $filter: {
                  input: {
                    $sortArray: { input: "$events", sortBy: { createdAt: 1 } },
                  },
                  as: "event",
                  cond: { $eq: ["$$event.eventType", EventType.LOGIN] },
                },
              },
              0,
            ],
          },
          lastLogout: {
            $arrayElemAt: [
              {
                $filter: {
                  input: {
                    $sortArray: {
                      input: "$events",
                      sortBy: { createdAt: -1 },
                    },
                  },
                  as: "event",
                  cond: { $eq: ["$$event.eventType", EventType.LOGOUT] },
                },
              },
              0,
            ],
          },
        },
      },
      sortStage,
      {
        $lookup: {
          from: "users",
          localField: "user",
          foreignField: "_id",
          as: "userDetails",
        },
      },
      {
        $unwind: "$userDetails",
      },
      {
        $project: {
          _id: 0,
          user: "$userDetails",
          date: 1,
          firstLogin: { $ifNull: ["$firstLogin", null] },
          lastLogout: { $ifNull: ["$lastLogout", null] },
        },
      },
    ];

    return pipeline;
  }

  public static getCustomData(data) {
    const customData = data.flatMap((el) => {
      const { firstLogin, lastLogout, user, ...rest } = el;

      // Ensure user is UserDocument type and not ObjectId
      const userDocument =
        typeof user === "object" && "username" in user && "name" in user
          ? user
          : null;

      const transformedUser = {
        agent: userDocument?.name || userDocument?.username,
      };

      const loginData = firstLogin
        ? {
            ...transformedUser,
            eventType: firstLogin.eventType,
            date: firstLogin.date,
            browserName: firstLogin.browser?.name || null,
            browserOS: firstLogin.browser?.os || null,
            ipAddress: firstLogin.ipAddress || null,
            createdAt:
              firstLogin?.createdAt &&
              format(new Date(firstLogin?.createdAt), "dd/MM/yyyy p"),
            ...rest,
          }
        : null;

      const logoutData = lastLogout
        ? {
            ...transformedUser,
            eventType: lastLogout.eventType,
            date: lastLogout.date,
            browserName: lastLogout.browser?.name || null,
            browserOS: lastLogout.browser?.os || null,
            ipAddress: lastLogout.ipAddress || null,
            createdAt:
              lastLogout?.createdAt &&
              format(new Date(lastLogout?.createdAt), "dd/MM/yyyy p"),
            ...rest,
          }
        : null;

      return [loginData, logoutData].filter((event) => event !== null);
    });
    return customData;
  }
}
