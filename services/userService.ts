import { CookieSerializeOptions } from "cookie";
import { differenceInMilliseconds } from "date-fns";
import md5 from "md5";
import { Types } from "mongoose";
import { UserDocument } from "../db/models/User";
import { WorktimeType } from "../db/models/Worktime";
import SessionRepo from "../db/repositories/SessionRepo";
import UserRepo from "../db/repositories/UserRepo";

export default class userService {
  public static async createDeviceIdCookie() {
    const deviceId = md5(`${Math.random()}${Date.now()}`);

    const cookieOptions: CookieSerializeOptions = {
      expires: new Date(Date.now() + 2 * 365 * 24 * 60 * 60 * 1000), // 2 years expiration
      path: "/",
      domain: null,
      secure: false,
      httpOnly: false,
    };

    return { deviceId, cookieOptions };
  }

  public static calculateSeniority(user: UserDocument, date: Date) {
    if (!user.firstDayWork) {
      return 0;
    }

    const refDate = new Date(date);
    const firstDayWork = new Date(user?.firstDayWork);
    const today = new Date();

    if (
      refDate?.getFullYear() === today.getFullYear() &&
      refDate?.getMonth() === today.getMonth()
    ) {
      // If it's the current month
      const seniority = today.getTime() - firstDayWork.getTime();
      return seniority;
    } else {
      // If it's a preceding month
      const endOfMonth = new Date(
        Date.UTC(
          refDate.getFullYear(),
          refDate.getMonth() + 1,
          0,
          23,
          59,
          59,
          999
        )
      );

      const seniority = endOfMonth.getTime() - firstDayWork.getTime();

      return seniority < 0 ? 0 : seniority;
    }
  }

  public static async getUserLateness(
    userId: string,
    startDate: Date,
    endDate: Date
  ) {
    const totalLatenessPipeline = [
      {
        $match: {
          user: new Types.ObjectId(userId),
          eventType: "login",
          createdAt: { $gte: startDate, $lte: endDate },
        },
      },
      {
        $group: {
          _id: { $dateToString: { format: "%Y-%m-%d", date: "$createdAt" } },
          firstLogin: { $min: "$createdAt" },
        },
      },
      {
        $lookup: {
          from: "worktimes", // Adjust to match your Worktime collection name
          let: { day: "$_id" },
          pipeline: [
            {
              $match: {
                userId: new Types.ObjectId(userId),
                type: WorktimeType.WORK,
                deleted: false,
                startDate: { $gte: startDate, $lte: endDate },
                $expr: {
                  $eq: [
                    {
                      $dateToString: {
                        format: "%Y-%m-%d",
                        date: "$startDate",
                      },
                    },
                    "$$day",
                  ],
                },
              },
            },
            {
              $group: {
                _id: "$$day",
                firstWorktime: { $min: "$startDate" },
              },
            },
          ],
          as: "worktimes",
        },
      },
      {
        $unwind: { path: "$worktimes", preserveNullAndEmptyArrays: true },
      },
      {
        $project: {
          _id: 0,
          day: "$_id",
          totalLateness: {
            $cond: {
              if: {
                $and: [
                  "$firstLogin",
                  "$worktimes.firstWorktime",
                  { $gt: ["$firstLogin", "$worktimes.firstWorktime"] },
                ],
              },
              then: { $subtract: ["$worktimes.firstWorktime", "$firstLogin"] },
              else: 0,
            },
          },
        },
      },
      {
        $group: {
          _id: null,
          totalLateness: { $sum: "$totalLateness" },
        },
      },
    ];

    const result = await SessionRepo.aggregate(totalLatenessPipeline);
    const lateness = result.length > 0 ? result[0].totalLateness : 0;
    return lateness < 0 ? lateness : 0;
  }
}
