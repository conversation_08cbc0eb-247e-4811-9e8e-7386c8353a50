import { endOfDay, startOfDay } from "date-fns";
import { Types } from "mongoose";
import StatClientResponse from "../db/models/StatClientResponse";

export default class statClientResponseService {
  public static handleQuery(reqQuery: any) {
    const kpis: { kpi: string; response: [] }[] = reqQuery.kpis as {
      kpi: string;
      response: [];
    }[];
    const {
      clientName,
      clientContact,
      office,
      statClient,
      admin,
      startDate,
      endDate,
    } = reqQuery;
    const query: any = {};

    if (kpis && kpis.length > 0) {
      const kpiFilters = kpis.map(({ kpi, response }) => {
        // Handle boolean values
        const processedResponse = response.map((value) =>
          value === "true" ? true : value === "false" ? false : value
        );

        return {
          kpis: {
            $elemMatch: {
              kpi: kpi,
              response: { $all: processedResponse },
            },
          },
        };
      });

      query["$and"] = kpiFilters;
    }

    if (clientContact) {
      query["clientContact"] = clientContact;
    }

    if (clientName) {
      query["clientName"] = clientName;
    }

    if (office) {
      query["office"] = office;
    }

    if (statClient) {
      query["statClient"] = statClient;
    }

    if (admin) {
      query["admin"] = { $in: admin };
    }

    if (startDate && endDate) {
      const convertedStartDate = startOfDay(startDate);
      const convertedEndDate = endOfDay(endDate);
      query["createdAt"] = { $gte: convertedStartDate, $lte: convertedEndDate };
    }
    return query;
  }
  public static getNumberStatClientResponseByUser(
    startDate: Date,
    endDate: Date,
    user: Types.ObjectId
  ) {
    return StatClientResponse.countDocuments({
      admin: {
        $in: [user],
      },
      createdAt: {
        $gte: startDate,
        $lte: endDate,
      },
    });
  }
}
