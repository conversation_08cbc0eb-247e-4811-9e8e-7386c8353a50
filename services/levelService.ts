import { FilterQuery } from "mongoose";
import { Level, LevelDocument } from "../db/models/Level";
import LevelRepo from "../db/repositories/LevelRepo";
import { BadRequestError, NotFoundError } from "../helpers/ApiError";

export default class LevelService {
  public static async create(level: Partial<LevelDocument>) {
    // Unique name
    const existLevel = await LevelRepo.findOne({ name: level.name });

    if (existLevel) {
      throw new BadRequestError(`Level name "${level.name}" already exists!`);
    }

    const newLevel = await LevelRepo.create(level);
    return newLevel;
  }

  public static async findOne(
    level: FilterQuery<LevelDocument>
  ): Promise<LevelDocument | null> {
    const foundLevel = await LevelRepo.findOne(level);
    if (!foundLevel) {
      throw new NotFoundError("Level not found !");
    }
    return foundLevel;
  }

  public static async find(query: any): Promise<any> {
    const levels = await LevelRepo.find({}, query);
    const { docs, ...meta } = levels;
    return { docs, meta };
  }

  public static async findOneAndUpdate(
    level: FilterQuery<LevelDocument>,
    update: Partial<LevelDocument>
  ): Promise<LevelDocument | null> {
    const updatedLevel = await LevelRepo.findOneAndUpdate(level, update);

    if (!updatedLevel) {
      throw new NotFoundError("Level not found !");
    }

    return updatedLevel;
  }
  public static async deleteOne(level: FilterQuery<LevelDocument>) {
    const deletedLevel = await Level.deleteOne(level);

    if (!deletedLevel) {
      throw new NotFoundError("Level not found !");
    }
    return;
  }
}
