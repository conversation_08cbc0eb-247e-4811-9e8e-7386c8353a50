import { Types } from "mongoose";
import { UserDocument, User } from "../models/User";

export default class AuthRepo {
  //Find One Login
  public static async findOneLogin(obj: object): Promise<UserDocument | null> {
    return (await User.findOne(obj).select("+password")).populate({
      path: "permissionGroup",
      populate: {
        path: "permissions",
      },
    });
  }

  //Find By Id
  public static async findById(
    id: Types.ObjectId
  ): Promise<UserDocument | null> {
    return await User.findById(id, {
      deletedAt: 0,
      updatedAt: 0,
    })
      .populate("extraPermissions")
      .populate({
        path: "permissionGroup",
        populate: {
          path: "permissions",
        },
      });
  }

  public static async findOneByToken(
    token: string
  ): Promise<UserDocument | null> {
    return await User.findOne({ OAuthToken: token })
      .select("-OAuthToken")
      .populate("extraPermissions")
      .populate("office")
      .populate({
        path: "permissionGroup",
        populate: {
          path: "permissions",
        },
      });
  }
}
