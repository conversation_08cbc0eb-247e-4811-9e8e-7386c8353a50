import APIFeatures from "../../helpers/ApiFeatures";
import { User, UserDocument } from "../models/User";

export default class UserRepo {
  //Find One
  public static async findOne(obj: object): Promise<UserDocument | null> {
    return await User.findOne(obj)
      .populate("office")
      .populate({
        path: "permissionGroup",
        populate: {
          path: "permissions",
        },
      })
      .populate("extraPermissions")
      .populate("department");
  }

  //Find + Pagination
  public static async findWithPagination(obj: object, query: any) {
    const features = new APIFeatures(
      User.find(obj).populate("office").populate("department"),
      query
    )
      .filter()
      .search(["username", "name", "email"])
      .sort()
      .limitFields();

    const options = {
      query: features.query,
      limit: query.limit,
      page: query.page,
    };

    return await User.paginate(options);
  }

  //Find + Filter - pagination
  public static async find(query: any, filter: object) {
    const features = new APIFeatures(
      User.find(filter).populate([
        { path: "office", select: "name" },
        {
          path: "permissionGroup",
          populate: {
            path: "permissions",
          },
        },
        {
          path: "extraPermissions",
          select: "model method",
        },
      ]),
      query
    )
      .filter()
      .search(["username", "name", "email"])
      .sort()
      .limitFields();

    return await User.find(features.query);
  }

  public static async findAll(filter: object) {
    return await User.find(filter);
  }
  public static async findAndselect(filter: object) {
    return await User.find(filter).select("_id name email username office");
  }

  //Create
  public static async create(user: Object): Promise<UserDocument | null> {
    return await (
      await User.create(user)
    ).populate({
      path: "permissionGroup",
      populate: {
        path: "permissions",
      },
    });
  }

  //Count
  public static async count(obj: object): Promise<Number | null> {
    return await User.count(obj);
  }

  // UPDATE
  public static async update(
    userId: string,
    update: object
  ): Promise<UserDocument | null> {
    return await User.findByIdAndUpdate(userId, update, { new: true })
      .populate("office")
      .populate({
        path: "permissionGroup",
        populate: {
          path: "permissions",
        },
      })
      .populate("extraPermissions");
  }
}
