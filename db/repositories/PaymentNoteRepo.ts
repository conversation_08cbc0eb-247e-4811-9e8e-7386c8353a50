import APIFeatures from "../../helpers/ApiFeatures";
import { PaymentNote, PaymentNoteDocument } from "../models/PaymentNote";

export default class PaymentNoteRepo {
  public static async create(
    paymentNote: object
  ): Promise<PaymentNoteDocument> {
    return await PaymentNote.create(paymentNote);
  }

  //Find + Pagination
  public static async findWithPagination(obj: Object, query: any) {
    const features = new APIFeatures(PaymentNote.find(obj), query)
      .filter()
      .sort()
      .limitFields();

    const options = {
      query: features.query,
      limit: query.limit || 10,
      page: query.page || 1,
    };
    return await PaymentNote.paginate(options);
  }

  //Find One
  public static async findOne(
    obj: object
  ): Promise<PaymentNoteDocument | null> {
    return await PaymentNote.findOne(obj);
  }

  // Update one
  public static async updateOne(
    filter: Object,
    update: object
  ): Promise<PaymentNoteDocument | null> {
    return await PaymentNote.findByIdAndUpdate(filter, update, { new: true });
  }

  // Soft delete
  public static async softDelete(
    filter: Object
  ): Promise<PaymentNoteDocument | null> {
    const note = await PaymentNote.findOne(filter);

    if (!note) return null;

    const updatedContent = `${
      note.content
    } - deleted at ${new Date().toISOString()}`;

    return await PaymentNote.findByIdAndUpdate(
      filter,
      {
        deleted: true,
        deletedAt: new Date(),
        content: updatedContent,
      },
      { new: true } // Return the updated document
    );
  }

  // Soft delete many
  public static async softDeleteMany(filter: Object): Promise<any> {
    const updatedContentDate = new Date().toISOString();

    return await PaymentNote.updateMany(filter, [
      {
        $set: {
          deleted: true,
          deletedAt: new Date(),
          content: {
            $concat: ["$content", ` - deleted at ${updatedContentDate}`],
          },
        },
      },
    ]);
  }
}
