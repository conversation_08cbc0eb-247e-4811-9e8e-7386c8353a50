import APIFeatures from "../../helpers/ApiFeatures";
import {
  AgentPerformance,
  AgentPerformanceDocument,
} from "../models/AgentPerformance";

export default class AgentPerformanceRepo {
  public static async findOne(
    obj: object
  ): Promise<AgentPerformanceDocument | null> {
    return await AgentPerformance.findOne(obj).populate("user");
  }

  public static async findAll(filter: object) {
    return await AgentPerformance.find(filter).populate("user");
  }

  // List
  public static async find(query: object, filter: object) {
    const features = new APIFeatures(AgentPerformance.find(filter), query)
      .filter()
      .search()
      .sort();
    const agentPerformances = await AgentPerformance.find(
      features.query
    ).populate("user");
    return agentPerformances;
  }

  //Find + pagination
  public static async findWithPagination(obj: Object, query: any = {}) {
    const features = new APIFeatures(
      AgentPerformance.find(obj).populate("user"),
      query
    )
      .filter()
      .sort()
      .limitFields();

    const options = {
      query: features.query,
      limit: query.limit,
      page: query.page,
    };
    return await AgentPerformance.paginate(options);
  }

  public static async create(obj: object) {
    const agentPerformance = await AgentPerformance.create(obj);
    return await agentPerformance.populate("user");
  }

  public static async updateOne(filter: object, update: object) {
    return await AgentPerformance.findOneAndUpdate(filter, update, {
      new: true,
    }).populate("user");
  }
}
