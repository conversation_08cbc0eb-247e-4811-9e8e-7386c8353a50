import { Types } from "mongoose";
import APIFeatures from "../../helpers/ApiFeatures";
import { Note, NoteDocument } from "../models/Note";

export default class NoteRepo {
  public static async create(note: object): Promise<NoteDocument> {
    return await (await Note.create(note)).populate("files");
  }

  public static async find(obj: object, query: any) {
    const features = new APIFeatures(Note.find(obj), query)
      .filter()
      .sort()
      .limitFields();

    const options = {
      query: features.query,
      limit: query.limit,
      page: query.page,
      populate: {
        path: "createdBy",
        select: { name: 1 },
      },
    };
    return Note.paginate(options);
  }

  public static async findOne(obj: object): Promise<NoteDocument | null> {
    return await Note.findOne(obj).populate([
      {
        path: "createdBy",
        select: { username: 1 },
      },
      {
        path: "files",
      },
    ]);
  }

  public static async findByIdAndUpdate(
    tagsId: Types.ObjectId,
    note: NoteDocument
  ): Promise<NoteDocument | null> {
    return await Note.findByIdAndUpdate(tagsId, note, { new: true });
  }

  public static async findOneAndUpdate(filter: object, note: NoteDocument) {
    return await Note.findOneAndUpdate(filter, note, { new: true });
  }

  public static async deleteOne(filter: object) {
    return await Note.findOneAndUpdate(filter, [
      {
        $set: {
          deletedAt: new Date(),
          deleted: true,
          title: {
            $concat: [
              {
                $dateToString: {
                  format: "%Y-%m-%d_%H-%M-%S", // Format for year-month-day_hour-minute-second
                  date: new Date(),
                },
              },
              "__",
              "$title",
            ],
          },
        },
      },
    ]);
  }

  public static async deleteMany(filter: object) {
    return await Note.updateMany(filter, [
      {
        $set: {
          title: {
            $concat: [
              {
                $dateToString: {
                  format: "%Y-%m-%d_%H-%M-%S", // Format for year-month-day_hour-minute-second
                  date: new Date(),
                },
              },
              "__",
              "$title",
            ],
          },
          deletedAt: new Date(),
          deleted: true,
        },
      },
    ]);
  }
}
