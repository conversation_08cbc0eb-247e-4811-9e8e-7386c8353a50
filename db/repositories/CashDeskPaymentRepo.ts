import { PipelineStage } from "mongoose";
import APIFeatures from "../../helpers/ApiFeatures";
import {
  CashDeskPayment,
  CashDeskPaymentDocument,
} from "../models/CashDeskPayment";

export default class CashDeskPaymentRepo {
  //Create
  public static async create(
    cashDeskPayment: CashDeskPaymentDocument
  ): Promise<CashDeskPaymentDocument> {
    const newCashDeskPayment = await CashDeskPayment.create(cashDeskPayment);
    return newCashDeskPayment;
  }

  //Find One
  public static async findOne(
    obj: object
  ): Promise<CashDeskPaymentDocument | null> {
    return await CashDeskPayment.findOne(obj).populate([
      {
        path: "cashedTo",
        select: "username name",
      },
      {
        path: "cashedBy",
        select: "username name",
      },
      {
        path: "office",
        select: "name",
      },
    ]);
  }

  //Find + Pagination
  public static async findWithPagination(obj: Object, query: any) {
    const features = new APIFeatures(
      CashDeskPayment.find(obj).populate([
        {
          path: "cashedTo",
          select: "username name",
        },
        {
          path: "cashedBy",
          select: "username name",
        },
        {
          path: "office",
          select: "name",
        },
      ]),
      query
    )
      .filter()
      .sort()
      .limitFields();

    const options = {
      query: features.query,
      limit: query.limit,
      page: query.page,
    };
    return await CashDeskPayment.paginate(options);
  }

  //Find
  public static async find(query: any, filter: object) {
    const features = new APIFeatures(
      CashDeskPayment.find(filter).populate([
        {
          path: "cashedTo",
          select: "username name",
        },
        {
          path: "cashedBy",
          select: "username name",
        },
        {
          path: "office",
          select: "name",
        },
      ]),
      query
    )
      .filter()
      .sort()
      .limitFields();

    return await CashDeskPayment.find(features.query);
  }

  // find one and update
  public static async findOneAndUpdate(
    filter: object,
    cashDeskPayment: Partial<CashDeskPaymentDocument>
  ) {
    return await CashDeskPayment.findOneAndUpdate(filter, cashDeskPayment, {
      new: true,
    });
  }

  //Aggregation
  public static async aggregate(pipeline: PipelineStage[]) {
    return await CashDeskPayment.aggregate(pipeline);
  }
}
