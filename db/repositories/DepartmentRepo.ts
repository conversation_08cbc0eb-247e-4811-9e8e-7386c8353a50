import { FilterQuery } from "mongoose";
import APIFeatures from "../../helpers/ApiFeatures";
import Department, { DepartmentDocument } from "../models/Department";

export default class DepartmentRepo {
  // Create
  public static async create(department: object): Promise<DepartmentDocument> {
    const newDepartment = await Department.create(department);
    return newDepartment;
  }

  //Find One
  public static async findOne(obj: object): Promise<DepartmentDocument | null> {
    return await Department.findOne(obj);
  }

  //Find with filter
  public static async find(obj: Object, query: any) {
    const features = new APIFeatures(Department.find(obj), query)
      .filter()
      .sort()
      .limitFields();

    const options = {
      query: features.query,
      limit: query.limit | 10,
      page: query.page | 1,
    };
    return await Department.paginate(options);
  }

  //Find One And Update
  public static async findOneAndUpdate(
    department: FilterQuery<DepartmentDocument>,
    update: Partial<DepartmentDocument>
  ) {
    return await Department.findOneAndUpdate(department, update, {
      new: true,
    });
  }

  //Delete one
  public static async deleteOne(filter: object) {
    return await Department.findOneAndUpdate(filter, [
      {
        $set: {
          deletedAt: new Date(),
          deleted: true,
          name: {
            $concat: [
              {
                $dateToString: {
                  format: "%Y-%m-%d_%H-%M-%S", // Format for year-month-day_hour-minute-second
                  date: new Date(),
                },
              },
              "__",
              "$name",
            ],
          },
        },
      },
    ]);
  }
}
