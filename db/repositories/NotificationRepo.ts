import { PipelineStage } from 'mongoose'
import APIFeatures from '../../helpers/ApiFeatures'
import { Notification, NotificationDocument } from '../models/Notification'

export default class NotificationRepo {
  // Create amny
  public static async createMany(
    notifications: object[]
  ): Promise<NotificationDocument[]> {
    const newNotification = await Notification.insertMany(notifications)
    return newNotification
  }

  // List + Pagination
  public static async findWithPagination(obj: object, query?: any) {
    const features = new APIFeatures(
      Notification.find(obj).populate('from'),
      query
    )
      .filter()
      .sort()
      .limitFields()

    const options = {
      query: features.query,
      limit: query.limit,
      page: query.page,
    }

    return await Notification.paginate(options)
  }

  // Update one
  public static async update(
    filter: object,
    update: object
  ): Promise<NotificationDocument | null> {
    return await Notification.findOneAndUpdate(filter, update, { new: true })
  }

  // update many
  public static async updateMany(filter: object, update: object) {
    return await Notification.updateMany(filter, update)
  }

  //COUNT
  public static async count(filter: object) {
    return await Notification.count(filter)
  }
}
