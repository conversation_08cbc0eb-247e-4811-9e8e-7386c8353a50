import { Types } from "mongoose";
import APIFeatures from "../../helpers/ApiFeatures";
import { Tag, TagDocument } from "../models/Tag";

export default class TagRepo {
  public static async findOne(obj: object): Promise<TagDocument | null> {
    return await Tag.findOne(obj);
  }
  public static async find(obj: object, query: any) {
    const features = new APIFeatures(Tag.find(obj), query)
      .filter()
      .sort()
      .limitFields();

    const options = {
      query: features.query,
      limit: query.limit,
      page: query.page,
    };
    return Tag.paginate(options);
  }
  public static async create(tag: object): Promise<TagDocument> {
    return await Tag.create(tag);
  }
  public static async findByIdAndUpdate(
    tagsId: Types.ObjectId,
    tag: TagDocument
  ): Promise<TagDocument | null> {
    return await Tag.findByIdAndUpdate(tagsId, tag, { new: true });
  }
  public static async findAndUpdate(filter:object,tag:TagDocument){
    return await Tag.findOneAndUpdate(filter,tag,{new:true})
  }
  public static async deleteById(tagsId:string){
    return await Tag.findByIdAndDelete(tagsId);
  }
  public static async deleteMany(filter: object){
    return await Tag.deleteMany(filter)
  }
}
