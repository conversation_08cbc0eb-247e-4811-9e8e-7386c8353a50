import APIFeatures from "../../helpers/ApiFeatures";
import DailyKpi, { DailyKpiDocument } from "../models/DailyKpi";
import Kpi, { KpiDocument } from "../models/Kpi";

export default class DailyKpiRepo {
  // Create
  public static async create(
    dailyKpi: DailyKpiDocument
  ): Promise<DailyKpiDocument> {
    const newDailyKpi = await DailyKpi.create(dailyKpi);
    return newDailyKpi;
  }

  // List + Pagination
  public static async findWithPagination(obj: object, query?: any) {
    const features = new APIFeatures(DailyKpi.find(obj), query)
      .filter()
      .search(["name", "label"])
      .sort()
      .limitFields();
    const options = {
      query: features.query,
      limit: query.limit,
      page: query.page,
    };

    return await DailyKpi.paginate(options);
  }

  // List
  public static async find(query: object, filter: object) {
    const features = new APIFeatures(DailyKpi.find(filter), query)
      .filter()
      .search(["name", "label"])
      .sort();
    const dailyKpis = await DailyKpi.find(features.query);
    return dailyKpis;
  }

  // Get One
  public static async findOne(obj: object): Promise<DailyKpiDocument | null> {
    return await DailyKpi.findOne(obj);
  }

  // Update one
  public static async update(
    id: string,
    body: object
  ): Promise<DailyKpiDocument | null> {
    return await DailyKpi.findByIdAndUpdate(id, body, { new: true });
  }

  // update many
  public static async updateMany(filter: object, update: object) {
    return await DailyKpi.updateMany(filter, update);
  }

  // Delete One
  public static async deleteOne(filter: object, update: object): Promise<any> {
    await DailyKpi.updateOne(filter, update);
  }

  // Count
  public static async count(filter: object) {
    return await DailyKpi.count(filter);
  }
}
