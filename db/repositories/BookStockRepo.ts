import { FilterQuery } from "mongoose";
import APIFeatures from "../../helpers/ApiFeatures";
import { Book, BookDocument } from "../models/Book";
import { BookStock, BookStockDocument } from "../models/BookStock";
export default class BookStockRepo {
  //Create
  public static async create(
    bookStock: Partial<BookStockDocument>
  ): Promise<BookStockDocument> {
    const newBookStock = await BookStock.create(bookStock);
    return newBookStock;
  }

  //Find One
  public static async findOne(
    bookStock: FilterQuery<BookStockDocument>
  ): Promise<BookStockDocument | null> {
    return await BookStock.findOne(bookStock)
      .populate({
        path: "book",
        select: "name image level section",
        populate: [
          { path: "level", select: "name" },
          { path: "section", select: "name" },
        ],
      })
      .populate("office", "name");
  }

  //Find Available
  public static async findAvailable(obj: Object, query: any) {
    const features = new APIFeatures(
      BookStock.find(obj)
        .select("-office -deleted -createdAt -updatedAt")
        .populate({
          path: "book",
          select: "name image level section",
          populate: [
            { path: "level", select: "name" },
            { path: "section", select: "name" },
          ],
        }),
      query
    )
      .filter()
      .sort()
      .limitFields();

    const options = {
      query: features.query,
      limit: query.limit || 10,
      page: query.page || 1,
    };
    return await BookStock.paginate(options);
  }

  //Find with filter
  public static async find(obj: Object, query: any) {
    const features = new APIFeatures(
      BookStock.find(obj)
        .populate("office", "name")
        .populate({
          path: "book",
          select: "name image level section",
          populate: [
            { path: "level", select: "name" },
            { path: "section", select: "name" },
          ],
        }),
      query
    )
      .filter()
      .sort()
      .limitFields();

    const options = {
      query: features.query,
      limit: query.limit || 10,
      page: query.page || 1,
    };
    return await BookStock.paginate(options);
  }

  //Find One And Update
  public static async findOneAndUpdate(
    bookStock: FilterQuery<BookStockDocument>,
    update: Partial<BookStockDocument>
  ) {
    return await BookStock.findOneAndUpdate(bookStock, update, {
      new: true,
    });
  }

  //Delete one
  public static async deleteOne(filter: object) {
    return await BookStock.findOneAndDelete(filter);
  }

  // Count
  public static async count(
    bookStock: FilterQuery<BookStockDocument>
  ): Promise<number | null> {
    return await BookStock.count(bookStock);
  }

  // Stock Sum Quantity
  public static async stockSumQuantity(
    bookStock: FilterQuery<BookStockDocument>
  ): Promise<number> {
    const result = await BookStock.aggregate([
      { $match: bookStock },
      { $group: { _id: null, totalQuantity: { $sum: "$quantity" } } },
    ]);

    return result.length > 0 ? result[0].totalQuantity : 0;
  }

  // Stock Sum Per Book
  public static async stockSumQuantityPerBook(
    bookStock: FilterQuery<BookStockDocument>
  ): Promise<Partial<BookStockDocument>[] | null> {
    const result = await BookStock.aggregate([
      { $match: bookStock },
      {
        $group: {
          _id: "$book",
          count: {
            $sum: "$quantity",
          },
        },
      },
      {
        $lookup: {
          from: "books",
          localField: "_id",
          foreignField: "_id",
          as: "book",
        },
      },
      {
        $unwind: {
          path: "$book",
        },
      },
      {
        $project: {
          _id: 0,
          bookName: "$book.name",
          count: 1,
        },
      },
    ]);

    return result;
  }

  // Stock Sum Per Book
  public static async stockSumQuantityPerBookAndOffice(
    bookStock: FilterQuery<BookStockDocument>
  ): Promise<Partial<BookStockDocument>[] | null> {
    const result = await BookStock.aggregate([
      { $match: bookStock },
      {
        $group: {
          _id: {
            office: "$office",
            book: "$book",
          },
          count: {
            $sum: "$quantity",
          },
        },
      },
      {
        $lookup: {
          from: "books",
          localField: "_id.book",
          foreignField: "_id",
          as: "bookInfo",
        },
      },
      {
        $unwind: {
          path: "$bookInfo",
        },
      },
      {
        $lookup: {
          from: "offices",
          localField: "_id.office",
          foreignField: "_id",
          as: "officeInfo",
        },
      },
      {
        $unwind: {
          path: "$officeInfo",
        },
      },
      {
        $group: {
          _id: "$_id.office",
          office: {
            $first: "$officeInfo.name",
          },
          stock: {
            $push: {
              count: "$count",
              bookName: "$bookInfo.name",
            },
          },
        },
      },
      {
        $project: {
          _id: 0,
          office: 1,
          stock: 1,
        },
      },
    ]);

    return result;
  }
}
