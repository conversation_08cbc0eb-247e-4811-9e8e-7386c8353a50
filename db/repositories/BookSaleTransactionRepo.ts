import { endOfDay, startOfDay } from "date-fns";
import { FilterQuery, Types } from "mongoose";
import APIFeatures from "../../helpers/ApiFeatures";
import { Book, BookDocument } from "../models/Book";
import {
  BookSaleTransaction,
  BookSaleTransactionDocument,
} from "../models/BookSaleTransaction";
export default class BookSaleTransactionRepo {
  //Create
  public static async create(
    bookSaleTransaction: Partial<BookSaleTransactionDocument>
  ): Promise<BookSaleTransactionDocument> {
    const newBookSaleTransaction = await BookSaleTransaction.create(
      bookSaleTransaction
    );
    return newBookSaleTransaction;
  }

  //Find One
  public static async findOne(
    bookSaleTransaction: FilterQuery<BookSaleTransactionDocument>
  ): Promise<BookSaleTransactionDocument | null> {
    return await BookSaleTransaction.findOne(bookSaleTransaction)
      .populate("office", "name address")
      .populate("createdBy", "username name")
      .populate({
        path: "items.book",
        select: "name image level section",
        populate: [
          { path: "level", select: "name" },
          { path: "section", select: "name" },
        ],
      });
  }

  //Find with filter
  public static async find(
    bookSaleTransaction: FilterQuery<BookSaleTransactionDocument>,
    query: FilterQuery<BookSaleTransactionDocument>
  ) {
    const features = new APIFeatures(
      BookSaleTransaction.find(bookSaleTransaction)
        .populate("office", "name address")
        .populate("createdBy", "username name")
        .populate({
          path: "items.book",
          select: "name image level section",
          populate: [
            { path: "level", select: "name" },
            { path: "section", select: "name" },
          ],
        }),
      query
    )
      .filter()
      .sort()
      .limitFields();

    const options = {
      query: features.query,
      limit: query.limit || 10,
      page: query.page || 1,
    };
    return await BookSaleTransaction.paginate(options);
  }

  //Find One And Update
  public static async findOneAndUpdate(
    bookSaleTransaction: FilterQuery<BookSaleTransactionDocument>,
    update: Partial<BookSaleTransactionDocument>
  ) {
    return await BookSaleTransaction.findOneAndUpdate(
      bookSaleTransaction,
      update,
      {
        new: true,
      }
    );
  }

  // Delete One (Soft Delete)
  public static async deleteOne(
    bookSaleTransaction: FilterQuery<BookSaleTransactionDocument>
  ) {
    return await BookSaleTransaction.delete(bookSaleTransaction);
  }

  // Total Amount Per Office
  public static async totalAmountPerOffice(
    bookSaleTransaction: FilterQuery<BookSaleTransactionDocument>
  ) {
    return await BookSaleTransaction.aggregate([
      { $match: bookSaleTransaction },
      {
        $group: {
          _id: "$office",
          totalRevenue: {
            $sum: "$totalAmount",
          },
        },
      },
      {
        $lookup: {
          from: "offices",
          localField: "_id",
          foreignField: "_id",
          as: "office",
        },
      },
      {
        $unwind: {
          path: "$office",
        },
      },
      {
        $project: {
          _id: 0,
          office: "$office.name",
          totalRevenue: 1,
        },
      },
    ]);
  }

  // Total Amount
  public static async totalAmount(
    bookSaleTransaction: FilterQuery<BookSaleTransactionDocument>
  ) {
    return await BookSaleTransaction.aggregate([
      { $match: bookSaleTransaction },
      {
        $group: {
          _id: "office",
          totalAmount: {
            $sum: "$totalAmount",
          },
        },
      },
    ]);
  }

  // Total Revenues
  public static async totalRevenue(
    bookSaleTransaction: FilterQuery<BookSaleTransactionDocument>
  ) {
    return await BookSaleTransaction.aggregate([
      { $match: bookSaleTransaction },
      {
        $group: {
          _id: null,
          totalRevenue: {
            $sum: "$totalAmount",
          },
        },
      },
    ]);
  }
}
