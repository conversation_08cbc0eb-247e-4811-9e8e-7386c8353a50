import { PipelineStage } from "mongoose";
import APIFeatures from "../../helpers/ApiFeatures";
import Operation, { OperationDocument } from "../models/Operation";

export default class OperationRepo {
  //Create
  public static async create(
    operation: OperationDocument
  ): Promise<OperationDocument> {
    const newOperation = await Operation.create(operation);
    await newOperation.populate([
      {
        path: "admin",
        select: "name username",
      },
      {
        path: "office",
        select: "name",
      },
    ]);
    return newOperation;
  }
  //Find with Pagination
  public static async findWithPagination(obj: Object, query: any) {
    const features = new APIFeatures(
      Operation.find(obj).populate([
        {
          path: "admin",
          select: "name username",
        },
        {
          path: "office",
          select: "name",
        },
      ]),
      query
    )
      .filter()
      .search(["notes"])
      .sort()
      .limitFields();

    const options = {
      query: features.query,
      limit: query.limit,
      page: query.page,
    };

    return await Operation.paginate(options);
  }

  // Find
  public static async find(query: any, filter: object) {
    const features = new APIFeatures(
      Operation.find(filter).populate([
        {
          path: "admin",
          select: "name username",
        },
        {
          path: "office",
          select: "name",
        },
      ]),
      query
    )
      .filter()
      .search(["notes"])
      .sort()
      .limitFields();

    return await Operation.find(features.query);
  }
  //Find One
  public static async findOne(obj: object): Promise<OperationDocument | null> {
    return await Operation.findOne(obj).populate([
      {
        path: "admin",
        select: "username name",
      },
      {
        path: "office",
        select: "name",
      },
    ]);
  }

  // find one and update
  public static async findOneAndUpdate(
    filter: object,
    operation: Partial<OperationDocument>
  ) {
    return await Operation.findOneAndUpdate(filter, operation, {
      new: true,
      populate: [
        {
          path: "admin",
          select: "username name",
        },
        {
          path: "office",
          select: "name",
        },
      ],
    });
  }

  //Aggregation
  public static async aggregate(pipeline: PipelineStage[]) {
    return await Operation.aggregate(pipeline);
  }
}
