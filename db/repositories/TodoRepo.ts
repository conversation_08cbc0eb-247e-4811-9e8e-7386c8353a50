import APIFeatures from "../../helpers/ApiFeatures";
import { Todo, TodoDocument } from "../models/Todo";

export default class TodoRepo {
  // create
  public static async create(todo: TodoDocument): Promise<TodoDocument> {
    const createdTodo = await Todo.create(todo);
    return await createdTodo.populate([
      {
        path: "author",
        select: "name username",
      },
    ]);
  }

  // List + Pagination
  public static async findWithPagination(obj: object, query?: any) {
    const features = new APIFeatures(
      Todo.find(obj).populate([
        {
          path: "author",
          select: "name username",
        },
      ]),
      query
    )
      .filter()
      .search(["description"])
      .sort()
      .limitFields();
    const options = {
      query: features.query,
      limit: query.limit,
      page: query.page,
    };

    return await Todo.paginate(options);
  }

  // find one
  public static async findOne(obj: object) {
    return await Todo.findOne(obj).populate([
      {
        path: "author",
        select: "name username",
      },
    ]);
  }

  // Delete One
  public static async deleteOne(filter: object, update: object): Promise<any> {
    await Todo.updateOne(filter, update);
  }

  // update many
  public static async updateMany(filter: object, update: object) {
    return await Todo.updateMany(filter, update);
  }

  // Update
  public static async update(
    filter: object,
    update: object
  ): Promise<TodoDocument | null> {
    return await Todo.findOneAndUpdate(filter, update, { new: true }).populate([
      {
        path: "author",
        select: "name username",
      },
    ]);
  }
}
