import APIFeatures from "../../helpers/ApiFeatures";
import Office, { OfficeDocument } from "../models/Office";

export default class OfficeRepo {
  // Create
  public static async create(office: object): Promise<OfficeDocument> {
    const newOffice = await Office.create(office);
    return newOffice;
  }

  //Find One
  public static async findOne(obj: object): Promise<OfficeDocument | null> {
    return await Office.findOne(obj);
  }

  //Count
  public static async count(obj: object): Promise<Number | null> {
    return await Office.count(obj);
  }

  // find + Pagination
  public static async findWithPagination(obj: object, query: any) {
    const features = new APIFeatures(Office.find(obj), query)
      .filter()
      .search(["name", "address"])
      .sort();

    const options = {
      query: features.query,
      limit: query.limit,
      page: query.page,
    };

    return await Office.paginate(options);
  }

  // find
  public static async find(query: any, filter: object) {
    const features = new APIFeatures(Office.find(filter), query)
      .filter()
      .search(["name", "address"])
      .sort()
      .limitFields();

    return await Office.find(features.query);
  }

  //Find One And Update
  public static async findOneAndUpdate(
    filter: object,
    update: object
  ): Promise<OfficeDocument | null> {
    return await Office.findOneAndUpdate(filter, update, {
      new: true,
    });
  }

  // find
  public static async findAll(filter: object) {
    return await Office.find(filter);
  }
}
