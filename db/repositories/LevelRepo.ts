import { FilterQuery } from "mongoose";
import APIFeatures from "../../helpers/ApiFeatures";
import { Level, LevelDocument } from "../models/Level";

export default class LevelRepo {
  //Create
  public static async create(
    level: Partial<LevelDocument>
  ): Promise<LevelDocument> {
    const newLevel = await Level.create(level);
    return newLevel;
  }

  //Find One
  public static async findOne(
    level: FilterQuery<LevelDocument>
  ): Promise<LevelDocument | null> {
    return await Level.findOne(level);
  }

  //Find with filter
  public static async find(obj: Object, query: any) {
    const features = new APIFeatures(Level.find(obj), query)
      .filter()
      .sort()
      .limitFields();

    const options = {
      query: features.query,
      limit: query.limit || 10,
      page: query.page || 1,
    };
    return await Level.paginate(options);
  }

  //Find One And Update
  public static async findOneAndUpdate(
    level: FilterQuery<LevelDocument>,
    update: Partial<LevelDocument>
  ) {
    return await Level.findOneAndUpdate(level, update, {
      new: true,
    });
  }

  //Delete one
  public static async deleteOne(filter: object) {
    return await Level.findOneAndUpdate(filter, [
      {
        $set: {
          deletedAt: new Date(),
          deleted: true,
          name: {
            $concat: [
              {
                $dateToString: {
                  format: "%Y-%m-%d_%H-%M-%S", // Format for year-month-day_hour-minute-second
                  date: new Date(),
                },
              },
              "__",
              "$name",
            ],
          },
        },
      },
    ]);
  }
}
