import APIFeatures from "../../helpers/ApiFeatures";
import Permission, { PermissionDocument } from "../models/Permission";

export default class PermissionRepo {
  //Create
  public static async create(
    permission: PermissionDocument
  ): Promise<PermissionDocument> {
    const newPermission = await Permission.create(permission);
    return newPermission;
  }

  //Find One
  public static async findOne(obj: object): Promise<PermissionDocument | null> {
    return await Permission.findOne(obj);
  }

  //Find with filter
  public static async find(obj: Object, query: any) {
    const features = new APIFeatures(Permission.find(obj), query)
      .filter()
      .sort()
      .limitFields();

    const options = {
      query: features.query,
      limit: query.limit,
      page: query.page,
    };
    return await Permission.paginate(options);
  }

  //COUNT
  public static async count(obj: object) {
    return await Permission.count(obj);
  }
}
