import APIFeatures from "../../helpers/ApiFeatures";
import { File, FileDocument } from "../models/File";

export default class FileRepo {
  public static async find(obj: Object, query: any) {
    const features = new APIFeatures(File.find(obj), query)
      .filter()
      .sort()
      .search(["name"])
      .limitFields();
    const options = {
      query: features.query,
      limit: query.limit,
      page: query.page,
    };
    return await File.paginate(options);
  }
  public static async findOne(obj: object) {
    return await File.findOne(obj);
  }
  public static async insertMany(obj: Object[]) {
    return await File.insertMany(obj);
  }
  public static async findOneAndUpdate(
    filter: object,
    update: object
  ): Promise<FileDocument | null> {
    return await File.findOneAndUpdate(filter, update, {
      new: true,
    });
  }
  public static async delete(filter: object) {
    return await File.findOneAndUpdate();
  }
}
