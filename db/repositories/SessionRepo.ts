import { PipelineStage } from "mongoose";
import APIFeatures from "../../helpers/ApiFeatures";
import { Session, SessionDocument } from "../models/Session";

export default class SessionRepo {
  //Create
  public static async create(
    session: Partial<SessionDocument>
  ): Promise<SessionDocument> {
    return await Session.create(session);
  }

  // create Many
  public static async createMany(
    sessions: Partial<SessionDocument>[]
  ): Promise<SessionDocument[]> {
    return await Session.insertMany(sessions);
  }

  // List + Pagination
  public static async find(obj: object, query?: any, withPagination?: boolean) {
    const features = new APIFeatures(
      Session.find(obj).populate("user", "name username"),
      query
    )
      .filter()
      .search()
      .sort()
      .limitFields();

    const options = withPagination
      ? {
          query: features.query,
          limit: query.limit || 10,
          page: query.page || 1,
        }
      : { query: features.query };

    return await Session.paginate(options);
  }

  //Aggregation
  public static async aggregate(pipeline: PipelineStage[]) {
    return await Session.aggregate(pipeline);
  }
  // Aggregation +  Pagination
  public static async aggregatePaginate(
    pipeline: PipelineStage[],
    options: { limit: number; page: number }
  ) {
    return await Session.aggregatePaginate(
      Session.aggregate(pipeline),
      options
    );
  }
}
