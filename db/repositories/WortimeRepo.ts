import { PipelineStage } from "mongoose";
import APIFeatures from "../../helpers/ApiFeatures";
import { Worktime, WorktimeDocument } from "../models/Worktime";

export default class WorktimeRepo {
  // Create
  public static async createMany(worktimes: WorktimeDocument[]): Promise<any> {
    return await Worktime.insertMany(worktimes);
  }

  // Find
  public static async findOne(obj: object): Promise<WorktimeDocument | null> {
    return Worktime.findOne(obj);
  }

  // Find All With pagination
  public static async findWithPagination(obj: object, query: any) {
    const features = await new APIFeatures(Worktime.find(obj), query)
      .filter()
      .sort()
      .limitFields();

    const options = {
      query: features.query,
      limit: query.limit,
      page: query.page,
    };
    return Worktime.paginate(options);
  }
  // Update
  public static async update(
    filter: object,
    body: any
  ): Promise<WorktimeDocument | null> {
    let data = await Worktime.findOneAndUpdate(filter, body, { new: true });
    return data;
  }

  // COUNT
  public static async count(obj: object) {
    return await Worktime.count(obj);
  }

  // Many MANY
  public static async updateMany(filter: object, update: object) {
    return await Worktime.updateMany(filter, update);
  }

  // Find All
  public static async findAll(filter: object): Promise<WorktimeDocument[]> {
    const overlappingWorktimes = await Worktime.find(filter);
    return overlappingWorktimes;
  }

  // aggregate
  public static async aggregate(pipeline: PipelineStage[]) {
    return await Worktime.aggregate(pipeline);
  }
}
