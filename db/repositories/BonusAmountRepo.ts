import APIFeatures from "../../helpers/ApiFeatures";
import { BonusAmount, BonusAmountDocument } from "../models/BonusAmount";

export default class BonusAmountRepo {
  public static async create(amount: object): Promise<BonusAmountDocument> {
    return await BonusAmount.create(amount);
  }

  public static async find(filter: object): Promise<BonusAmountDocument[]> {
    return await BonusAmount.find(filter);
  }

  public static async findWithPagination(filter: Object, query: any) {
    const features = new APIFeatures(BonusAmount.find(filter), query)
      .filter()
      .sort()
      .limitFields();

    const options = {
      query: features.query,
      limit: query.limit || 10,
      page: query.page || 1,
    };
    return await BonusAmount.paginate(options);
  }

  public static async findOne(filter: object) {
    return await BonusAmount.findOne(filter);
  }

  public static async update(
    filter: object,
    update: object
  ): Promise<BonusAmountDocument | null> {
    return await BonusAmount.findOneAndUpdate(filter, update, { new: true });
  }

  public static async count(filter: Object) {
    return await BonusAmount.count(filter);
  }

  public static async updateMany(filter: Object, update: Object) {
    return await BonusAmount.updateMany(filter, update);
  }
}
