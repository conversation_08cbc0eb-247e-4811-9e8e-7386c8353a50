import { PipelineStage } from "mongoose";
import APIFeatures from "../../helpers/ApiFeatures";
import DailyResponse from "../models/DailyResponse";
import { DailyResponseDocument } from "../models/DailyResponse";

export default class DailyResponseRepo {
  //Create
  public static async create(
    dailyResponse: DailyResponseDocument
  ): Promise<DailyResponseDocument> {
    //Create
    const newDailyResponse = await DailyResponse.create(dailyResponse);
    //Populate
    await newDailyResponse.populate({
      path: "dailyKpis",
      populate: {
        path: "dailyKpi",
      },
    });
    await newDailyResponse.populate("dailyQuestion");

    //Return the response
    return newDailyResponse;
  }

  //Find +  pagination
  public static async findWithPagination(obj: Object, query: any) {
    const features = new APIFeatures(
      DailyResponse.find(obj).populate([
        {
          path: "dailyKpis",
          populate: {
            path: "dailyKpi",
            select: { name: 1, frontType: 1 },
          },
        },
        {
          path: "admin",
          select: "name",
        },
        {
          path: "dailyQuestion",
          select: "name",
        },
        {
          path: "office",
          select: "name",
        },
      ]),
      query
    )
      .filter()
      .sort()
      .limitFields();

    const options = {
      query: features.query,
      limit: query.limit,
      page: query.page,
    };
    return await DailyResponse.paginate(options);
  }

  //Find
  public static async find(query: any) {
    return await DailyResponse.find(query).populate([
      {
        path: "dailyKpis",
        populate: {
          path: "dailyKpi",
          select: { name: 1, frontType: 1 },
        },
      },
      {
        path: "admin",
        select: "name",
      },
      {
        path: "dailyQuestion",
        select: "name",
      },
      {
        path: "office",
        select: "name",
      },
    ]);
  }

  //Find One
  public static async findOne(
    obj: object
  ): Promise<DailyResponseDocument | null> {
    return await DailyResponse.findOne(obj).populate([
      {
        path: "dailyKpis",
        populate: {
          path: "dailyKpi",
        },
      },
      {
        path: "admin",
        select: "name",
      },
      {
        path: "dailyQuestion",
        select: "name",
      },
      {
        path: "office",
        select: "name",
      },
    ]);
  }

  //find with pagination
  public static async findPaginate(obj: object, query: any) {
    const features = new APIFeatures(DailyResponse.find(obj), query)
      .filter()
      .sort()
      .limitFields();
    const options = {
      query: { ...features.query, ...obj },
      limit: query.limit,
      page: query.page,
      populate: [
        {
          path: "dailyKpis",
          populate: {
            path: "dailyKpi",
            select: { name: 1, frontType: 1 },
          },
        },
        {
          path: "dailyQuestion",
          select: { name: 1 },
        },
        {
          path: "admin",
          select: { name: 1 },
        },
        {
          path: "office",
          select: "name",
        },
      ],
    };
    return await DailyResponse.paginate(options);
  }

  //Aggregation
  public static async aggregate(pipeline: PipelineStage[]) {
    return await DailyResponse.aggregate(pipeline);
  }

  //Count
  public static async count(obj: object) {
    return await DailyResponse.countDocuments(obj);
  }

  //Update
  public static async findOneAndUpdate(filter: object, update: object) {
    return await DailyResponse.findOneAndUpdate(filter, update, {
      new: true,
    }).populate([
      {
        path: "dailyKpis",
        populate: {
          path: "dailyKpi",
        },
      },
      {
        path: "admin",
        select: "name",
      },
      {
        path: "dailyquestion",
        select: "name",
      },
      {
        path: "office",
        select: "name",
      },
    ]);
  }

  //Update Many
  public static async updateMany(filter: object, update: object) {
    return await DailyResponse.updateMany(filter, update);
  }
}
