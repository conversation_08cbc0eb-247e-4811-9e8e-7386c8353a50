import APIFeatures from "../../helpers/ApiFeatures";
import DailyQuestion, { DailyQuestionDocument } from "../models/DailyQuestion";

export default class DailyQuestionRepo {
  //Create
  public static async create(
    dailyQuestion: DailyQuestionDocument
  ): Promise<DailyQuestionDocument> {
    const newDailyQuestion = await DailyQuestion.create(dailyQuestion);
    return newDailyQuestion;
  }

  //Find
  public static async find(obj: Object, query: any) {
    const features = new APIFeatures(
      DailyQuestion.find(obj).populate("dailyKpis"),
      query
    )
      .filter()
      .sort()
      .limitFields();

    const options = {
      query: features.query,
      limit: query.limit,
      page: query.page,
    };
    return await DailyQuestion.paginate(options);
  }

  //Find One
  public static async findOne(
    obj: object
  ): Promise<DailyQuestionDocument | null> {
    return await DailyQuestion.findOne(obj).populate("dailyKpis");
  }

  //Find One And Update
  public static async findOneAndUpdate(
    filter: object,
    update: object
  ): Promise<DailyQuestionDocument | null> {
    return await DailyQuestion.findOneAndUpdate(filter, update, {
      new: true,
    });
  }

  //count
  public static async count(obj: Object) {
    return await DailyQuestion.count(obj);
  }

  //Update Many
  public static async updateMany(filter: Object, update: Object) {
    return await DailyQuestion.updateMany(filter, update);
  }
}
