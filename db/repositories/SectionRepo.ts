import { FilterQuery } from "mongoose";
import APIFeatures from "../../helpers/ApiFeatures";
import { Section, SectionDocument } from "../models/Section";

export default class SectionRepo {
  //Create
  public static async create(
    section: Partial<SectionDocument>
  ): Promise<SectionDocument> {
    const newSection = await Section.create(section);
    return newSection;
  }

  //Find One
  public static async findOne(obj: object): Promise<SectionDocument | null> {
    return await Section.findOne(obj);
  }

  //Find with filter
  public static async find(obj: Object, query: any) {
    const features = new APIFeatures(Section.find(obj), query)
      .filter()
      .sort()
      .limitFields();

    const options = {
      query: features.query,
      limit: query.limit || 10,
      page: query.page || 1,
    };
    return await Section.paginate(options);
  }

  //Find One And Update
  public static async findOneAndUpdate(
    section: FilterQuery<SectionDocument>,
    update: Partial<SectionDocument>
  ) {
    return await Section.findOneAndUpdate(section, update, {
      new: true,
    });
  }

  //Delete one
  public static async deleteOne(filter: object) {
    return await Section.findOneAndUpdate(filter, [
      {
        $set: {
          deletedAt: new Date(),
          deleted: true,
          name: {
            $concat: [
              {
                $dateToString: {
                  format: "%Y-%m-%d_%H-%M-%S", // Format for year-month-day_hour-minute-second
                  date: new Date(),
                },
              },
              "__",
              "$name",
            ],
          },
        },
      },
    ]);
  }
}
