import { FilterQuery } from "mongoose";
import APIFeatures from "../../helpers/ApiFeatures";
import DepartmentWorktime, {
  DepartmentWorktimeDocument,
} from "../models/DepartmentWorktime";

export default class DepartmentWorktimeRepo {
  // Create
  public static async create(
    departmentWorktime: object
  ): Promise<DepartmentWorktimeDocument> {
    const newDepartmentWorktime = await DepartmentWorktime.create(
      departmentWorktime
    );
    return newDepartmentWorktime;
  }

  //Find One
  public static async findOne(
    obj: object
  ): Promise<DepartmentWorktimeDocument | null> {
    return await DepartmentWorktime.findOne(obj).populate("department", "name");
  }

  //Find with filter
  public static async find(obj: Object, query: any) {
    const features = new APIFeatures(
      DepartmentWorktime.find(obj).populate("department", "name"),
      query
    )
      .filter()
      .sort()
      .limitFields();

    const options = {
      query: features.query,
      limit: query.limit | 10,
      page: query.page | 1,
    };
    return await DepartmentWorktime.paginate(options);
  }

  //Find One And Update
  public static async findOneAndUpdate(
    departmentWorktime: FilterQuery<DepartmentWorktimeDocument>,
    update: Partial<DepartmentWorktimeDocument>
  ) {
    return await DepartmentWorktime.findOneAndUpdate(
      departmentWorktime,
      update,
      {
        new: true,
      }
    );
  }

  // Delete One (Soft Delete)
  public static async deleteOne(
    departmentWorktime: FilterQuery<DepartmentWorktimeDocument>
  ) {
    return await DepartmentWorktime.delete(departmentWorktime);
  }

  //Find with filter
  public static async simpleFind(
    departmentWorktime: FilterQuery<DepartmentWorktimeDocument>
  ) {
    const departmentWorktimes = await DepartmentWorktime.find(
      departmentWorktime
    );
    return departmentWorktimes;
  }
}
