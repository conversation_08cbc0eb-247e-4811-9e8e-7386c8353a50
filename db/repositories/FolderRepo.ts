import APIFeatures from "../../helpers/ApiFeatures";
import { Folder, FolderDocument } from "./../models/Folder";

export const populateInDepthFiveSubFolder = [
  {
    path: "subFolder",
    populate: [
      {
        path: "subFolder",
        populate: [
          {
            path: "subFolder",
            populate: [
              { path: "subFolder", populate: "subFolder" },
              {
                path: "files",
                populate: {
                  path: "addedBy",
                },
              },
            ],
          },
          {
            path: "files",
            populate: {
              path: "addedBy",
            },
          },
        ],
      },
      {
        path: "files",
        populate: {
          path: "addedBy",
        },
      },
      {
        path: "show",
      },
    ],
  },
  {
    path: "files",
    populate: {
      path: "addedBy",
    },
  },
  {
    path: "show",
  },
];

export default class FolderRepo {
  public static async find(obj: Object, query: any) {
    const features = new APIFeatures(
      Folder.find(obj).populate(populateInDepthFiveSubFolder),
      query
    )
      .filter()
      .sort()
      .limitFields();
    const options = {
      query: features.query,
      limit: query.limit,
      page: query.page,
    };
    return await Folder.paginate(options);
  }
  public static async findByIdAndUpdate(id: string, obj: Object) {
    return await Folder.findByIdAndUpdate(id, obj, { new: true }).populate(
      "show"
    );
  }
  public static async findOne(obj: Object) {
    return await Folder.findOne(obj);
  }
  public static async create(obj: FolderDocument) {
    return await Folder.create(obj);
  }
  public static async delete(filter: object): Promise<any> {
    return await Folder.updateOne(filter, [
      {
        $set: {
          name: { $concat: ["old__", "$name"] },
          deletedAt: new Date(),
          deleted: true,
        },
      },
    ]);
  }
  public static async isAtMaxDepth(folderId: string): Promise<boolean> {
    let folder = await Folder.findById(folderId);
    let depth = 0;
    while (folder.parentFolder) {
      depth++;
      folder = await Folder.findById(folder.parentFolder);
    }
    return depth >= 5;
  }
}
