import { PipelineStage } from "mongoose";
import APIFeatures from "../../helpers/ApiFeatures";
import StatClientResponse from "../models/StatClientResponse";
import { StatClientResponseDocument } from "../models/StatClientResponse";

export default class StatClientResponseRepo {
  //Create
  public static async create(
    statClientResponse: StatClientResponseDocument
  ): Promise<StatClientResponseDocument> {
    //Create
    const newStatClientResponse = await StatClientResponse.create(
      statClientResponse
    );
    //Populate
    await newStatClientResponse.populate({
      path: "kpis",
      populate: {
        path: "kpi",
      },
    });
    await newStatClientResponse.populate("statClient");

    //Return the reponse
    return newStatClientResponse;
  }

  //Find +  pagination
  public static async findWithPagination(obj: Object, query: any) {
    const features = new APIFeatures(
      StatClientResponse.find(obj).populate([
        {
          path: "kpis",
          populate: {
            path: "kpi",
            select: { name: 1, frontType: 1 },
          },
        },
        {
          path: "admin",
          select: "name",
        },
        {
          path: "statClient",
          select: "name",
        },
        {
          path: "office",
          select: "name",
        },
      ]),
      query
    )
      .filter()
      .sort()
      .limitFields();

    const options = {
      query: features.query,
      limit: query.limit,
      page: query.page,
    };
    return await StatClientResponse.paginate(options);
  }
  //Find
  public static async find(query: any) {
    return await StatClientResponse.find(query).populate([
      {
        path: "kpis",
        populate: {
          path: "kpi",
          select: { name: 1, frontType: 1 },
        },
      },
      {
        path: "admin",
        select: "name",
      },
      {
        path: "statClient",
        select: "name",
      },
      {
        path: "office",
        select: "name",
      },
    ]);
  }

  //Find One
  public static async findOne(
    obj: object
  ): Promise<StatClientResponseDocument | null> {
    return await StatClientResponse.findOne(obj).populate([
      {
        path: "kpis",
        populate: {
          path: "kpi",
        },
      },
      {
        path: "admin",
        select: "name",
      },
      {
        path: "statClient",
        select: "name",
      },
      {
        path: "office",
        select: "name",
      },
    ]);
  }

  //Find One And Update
  public static async findOneAndUpdate(
    filter: object,
    update: object
  ): Promise<StatClientResponseDocument | null> {
    return await StatClientResponse.findOneAndUpdate(filter, update, {
      new: true,
    }).populate({
      path: "kpis",
      populate: {
        path: "kpi",
      },
    });
  }

  //count
  public static async count(obj: Object) {
    return await StatClientResponse.count(obj);
  }

  //Update Many
  public static async updateMany(filter: Object, update: Object) {
    return await StatClientResponse.updateMany(filter, update);
  }

  //find with pagination
  public static async findPaginate(obj: object, query: any) {
    const features = new APIFeatures(StatClientResponse.find(obj), query)
      .filter()
      .sort()
      .limitFields();
    const options = {
      query: { ...features.query, ...obj },
      limit: query.limit,
      page: query.page,
      populate: [
        {
          path: "kpis",
          populate: {
            path: "kpi",
            select: { name: 1, frontType: 1 },
          },
        },
        {
          path: "statClient",
          select: { name: 1 },
        },
        {
          path: "admin",
          select: { name: 1 },
        },
        {
          path: "office",
          select: "name",
        },
      ],
    };
    return await StatClientResponse.paginate(options);
  }
  //Aggregation
  public static async aggregate(pipeline: PipelineStage[]) {
    return await StatClientResponse.aggregate(pipeline);
  }
}
