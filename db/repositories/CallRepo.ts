import APIFeatures from "../../helpers/ApiFeatures";
import { Call, CallDocument } from "../models/Call";

export default class CallRepo {
  public static async create(CallInput: CallDocument): Promise<any> {
    const newCall = new Call(CallInput);
    await newCall.save();
    return newCall;
  }

  //Find One
  public static async findOne(obj: object): Promise<CallDocument | null> {
    return await Call.findOne(obj);
  }

  //Find + pagination
  public static async findWithPagination(obj: Object, query: any = {}) {
    const features = new APIFeatures(
      Call.find(obj).populate("user", "name"),
      query
    )
      .filter()
      .sort()
      .limitFields();

    const options = {
      query: features.query,
      limit: query.limit,
      page: query.page,
    };
    
    return await Call.paginate(options);
  }

  //Find
  public static async find(query: any = {}, filter: object) {
    const features = new APIFeatures(
      Call.find(filter).populate("user", "name username"),
      query
    )
      .filter()
      .sort()
      .limitFields();
    return await Call.find(features.query);
  }
  public static async findById(id: string) {
    return await this.findOne({ _id: id });
  }
  public static async findByIdAndUpdate(id: string, newCall: CallDocument) {
    return await Call.findByIdAndUpdate(id, newCall, { new: true });
  }
}
