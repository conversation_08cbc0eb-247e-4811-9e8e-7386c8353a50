import APIFeatures from "../../helpers/ApiFeatures";
import Kpi, { KpiDocument } from "../models/Kpi";

export default class KpiRepo {
  // Create
  public static async create(kpi: KpiDocument): Promise<KpiDocument> {
    const newKpi = await Kpi.create(kpi);
    return newKpi;
  }

  // List + Pagination
  public static async findWithPagination(obj: object, query?: any) {
    const features = new APIFeatures(Kpi.find(obj), query)
      .filter()
      .search(["name", "label"])
      .sort()
      .limitFields();
    const options = {
      query: features.query,
      limit: query.limit,
      page: query.page,
    };

    return await Kpi.paginate(options);
  }

  // List
  public static async find(query: object, filter: object) {
    const features = new APIFeatures(Kpi.find(filter), query)
      .filter()
      .search(["name", "label"])
      .sort();
    const kpis = await Kpi.find(features.query);
    return kpis;
  }

  // Get One
  public static async findOne(obj: object): Promise<KpiDocument | null> {
    return await Kpi.findOne(obj);
  }

  // Update one
  public static async update(
    id: string,
    body: object
  ): Promise<KpiDocument | null> {
    return await Kpi.findByIdAndUpdate(id, body, { new: true });
  }

  // update many
  public static async updateMany(filter: object, update: object) {
    return await Kpi.updateMany(filter, update);
  }

  // Delete One
  public static async deleteOne(filter: object, update: object): Promise<any> {
    await Kpi.updateOne(filter, update);
  }

  // Count
  public static async count(filter: object) {
    return await Kpi.count(filter);
  }
}
