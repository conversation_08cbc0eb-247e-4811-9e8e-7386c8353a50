import { PipelineStage } from "mongoose";
import APIFeatures from "../../helpers/ApiFeatures";
import Code, { CodeDocument } from "../models/Code";

export default class CodeRepo {
  //Create
  public static async create(code: CodeDocument): Promise<CodeDocument> {
    const newCode = await Code.create(code);
    return newCode;
  }
  //Find + Pagination
  public static async findWithPagination(obj: Object, query: any) {
    const features = new APIFeatures(
      Code.find(obj).populate([
        {
          path: "admin",
          select: "name",
        },
        {
          path: "office",
          select: "name",
        },
        {
          path: "paymentNote",
          select: "name description",
        },
      ]),
      query
    )
      .filter()
      .sort()
      .limitFields();

    const options = {
      query: features.query,
      limit: query.limit || 10,
      page: query.page || 1,
    };
    return await Code.paginate(options);
  }
  //Find
  public static async find(query: any, filter: object) {
    const features = new APIFeatures(
      Code.find(filter).populate([
        {
          path: "admin",
          select: "name username",
        },
        {
          path: "office",
          select: "name",
        },
        {
          path: "paymentNote",
          select: "name description",
        },
      ]),
      query
    )
      .filter()
      .sort()
      .limitFields();

    return await Code.find(features.query);
  }

  //Find One
  public static async findOne(obj: object): Promise<CodeDocument | null> {
    return await Code.findOne(obj).populate([
      {
        path: "admin",
        select: "username name",
      },
      {
        path: "office",
        select: "name",
      },
      {
        path: "paymentNote",
        select: "name description",
      },
    ]);
  }

  // find one and update
  public static async findOneAndUpdate(
    filter: object,
    code: Partial<CodeDocument>
  ) {
    return await Code.findOneAndUpdate(filter, code, { new: true });
  }

  public static async deleteOne(filter: object) {
    return await Code.findOneAndUpdate(filter, [
      {
        $set: {
          deletedAt: new Date(),
          deleted: true,
          code: {
            $concat: ["old__", "$code"],
          },
        },
      },
    ]);
  }

  //Aggregation
  public static async aggregate(pipeline: PipelineStage[]) {
    return await Code.aggregate(pipeline);
  }

  //Count
  public static async count(filter: object): Promise<number> {
    return await Code.count(filter);
  }
}
