import APIFeatures from "../../helpers/ApiFeatures";
import { PdfContent, pdfContentDocument } from "../models/pdfContent";

export default class PdfContentRepo {
  public static async findOne(obj: object): Promise<pdfContentDocument | null> {
    return await PdfContent.findOne(obj);
  }

  public static async find(filter: object) {
    return await PdfContent.find(filter);
  }

  public static async findWithPagination(obj: object, query?: any) {
    const features = new APIFeatures(PdfContent.find(obj), query)
      .filter()
      .search(["title", "content"])
      .sort()
      .limitFields();
    const options = {
      query: features.query,
      limit: query.limit,
      page: query.page,
    };

    return await PdfContent.paginate(options);
  }

  public static async create(obj: object) {
    const pdfContent = await PdfContent.create(obj);
    return await pdfContent;
  }

  public static async updateOne(filter: object, update: object) {
    return await PdfContent.findOneAndUpdate(filter, update, {
      new: true,
    });
  }
}
