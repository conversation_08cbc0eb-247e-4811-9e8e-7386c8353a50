import APIFeatures from '../../helpers/ApiFeatures'
import StatClient, { StatClientDocument } from '../models/StatClient'

export default class StatClientRepo {
  //Create
  public static async create(
    statClient: StatClientDocument
  ): Promise<StatClientDocument> {
    const newStatClient = await StatClient.create(statClient)
    return newStatClient
  }

  //Find
  public static async find(obj: Object, query: any) {
    const features = new APIFeatures(
      StatClient.find(obj).populate('kpis'),
      query
    )
      .filter()
      .sort()
      .limitFields()

    const options = {
      query: features.query,
      limit: query.limit,
      page: query.page,
    }
    return await StatClient.paginate(options)
  }

  //Find One
  public static async findOne(obj: object): Promise<StatClientDocument | null> {
    return await StatClient.findOne(obj).populate('kpis')
  }

  //Find One And Update
  public static async findOneAndUpdate(
    filter: object,
    update: object
  ): Promise<StatClientDocument | null> {
    return await StatClient.findOneAndUpdate(filter, update, {
      new: true,
    })
  }

  //count
  public static async count(obj: Object) {
    return await StatClient.count(obj)
  }

  //Update Many
  public static async updateMany(filter: Object, update: Object) {
    return await StatClient.updateMany(filter, update)
  }
}
