import { superAdminCredentials } from '../../config'
import APIFeatures from '../../helpers/ApiFeatures'
import PermissionGroup, {
  PermissionGroupDocument,
} from '../models/PermissionGroup'

export default class PermissionGroupRepo {
  //Create
  public static async create(
    permissionGroup: PermissionGroupDocument
  ): Promise<PermissionGroupDocument> {
    const newPermissionGroup = await PermissionGroup.create(permissionGroup)
    return newPermissionGroup
  }

  //Find
  public static async find(obj: Object, query: any) {
    const { superAdminGroupName } = superAdminCredentials
    const features = new APIFeatures(
      PermissionGroup.find({
        ...obj,
        name: { $ne: superAdminGroupName },
      }).populate('permissions'),
      query
    )
      .filter()
      .sort()
      .limitFields()

    const options = {
      query: features.query,
      limit: query.limit,
      page: query.page,
    }
    return await PermissionGroup.paginate(options)
  }

  //Find One And Update
  public static async findOneAndUpdate(
    query: object,
    obj: object
  ): Promise<PermissionGroupDocument | null> {
    const { superAdminGroupName } = superAdminCredentials
    return await PermissionGroup.findOneAndUpdate(
      { ...query, name: { $ne: superAdminGroupName } },
      obj,
      {
        new: true,
      }
    ).populate('permissions')
  }

  //Find One
  public static async findOne(
    obj: object
  ): Promise<PermissionGroupDocument | null> {
    const { superAdminGroupName } = superAdminCredentials
    return await PermissionGroup.findOne({
      ...obj,
      name: { $ne: superAdminGroupName },
    }).populate('permissions')
  }

  //Find One
  public static async findOneSuperAdmin(
    obj: object
  ): Promise<PermissionGroupDocument | null> {
    return await PermissionGroup.findOne(obj).populate('permissions')
  }

  // Delete One
  public static async deleteOne(permissionGroup: any): Promise<any> {
    let name = permissionGroup.name
    const deletedGroups = await PermissionGroup.count({
      name: { $regex: /^old[0-9]+g/ },
    })
    await PermissionGroup.updateOne(
      { _id: permissionGroup._id },
      {
        $set: {
          name: `old${deletedGroups}${name}`,
          deletedAt: new Date(),
          deleted: true,
        },
      }
    )
  }

  //COUNT
  public static async count(filter: object) {
    return await PermissionGroup.count(filter)
  }
}
