import { FilterQuery } from "mongoose";
import APIFeatures from "../../helpers/ApiFeatures";
import { Book, BookDocument } from "../models/Book";
export default class BookRepo {
  //Create
  public static async create(
    book: Partial<BookDocument>
  ): Promise<BookDocument> {
    const newBook = await Book.create(book);
    return newBook;
  }

  //Find One
  public static async findOne(
    book: FilterQuery<BookDocument>
  ): Promise<BookDocument | null> {
    return await Book.findOne(book)
      .populate("section", "name")
      .populate("level", "name");
  }

  //Find with filter
  public static async find(obj: Object, query: any) {
    const features = new APIFeatures(
      Book.find(obj).populate("level", "name").populate("section", "name"),
      query
    )
      .filter()
      .sort()
      .limitFields()
      .search(["name"]);

    const options = {
      query: features.query,
      limit: query.limit || 10,
      page: query.page || 1,
    };

    return await Book.paginate(options);
  }

  //Find One And Update
  public static async findOneAndUpdate(
    book: FilterQuery<BookDocument>,
    update: Partial<BookDocument>
  ) {
    return await Book.findOneAndUpdate(book, update, {
      new: true,
    });
  }

  //Delete one
  public static async deleteOne(filter: object) {
    return await Book.findOneAndUpdate(filter, [
      {
        $set: {
          deletedAt: new Date(),
          deleted: true,
          name: {
            $concat: [
              {
                $dateToString: {
                  format: "%Y-%m-%d_%H-%M-%S", // Format for year-month-day_hour-minute-second
                  date: new Date(),
                },
              },
              "__",
              "$name",
            ],
          },
        },
      },
    ]);
  }
}
