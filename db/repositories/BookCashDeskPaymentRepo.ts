import { FilterQuery, Pi<PERSON>ineStage } from "mongoose";
import APIFeatures from "../../helpers/ApiFeatures";
import {
  BookCashDeskPayment,
  BookCashDeskPaymentDocument,
} from "../models/BookCashDeskPayment";

export default class BookCashDeskPaymentRepo {
  //Create
  public static async create(
    bookCashDeskPayment: Partial<BookCashDeskPaymentDocument>
  ): Promise<BookCashDeskPaymentDocument> {
    const newBookCashDeskPayment = await BookCashDeskPayment.create(
      bookCashDeskPayment
    );
    return newBookCashDeskPayment;
  }

  //Find + Pagination
  public static async find(
    bookCashDeskPayment: FilterQuery<BookCashDeskPaymentDocument>
  ) {
    const features = new APIFeatures(
      BookCashDeskPayment.find({}).populate([
        {
          path: "cashedTo",
          select: "username name",
        },
        {
          path: "cashedBy",
          select: "username name",
        },
        {
          path: "office",
          select: "name",
        },
      ]),
      bookCashDeskPayment
    )
      .filter()
      .sort()
      .limitFields();

    const options = {
      query: features.query,
      limit: bookCashDeskPayment.limit || 10,
      page: bookCashDeskPayment.page || 1,
    };
    return await BookCashDeskPayment.paginate(options);
  }

  //Find One
  public static async findOne(
    filter: FilterQuery<BookCashDeskPaymentDocument>
  ): Promise<BookCashDeskPaymentDocument | null> {
    return await BookCashDeskPayment.findOne(filter).populate([
      {
        path: "cashedTo",
        select: "username name",
      },
      {
        path: "cashedBy",
        select: "username name",
      },
      {
        path: "office",
        select: "name",
      },
    ]);
  }

  // find one and update
  public static async findOneAndUpdate(
    filter: object,
    bookCashDeskPayment: Partial<BookCashDeskPaymentDocument>
  ) {
    return await BookCashDeskPayment.findOneAndUpdate(
      filter,
      bookCashDeskPayment,
      {
        new: true,
      }
    );
  }

  //Delete one
  public static async deleteOne(filter: object) {
    return await BookCashDeskPayment.findOneAndDelete(filter);
  }

  //Aggregation
  public static async aggregate(pipeline: PipelineStage[]) {
    return await BookCashDeskPayment.aggregate(pipeline);
  }

  // Total Amount
  public static async totalPayments(
    bookCashDeskPayment: FilterQuery<BookCashDeskPaymentDocument>
  ) {
    return await BookCashDeskPayment.aggregate([
      { $match: bookCashDeskPayment },
      {
        $group: {
          _id: "office",
          totalAmount: {
            $sum: "$amount",
          },
        },
      },
    ]);
  }

  // Total payments per office
  public static async totalPaymentsPerOffice(
    bookCashDeskPayment: FilterQuery<BookCashDeskPaymentDocument>
  ) {
    return await BookCashDeskPayment.aggregate([
      {
        $match: bookCashDeskPayment,
      },
      {
        $group: {
          _id: "$office",
          totalPayments: {
            $sum: "$amount",
          },
        },
      },
      {
        $lookup: {
          from: "offices",
          localField: "_id",
          foreignField: "_id",
          as: "office",
        },
      },
      {
        $unwind: {
          path: "$office",
        },
      },
      {
        $project: {
          _id: 0,
          office: "$office.name",
          totalPayments: 1,
        },
      },
    ]);
  }
}
