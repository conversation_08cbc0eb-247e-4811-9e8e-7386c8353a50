import mongoose, { Types } from "mongoose";
import MongooseDelete from "mongoose-delete";
import { mongoosePagination, Pagination } from "mongoose-paginate-ts";

export const DEPARTMENT_DOCUMENT_NAME = "Department";

export interface DepartmentDocument extends mongoose.Document {
  name: string;
  description?: string;
  createdAt?: Date;
  updatedAt?: Date;
  deletedAt?: Date;
}

const departmentSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: [true, "Please tell us department name"],
    },
    description: {
      type: String,
    },
    deletedAt: {
      type: Date,
      default: null,
    },
  },
  { versionKey: false, timestamps: true }
);

departmentSchema.plugin(mongoosePagination);

departmentSchema.plugin(MongooseDelete, {
  overrideMethods: "all",
  indexFields: true,
});

const Department = mongoose.model<
  DepartmentDocument,
  Pagination<DepartmentDocument>
>(DEPARTMENT_DOCUMENT_NAME, departmentSchema);
export default Department;
