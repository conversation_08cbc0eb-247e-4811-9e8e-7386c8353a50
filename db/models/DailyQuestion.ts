import mongoose, { Types } from "mongoose";
import MongooseDelete from "mongoose-delete";
import { mongoosePagination, Pagination } from "mongoose-paginate-ts";
import { DailyKpiDocument } from "./DailyKpi";
import { KpiDocument } from "./Kpi";
import { UserDocument } from "./User";

export interface DailyQuestionDocument extends mongoose.Document {
  name: string;
  dailyKpis: Array<Types.ObjectId | DailyKpiDocument>;
  deletedAt: Date;
}

const dailyQuestionSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: [true, "Please tell us stat client name"],
    },
    dailyKpis: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "DailyKpi",
      },
    ],
    deletedAt: {
      type: Date,
      default: null,
    },
  },
  { versionKey: false, timestamps: true }
);

dailyQuestionSchema.plugin(mongoosePagination);

dailyQuestionSchema.plugin(MongooseDelete, {
  overrideMethods: "all",
  indexFields: true,
});

const DailyQuestion = mongoose.model<
  DailyQuestionDocument,
  Pagination<DailyQuestionDocument>
>("DailyQuestion", dailyQuestionSchema);
export default DailyQuestion;
