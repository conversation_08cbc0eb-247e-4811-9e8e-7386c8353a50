import mongoose from "mongoose";
import MongooseDelete from "mongoose-delete";
import { mongoosePagination, Pagination } from "mongoose-paginate-ts";

export enum ModelCode {
  USER = "USER",
  PERMISSION = "PERMISSION",
  PERMISSION_GROUP = "PERMISSION_GROUP",
  KPI = "K<PERSON>",
  DAILY_KPI = "DAILY_K<PERSON>",
  WORKTIME = "WORKTIME",
  CALL = "CALL",
  MY_WORKTIME = "MY_WORKTIME",
  STAT_CLIENT = "STAT_CLIENT",
  STAT_CLIENT_RESPONSE = "STAT_CLIENT_RESPONSE",
  NOTE = "NOTE",
  TAG = "TAG",
  ACCESS_USER_CALL = "ACESS_USER_CALL",
  ANALYTICS = "ANALYTICS",
  NOTIFICATION = "NOTIFICATION",
  TODO = "TODO",
  OFFICE = "OFFICE",
  FOLDER = "FOLDER",
  CODE = "CODE",
  BALANCE_ANALYTICS = "BALANCE_ANALYTICS",
  CASH_DESK_PAYMENT = "CASH_DESK_PAYMENT",
  OPERATION = "OPERATION",
  SESSION = "SESSION",
  LOGOUT_ADMIN = "LOGOUT_ADMIN",
  FILE_UPLOAD = "FILE_UPLOAD",
  AGENT_PERFORMANCE = "AGENT_PERFORMANCE",
  BONUS_AMOUNT = "BONUS_AMOUNT",
  PDF_CONTENT = "PDF_CONTENT",
  PAYMENT_NOTE = "PAYMENT_NOTE",
  SECTION = "SECTION",
  LEVEL = "LEVEL",
  BOOK = "BOOK",
  BOOK_STOCK = "BOOK_STOCK",
  BOOK_SALE_TRANSACTION = "BOOK_SALE_TRANSACTION",
  DEPARTMENT = "DEPARTMENT",
  DEPARTMENT_WORKTIME = "DEPARTMENT_WORKTIME",
  BOOKS_ANALYTICS = "BOOKS_ANALYTICS",
  BOOK_CASH_DESK_PAYMENT = "BOOK_CASH_DESK_PAYMENT",
  DAILY_RESPONSE = "DAILY_RESPONSE",
}

export enum MethodCode {
  CREATE = "CREATE",
  LIST = "LIST",
  VIEW = "VIEW",
  EDIT = "EDIT",
  DELETE = "DELETE",
}

export interface PermissionDocument extends mongoose.Document {
  model: string;
  method: string;
  deletedAt: Date;
}

const permissionSchema = new mongoose.Schema(
  {
    model: {
      type: String,
      required: [true, "Please tell us permission model"],
      enum: [ModelCode],
    },
    method: {
      type: String,
      required: [true, "Please tell us permission method"],
      enum: [MethodCode],
    },
    deletedAt: {
      type: Date,
      default: null,
    },
  },
  { versionKey: false, timestamps: true }
);

permissionSchema.plugin(mongoosePagination);

permissionSchema.plugin(MongooseDelete, {
  overrideMethods: "all",
  indexFields: true,
});

const Permission = mongoose.model<
  PermissionDocument,
  Pagination<PermissionDocument>
>("Permission", permissionSchema);
export default Permission;
