import mongoose, { Types } from 'mongoose'
import MongooseDelete from 'mongoose-delete'
import { mongoosePagination, Pagination } from 'mongoose-paginate-ts'
import { KpiDocument } from './Kpi'
import { UserDocument } from './User'

export interface StatClientDocument extends mongoose.Document {
  name: string
  kpis: Array<Types.ObjectId | KpiDocument>
  deletedAt: Date
}

const statClientSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: [true, 'Please tell us stat client name'],
    },
    kpis: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Kpi',
      },
    ],
    deletedAt: {
      type: Date,
      default: null,
    },
  },
  { versionKey: false, timestamps: true }
)

statClientSchema.plugin(mongoosePagination)

statClientSchema.plugin(MongooseDelete, {
  overrideMethods: 'all',
  indexFields: true,
})

const StatClient = mongoose.model<
  StatClientDocument,
  Pagination<StatClientDocument>
>('StatClient', statClientSchema)
export default StatClient
