import mongoose from 'mongoose'
import MongooseDelete from 'mongoose-delete'
import { mongoosePagination, Pagination } from 'mongoose-paginate-ts'
import { PermissionDocument } from './Permission'

export interface PermissionGroupDocument extends mongoose.Document {
  name: string
  permissions: PermissionDocument[]
  deletedAt: Date
}

const permissionGroupSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: [true, 'Please provide permission group name'],
      unique: true,
    },
    permissions: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Permission',
      },
    ],
    deletedAt: {
      type: Date,
      default: null,
    },
  },
  { versionKey: false, timestamps: true }
)

permissionGroupSchema.plugin(mongoosePagination)

permissionGroupSchema.plugin(MongooseDelete, {
  deletedAt: true,
  overrideMethods: [
    'find',
    'findOne',
    'findOneAndUpdate',
    'updateOne',
    'aggregate',
    'updateMany',
    'update',
  ],
  indexFields: true,
})

const PermissionGroup = mongoose.model<
  PermissionGroupDocument,
  Pagination<PermissionGroupDocument>
>('PermissionGroup', permissionGroupSchema)
export default PermissionGroup
