import { Document, model, Schema } from "mongoose";
import MongooseDelete from "mongoose-delete";
import { mongoosePagination, Pagination } from "mongoose-paginate-ts";

export enum FrontType {
  TEXTAREA = "textarea",
  RADIO = "radio",
  CHECKBOX = "checkbox",
  SELECT = "select",
  INPUT = "input",
  SWITCH = "switch",
}

export interface DailyKpiDocument extends Document {
  name: string;
  label: string;
  frontType: FrontType;
  choices?: string[];
  isRequired: boolean;
  createdAt?: Date;
  deletedAt?: Date;
  updatedAt?: Date;
}

const dailyKpiSchema = new Schema<DailyKpiDocument>(
  {
    name: {
      type: String,
      unique: true,
    },
    label: { type: String },
    frontType: {
      type: String,
      enum: [FrontType],
    },
    choices: { type: Schema.Types.Mixed },
    isRequired: { type: Boolean, default: false },
    deletedAt: {
      type: Date,
      default: null,
    },
  },
  { versionKey: false, timestamps: true }
);

dailyKpiSchema.plugin(mongoosePagination);

dailyKpiSchema.plugin(MongooseDelete, {
  deletedAt: true,
  overrideMethods: "all",
  indexFields: true,
});

const DailyKpi = model<DailyKpiDocument, Pagination<DailyKpiDocument>>(
  "DailyKpi",
  dailyKpiSchema
);
export default DailyKpi;
