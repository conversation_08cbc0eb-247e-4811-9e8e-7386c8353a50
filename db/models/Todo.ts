import { NextFunction } from "express";
import { HydratedDocument, model, Schema, Types } from "mongoose";
import MongooseDelete from "mongoose-delete";
import { mongoosePagination, Pagination } from "mongoose-paginate-ts";
import { UserDocument, USER_DOCUMENT_NAME } from "./User";

export const TODO_DOCUMENT_NAME = "Todo";
export const TODO_COLLECTION_NAME = "todos";

export enum TodoStatus {
  TODO = "todo",
  COMPLETED = "completed",
}
export enum TodoPriority {
  URGENT = "urgent",
  HIGH = "high",
  NORMAL = "normal",
  LOW = "low",
  NONE = "none",
}
export interface TodoDocument extends Document {
  _id: Types.ObjectId;
  author: Types.ObjectId;
  description: string;
  status: TodoStatus;
  priority: TodoPriority;
  mentions: (Types.ObjectId | UserDocument)[];
  completedAt: Date;
  createdAt?: Date;
  deletedAt?: Date;
  updatedAt?: Date;
  seenBy: UserDocument[];
  completedBy: Types.ObjectId;
}

const todoSchema = new Schema<TodoDocument>(
  {
    author: { type: Types.ObjectId, ref: USER_DOCUMENT_NAME, required: true },
    description: { type: String },
    status: { type: String, enum: TodoStatus, default: TodoStatus.TODO },
    priority: {
      type: String,
      enum: TodoPriority,
      default: TodoPriority.NONE,
    },
    mentions: [{ type: Types.ObjectId }],
    seenBy: [{ type: Types.ObjectId, ref: USER_DOCUMENT_NAME }],
    completedAt: {
      type: Date,
      default: null,
    },
    completedBy: { type: Types.ObjectId, default: null },
    deletedAt: {
      type: Date,
      default: null,
    },
  },
  { versionKey: false, timestamps: true }
);

// Pre-save hook to update completedAt field
todoSchema.pre<HydratedDocument<TodoDocument>>(
  "findOneAndUpdate",
  function (this: any, next: NextFunction) {
    const update = this.getUpdate();
    if (update.status === TodoStatus.COMPLETED) {
      this.update({}, { $set: { completedAt: new Date() } });
    } else if (update.status === TodoStatus.TODO) {
      this.update({}, { $set: { completedAt: null } });
    }
    next();
  }
);

todoSchema.plugin(mongoosePagination);

todoSchema.plugin(MongooseDelete, {
  overrideMethods: "all",
  indexFields: true,
});

export const Todo = model<TodoDocument, Pagination<TodoDocument>>(
  TODO_DOCUMENT_NAME,
  todoSchema,
  TODO_COLLECTION_NAME
);
