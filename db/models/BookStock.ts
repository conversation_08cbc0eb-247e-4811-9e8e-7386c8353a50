import { Document, model, Schema } from "mongoose";
import MongooseDelete from "mongoose-delete";
import { mongoosePagination, Pagination } from "mongoose-paginate-ts";
import { BookDocument } from "./Book";
import { OfficeDocument } from "./Office";

export interface BookStockDocument extends Document {
  book: BookDocument;
  office: OfficeDocument;
  quantity: number;
  createdAt: Date;
  updatedAt: Date;
}
export const DOCUMENT_NAME = "BookStock";

const bookStockSchema = new Schema(
  {
    book: { type: Schema.Types.ObjectId, ref: "Book", required: true },
    office: { type: Schema.Types.ObjectId, ref: "Office", required: true },
    quantity: { type: Number, required: true },
  },
  {
    versionKey: false,
    timestamps: true,
  }
);

bookStockSchema.index({ book: 1, office: 1 }, { unique: true });

bookStockSchema.plugin(mongoosePagination);
bookStockSchema.plugin(MongooseDelete, {
  overrideMethods: "all",
  indexFields: true,
});

export const BookStock = model<
  BookStockDocument,
  Pagination<BookStockDocument>
>(DOCUMENT_NAME, bookStockSchema);
