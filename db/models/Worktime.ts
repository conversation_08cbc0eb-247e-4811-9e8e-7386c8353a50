import { Document, model, Schema, Types } from "mongoose";
import MongooseDelete from "mongoose-delete";
import { mongoosePagination, Pagination } from "mongoose-paginate-ts";
import { USER_DOCUMENT_NAME } from "./User";

export enum WorktimeType {
  WORK = "work",
  LEAVE = "leave",
  AUTHORIZATION = "authorization",
  REPOS = 'repos'
}
export interface WorktimeDocument extends Document {
  startDate: Date;
  endDate: Date;
  title?: string;
  userId: String;
  type?: WorktimeType;
  createdAt?: Date;
  updatedAt?: Date;
  deletedAt?: Date;
}

const worktimeSchema = new Schema<WorktimeDocument>(
  {
    startDate: { type: Date },
    endDate: { type: Date },
    title: { type: String },
    userId: { type: Types.ObjectId, ref: "User", required: true },
    type: { type: String, enum: WorktimeType, default: WorktimeType.WORK },
    createdAt: {
      type: Date,
      required: true,
      select: false,
    },
    updatedAt: {
      type: Date,
      required: true,
      select: false,
    },
    deletedAt: {
      type: Date,
      default: null,
    },
  },
  { versionKey: false, timestamps: true }
);

worktimeSchema.plugin(mongoosePagination);
worktimeSchema.plugin(MongooseDelete, {
  overrideMethods: "all",
  indexFields: true,
});

export const Worktime = model<WorktimeDocument, Pagination<WorktimeDocument>>(
  "Worktime",
  worktimeSchema,
  "worktimes"
);
