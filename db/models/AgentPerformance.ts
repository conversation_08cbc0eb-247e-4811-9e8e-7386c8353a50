import { Document, model, Schema, Types } from "mongoose";
import MongooseDelete from "mongoose-delete";
import { mongoosePagination, Pagination } from "mongoose-paginate-ts";
import { UserDocument, USER_DOCUMENT_NAME } from "./User";

export const AGENT_PERFORMANCE_DOCUMENT_NAME = "AgentMetric";
export const AGENT_PERFORMANCE_COLLECTION_NAME = "agentMetrics";
export interface AgentPerformanceDocument extends Document {
  user: UserDocument;
  date: Date;
  seniority: number;
  lateness: number;
  leave: number;
  authorization: number;
  calls: number;
  statClientResponses: number;
  dailyResponses: number;
  todos: number;
  eliminationFault: boolean;
  tam: boolean;
  notes: string;
  bonusAmountPct: number;
  bonusAmountDT: number;
  deletedAt: Date;
  createdAt: Date;
  updatedAt: Date;
}

const AgentPerformanceSchema = new Schema<AgentPerformanceDocument>(
  {
    user: {
      type: Types.ObjectId,
      ref: USER_DOCUMENT_NAME,
    },
    date: {
      type: Date,
    },
    seniority: {
      type: Number,
    },
    lateness: { type: Number },
    leave: { type: Number },
    authorization: { type: Number },
    calls: { type: Number },
    statClientResponses: { type: Number },
    dailyResponses: { type: Number },
    todos: { type: Number },
    eliminationFault: { type: Boolean },
    tam: { type: Boolean },
    notes: { type: String },
    bonusAmountPct: { type: Number },
    bonusAmountDT: { type: Number },
    deletedAt: {
      type: Date,
      default: null,
    },
  },
  {
    versionKey: false,
    timestamps: true,
  }
);

AgentPerformanceSchema.plugin(mongoosePagination);
AgentPerformanceSchema.plugin(MongooseDelete, {
  overrideMethods: "all",
  indexFields: true,
});

export const AgentPerformance = model<
  AgentPerformanceDocument,
  Pagination<AgentPerformanceDocument> &
    MongooseDelete.SoftDeleteModel<AgentPerformanceDocument>
>(
  AGENT_PERFORMANCE_DOCUMENT_NAME,
  AgentPerformanceSchema,
  AGENT_PERFORMANCE_COLLECTION_NAME
);
