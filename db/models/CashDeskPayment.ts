import { Document, model, Schema, Types } from "mongoose";
import MongooseDelete from "mongoose-delete";
import { mongoosePagination, Pagination } from "mongoose-paginate-ts";
import { OfficeDocument } from "./Office";
import { UserDocument } from "./User";

export const DOCUMENT_NAME = "CashDeskPayment";
export interface CashDeskPaymentDocument extends Document {
  _id: Types.ObjectId;
  amount: number;
  cashedTo: UserDocument;
  cashedBy: UserDocument;
  office: OfficeDocument;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

const CashDeskPaymentSchema = new Schema(
  {
    amount: {
      type: Number,
      required: true,
    },
    cashedTo: {
      type: Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    office: {
      type: Schema.Types.ObjectId,
      ref: "Office",
      required: true,
    },
    notes: {
      type: String,
      default: null,
    },
    cashedBy: {
      type: Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
  },
  { versionKey: false, timestamps: true }
);

CashDeskPaymentSchema.plugin(mongoosePagination);

CashDeskPaymentSchema.plugin(MongooseDelete, {
  overrideMethods: "all",
  deletedAt: true,
  indexFields: true,
});

export const CashDeskPayment = model<
  CashDeskPaymentDocument,
  Pagination<CashDeskPaymentDocument>
>(DOCUMENT_NAME, CashDeskPaymentSchema);
