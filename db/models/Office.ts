import mongoose, { Types } from "mongoose";
import MongooseDelete from "mongoose-delete";
import { mongoosePagination, Pagination } from "mongoose-paginate-ts";

export interface OfficeDocument extends mongoose.Document {
  name: string;
  officeId: number;
  address?: string;
  ipAddress?: string;
  createdAt?: Date;
  updatedAt?: Date;
  deletedAt?: Date;
}

export const OFFICE_DOCUMENT_NAME = "Office";

const officeSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: [true, "Please tell us office name"],
    },
    officeId: {
      type: Number,
    },
    address: {
      type: String,
    },
    ipAddress: {
      type: String,
      default: null,
    },
    deletedAt: {
      type: Date,
      default: null,
    },
  },
  { versionKey: false, timestamps: true }
);

officeSchema.plugin(mongoosePagination);

officeSchema.plugin(MongooseDelete, {
  overrideMethods: "all",
  indexFields: true,
});

const Office = mongoose.model<OfficeDocument, Pagination<OfficeDocument>>(
  OFFICE_DOCUMENT_NAME,
  officeSchema
);
export default Office;
