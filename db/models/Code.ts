import mongoose, { Schema, Types } from "mongoose";
import MongooseDelete from "mongoose-delete";
import { mongoosePagination, Pagination } from "mongoose-paginate-ts";
import { OfficeDocument } from "./Office";
import { UserDocument, USER_DOCUMENT_NAME } from "./User";
export const CODE_DOCUMENT_NAME = "Code";

export interface CodeDocument extends mongoose.Document {
  code: string;
  paymentMethod: string;
  admin: UserDocument;
  office: OfficeDocument;
  clientName?: string;
  clientPhoneNumber?: string;
  clientLevel?: string;
  clientOffer?: string;
  checkNumber?: string;
  checkDate?: string;
  checkPayed?: boolean;
  checkPayable?: boolean;
  isPayed: boolean;
  paymentNote: Schema.Types.ObjectId | null;
  paymentNoteOther: string | null;
  notes?: string;
  payedAt: Date;
  billOfExchangeNum?: string | null;
  billOfExchangeName?: string | null;
  billOfExchangePhone?: string | null;
  billOfExchangeDueDate: Date | null;
  billOfExchangePayed: boolean | null;
  deleted: boolean;
  createdAt: Date;
  updatedAt: Date;
  deletedAt: Date;
}

export enum PaymentMethod {
  CASH = "cash",
  CHECK = "check",
  BILL_OF_EXCHANGE = "billOfExchange",
}

const codeSchema = new mongoose.Schema(
  {
    clientName: {
      type: String,
      default: null,
    },
    clientPhoneNumber: {
      type: String,
      default: null,
    },
    clientLevel: {
      type: String,
      default: null,
    },
    clientOffer: {
      type: String,
      default: null,
    },
    paymentMethod: {
      type: String,
      required: [true, "Please tell us paymentMethod offer"],
    },
    amount: {
      type: Number,
      required: [true, "Please tell us amount"],
    },
    code: {
      type: String,
      required: [true, "Please tell us code"],
    },
    checkNumber: {
      type: String,
      default: null,
    },
    checkDate: {
      type: Date,
      default: null,
    },
    checkPayed: {
      type: Boolean,
      default: false,
    },
    checkPayable: {
      type: Boolean,
      default: true,
    },
    isPayed: {
      type: Boolean,
      default: true,
    },
    paymentNote: {
      type: Schema.Types.ObjectId || null,
      ref: "PaymentNote",
      default: null,
    },
    paymentNoteOther: {
      type: String || null,
      default: null,
    },
    notes: {
      type: String,
      default: null,
    },
    deleted: {
      type: Boolean,
      default: false,
    },
    deletedAt: {
      type: Date,
      default: null,
    },
    admin: {
      type: Types.ObjectId,
      ref: USER_DOCUMENT_NAME,
    },
    office: {
      type: Types.ObjectId,
      ref: "Office",
    },
    payedAt: {
      type: Date,
      default: function () {
        return this.createdAt;
      },
    },
    // bill of exchange
    billOfExchangeNum: {
      type: String,
      default: null,
    },
    billOfExchangeName: {
      type: String,
      default: null,
    },
    billOfExchangePhone: {
      type: String,
      default: null,
    },
    billOfExchangeDueDate: {
      type: Date,
      default: null,
    },
    billOfExchangePayed: {
      type: Boolean,
      default: null,
    },
  },
  { versionKey: false, timestamps: true }
);

// Indexes
codeSchema.index({ code: 1 });
codeSchema.index({ office: 1, payedAt: 1, createdAt: 1 });
codeSchema.index({ paymentMethod: 1, isPayed: 1 });
codeSchema.index({ checkPayable: 1, checkPayed: 1 });
codeSchema.index({ paymentMethod: 1, office: 1 });
codeSchema.index({ amount: 1 });
codeSchema.index({ clientName: "text", notes: "text" });

codeSchema.plugin(mongoosePagination);

codeSchema.plugin(MongooseDelete, {
  overrideMethods: "all",
  indexFields: true,
});

const Code = mongoose.model<CodeDocument, Pagination<CodeDocument>>(
  CODE_DOCUMENT_NAME,
  codeSchema
);
export default Code;
