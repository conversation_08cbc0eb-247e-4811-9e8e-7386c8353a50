import { Document, model, Schema } from "mongoose";
import MongooseDelete from "mongoose-delete";
import { mongoosePagination, Pagination } from "mongoose-paginate-ts";

export enum FrontType {
  TEXTAREA = "textarea",
  RADIO = "radio",
  CHECKBOX = "checkbox",
  SELECT = "select",
  INPUT = "input",
  SWITCH = "switch",
}

export interface KpiDocument extends Document {
  name: string;
  label: string;
  frontType: FrontType;
  choices?: string[];
  isRequired: boolean;
  createdAt?: Date;
  deletedAt?: Date;
  updatedAt?: Date;
}

const kpiSchema = new Schema<KpiDocument>(
  {
    name: {
      type: String,
      unique: true,
    },
    label: { type: String },
    frontType: {
      type: String,
      enum: [FrontType],
    },
    choices: { type: Schema.Types.Mixed },
    isRequired: { type: Boolean, default: false },
    deletedAt: {
      type: Date,
      default: null,
    },
  },
  { versionKey: false, timestamps: true }
);

kpiSchema.plugin(mongoosePagination);

kpiSchema.plugin(MongooseDelete, {
  deletedAt: true,
  overrideMethods: "all",
  indexFields: true,
});

const Kpi = model<KpiDocument, Pagination<KpiDocument>>(
  "Kpi",
  kpiSchema,
  "kpis"
);
export default Kpi;
