import mongoose, { Schema, Types } from "mongoose";
import MongooseDelete from "mongoose-delete";
import { mongoosePagination, Pagination } from "mongoose-paginate-ts";
import { FILE_DOCUMENT_NAME } from "./File";
import { OFFICE_DOCUMENT_NAME } from "./Office";
import { UserDocument } from "./User";

export interface FolderDocument extends Document {
  name: string;
  user: Types.ObjectId | UserDocument;
  parentFolder: Types.ObjectId[];
  subFolder: Types.ObjectId[];
  files: Types.ObjectId[];
  path: string;
}

const FolderSchema = new Schema(
  {
    name: {
      type: String,
      min: [2, "Title must be at least 2 characters long."],
    },
    user: {
      type: Schema.Types.ObjectId,
      ref: "User",
    },
    parentFolder: {
      type: Schema.Types.ObjectId,
      ref: "Folder",
    },
    subFolder: {
      type: [Schema.Types.ObjectId],
      ref: "Folder",
    },
    files: {
      type: [Schema.Types.ObjectId],
      ref: FILE_DOCUMENT_NAME,
    },
    path: {
      type: String,
      required: true,
    },
    show: {
      type: [Schema.Types.ObjectId],
      ref: OFFICE_DOCUMENT_NAME,
      default: null,
    },
    deletedAt: {
      type: Date,
      default: null,
    },
  },
  {
    timestamps: true,
  }
);
FolderSchema.plugin(mongoosePagination);
FolderSchema.plugin(MongooseDelete, {
  overrideMethods: "all",
  indexFields: true,
});
export const Folder = mongoose.model<
  FolderDocument,
  Pagination<FolderDocument>
>("Folder", FolderSchema);
