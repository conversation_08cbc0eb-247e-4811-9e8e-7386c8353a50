import mongoose, { model, Schema, Document } from "mongoose";
import MongooseDelete from "mongoose-delete";
import { mongoosePagination, Pagination } from "mongoose-paginate-ts";
import { PermissionDocument } from "./Permission";
import { PermissionGroupDocument } from "./PermissionGroup";
import { OfficeDocument } from "./Office";
import { DepartmentDocument, DEPARTMENT_DOCUMENT_NAME } from "./Department";

export const USER_DOCUMENT_NAME = "User";
export const USER_COLLECTION_NAME = "users";

export const enum RoleCode {
  SUPER_ADMIN = "SUPER ADMIN",
  ADMIN = "ADMIN",
}

export interface UserDocument extends Document {
  username: string;
  name: string;
  email: string;
  avatar: string;
  office: OfficeDocument;
  role: RoleCode;
  deviceId: string | null;
  singleDevice: boolean;
  OAuthToken?: string;
  permissionGroup?: PermissionGroupDocument[];
  extraPermissions?: PermissionDocument[];
  isAuthorized: boolean;
  authorizedUntil: Date | null;
  createdAt: Date | null;
  updatedAt: Date | null;
  deletedAt: Date | null;
  firstDayWork: Date | null;
  hasAccess: Boolean;
  phone: String | null;
  department: DepartmentDocument;
  showAllCodes: boolean;
}

const userSchema = new Schema(
  {
    username: {
      type: String,
    },
    name: {
      type: String,
    },
    email: {
      type: String,
      lowercase: true,
    },
    office: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Office",
    },
    singleDevice: {
      type: Boolean,
      default: true,
    },
    isAuthorized: {
      type: Boolean,
      default: false,
    },
    authorizedUntil: {
      type: Date,
      default: null,
    },
    deviceId: {
      type: String,
      default: null,
    },
    role: {
      type: String,
      enum: [RoleCode.SUPER_ADMIN, RoleCode.ADMIN],
      default: RoleCode.ADMIN,
    },
    OAuthToken: {
      type: String,
    },
    permissionGroup: [
      {
        type: Schema.Types.ObjectId,
        ref: "PermissionGroup",
      },
    ],
    extraPermissions: [
      {
        type: Schema.Types.ObjectId,
        ref: "Permission",
      },
    ],
    deletedAt: {
      type: Date,
      default: null,
    },
    firstDayWork: {
      type: Date,
      default: null,
    },
    hasAccess: {
      type: Boolean,
      default: false,
    },
    phone: {
      type: String,
      default: null,
    },
    department: {
      type: Schema.Types.ObjectId,
      ref: DEPARTMENT_DOCUMENT_NAME,
    },
    showAllCodes: {
      type: Boolean,
      default: false,
    },
  },
  { versionKey: false, timestamps: true }
);

userSchema.plugin(mongoosePagination);

userSchema.plugin(MongooseDelete, {
  overrideMethods: "all",
  indexFields: true,
});

export const User = model<
  UserDocument,
  Pagination<UserDocument> & MongooseDelete.SoftDeleteModel<UserDocument>
>(USER_DOCUMENT_NAME, userSchema, USER_COLLECTION_NAME);
