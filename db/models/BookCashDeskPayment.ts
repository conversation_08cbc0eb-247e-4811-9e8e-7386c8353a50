import { Document, model, Schema, Types } from "mongoose";
import MongooseDelete from "mongoose-delete";
import { mongoosePagination, Pagination } from "mongoose-paginate-ts";
import { OfficeDocument } from "./Office";
import { UserDocument } from "./User";

export const DOCUMENT_NAME = "BookCashDeskPayment";

export interface BookCashDeskPaymentDocument extends Document {
  _id: Types.ObjectId;
  amount: number;
  cashedBy: UserDocument;
  cashedTo: UserDocument;
  office: OfficeDocument;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

const BookCashDeskPaymentSchema = new Schema(
  {
    amount: {
      type: Number,
      required: true,
    },
    cashedBy: {
      type: Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    cashedTo: {
      type: Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    office: {
      type: Schema.Types.ObjectId,
      ref: "Office",
      required: true,
    },
    notes: {
      type: String,
      default: null,
    },
  },
  { versionKey: false, timestamps: true }
);

BookCashDeskPaymentSchema.plugin(mongoosePagination);

BookCashDeskPaymentSchema.plugin(MongooseDelete, {
  overrideMethods: "all",
  deletedAt: true,
  indexFields: true,
});

export const BookCashDeskPayment = model<
  BookCashDeskPaymentDocument,
  Pagination<BookCashDeskPaymentDocument>
>(DOCUMENT_NAME, BookCashDeskPaymentSchema);
