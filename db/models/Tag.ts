import { string } from "joi";
import { Document, model, Schema } from "mongoose";
import MongooseDelete from "mongoose-delete";
import { mongoosePagination, Pagination } from "mongoose-paginate-ts";

export const DOCUMENT_NAME = "Tag";
export const COLLECTION_NAME = "tags";

export interface TagDocument extends Document {
  title: string;
  color: string;
}

const tagsSchema = new Schema(
  {
    title: {
      type: String,
      required: true,
    },
    color: {
      type: String,
      default: "#fff",
    },
  },
  {
    versionKey: false,
    timestamps: true,
    validateBeforeSave: true,
  }
);

tagsSchema.plugin(mongoosePagination);

tagsSchema.plugin(MongooseDelete, {
  overrideMethods: "all",
  indexFields: true,
});

export const Tag = model<
  TagDocument,
  Pagination<TagDocument> & MongooseDelete.SoftDeleteModel<TagDocument>
>(DOCUMENT_NAME, tagsSchema, COLLECTION_NAME);
