import { model, Schema, Types } from "mongoose";
import { mongoosePagination, Pagination } from "mongoose-paginate-ts";
import { FolderDocument } from "./Folder";
import { UserDocument } from "./User";

export const FILE_DOCUMENT_NAME = "File";
export const COLLECTION_NAME = "Files";

export interface FileDocument extends Document {
  name: string;
  path: string;
  originalName: string;
  folder: FolderDocument | string;
  addedBy: UserDocument | object;
  deleted?: boolean;
  deletedAt?: Date;
}

const FileSchema = new Schema(
  {
    addedBy: {
      type: Types.ObjectId,
      ref: "User",
      required: true,
    },
    name: {
      type: String,
      required: true,
      min: [2, "name must be longer then 2 charatere"],
    },
    originalName: {
      type: String,
      required: true,
      min: [2, "name must be longer then 2 charatere"],
    },
    folder: {
      type: Types.ObjectId,
      ref: "Folder",
    },
    type: {
      type: String,
      required: true,
    },
    path: {
      type: String,
    },
    size: {
      type: String,
    },
  },
  { versionKey: false, timestamps: true, new: true }
);
FileSchema.plugin(mongoosePagination);

export const File = model<FileDocument, Pagination<FileDocument>>(
  FILE_DOCUMENT_NAME,
  FileSchema,
  COLLECTION_NAME
);
