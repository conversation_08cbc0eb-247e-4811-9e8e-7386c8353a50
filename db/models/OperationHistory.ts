import mongoose, { Types } from "mongoose";
import { mongoosePagination, Pagination } from "mongoose-paginate-ts";
import { OfficeDocument } from "./Office";
import { OperationDocument, OperationType } from "./Operation";
import { UserDocument, USER_DOCUMENT_NAME } from "./User";
export const OPERATION_HISTORY_DOCUMENT_NAME = "OperationHistory";

export enum OperationHistoryAction {
  CREATE = "create",
  UPDATE = "update",
  DELETE = "delete",
}

export interface OperationHistoryDocument extends mongoose.Document {
  operation: OperationDocument;
  action: OperationHistoryAction;
  type: OperationType;
  amount: number;
  notes: string;
  office: OfficeDocument;
  admin: UserDocument | Object;
  createdBy: UserDocument | Object;
}

const operationHistorySchema = new mongoose.Schema(
  {
    operation: {
      type: Types.ObjectId,
      ref: OPERATION_HISTORY_DOCUMENT_NAME,
      required: [true, "Please tell us operation"],
    },
    action: {
      type: String,
      enum: OperationHistoryAction,
      required: [true, "Please tell us operation"],
    },
    type: {
      type: String,
      enum: OperationType,
    },
    amount: {
      type: Number,
      required: [true, "Please tell us amount"],
    },

    notes: {
      type: String,
      required: [true, "Please tell us notes"],
    },
    office: {
      type: Types.ObjectId,
      ref: "Office",
      required: [true, "Please tell us office"],
    },
    admin: {
      type: Types.ObjectId,
      ref: USER_DOCUMENT_NAME,
      required: [true, "Please tell us admin"],
    },
    createdBy: {
      type: Types.ObjectId,
      ref: USER_DOCUMENT_NAME,
      required: [true, "Please tell us admin"],
    },
  },
  { versionKey: false, timestamps: true }
);

operationHistorySchema.plugin(mongoosePagination);

const OperationHistory = mongoose.model<
  OperationHistoryDocument,
  Pagination<OperationHistoryDocument>
>(OPERATION_HISTORY_DOCUMENT_NAME, operationHistorySchema);
export default OperationHistory;
