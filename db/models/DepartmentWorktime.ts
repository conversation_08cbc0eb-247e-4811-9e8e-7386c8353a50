import mongoose from "mongoose";
import MongooseDelete, { SoftDeleteModel } from "mongoose-delete";
import { mongoosePagination, Pagination } from "mongoose-paginate-ts";
import { DepartmentDocument, DEPARTMENT_DOCUMENT_NAME } from "./Department";

export const DEPARTMENT_WORKTIME_DOCUMENT_NAME = "DepartmentWorktime";

export interface DepartmentWorktimeDocument extends mongoose.Document {
  department: DepartmentDocument[] | string[];
  startHour: Date;
  endHour: Date;
  startDate: number;
  endDate: number;
  createdAt?: Date;
  updatedAt?: Date;
  deletedAt?: Date;
}

const departmentWorktimeSchema = new mongoose.Schema(
  {
    department: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: DEPARTMENT_DOCUMENT_NAME,
      },
    ],
    startHour: {
      type: Date,
      required: true,
    },
    endHour: {
      type: Date,
      required: true,
    },
    startDate: {
      type: Date,
      required: true,
    },
    endDate: {
      type: Date,
      required: true,
    },
  },
  { versionKey: false, timestamps: true }
);

departmentWorktimeSchema.plugin(mongoosePagination);

departmentWorktimeSchema.plugin(MongooseDelete, {
  overrideMethods: "all",
  indexFields: true,
});

const DepartmentWorktime = mongoose.model<
  DepartmentWorktimeDocument,
  Pagination<DepartmentWorktimeDocument> &
    SoftDeleteModel<DepartmentWorktimeDocument>
>(DEPARTMENT_WORKTIME_DOCUMENT_NAME, departmentWorktimeSchema);
export default DepartmentWorktime;
