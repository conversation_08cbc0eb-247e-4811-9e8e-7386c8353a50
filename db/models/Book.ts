import { Document, model, Schema } from "mongoose";
import MongooseDelete from "mongoose-delete";
import { mongoosePagination, Pagination } from "mongoose-paginate-ts";
import { LevelDocument } from "./Level";
import { SectionDocument } from "./Section";

export interface BookDocument extends Document {
  name: string;
  description: string;
  level: LevelDocument;
  section?: SectionDocument | null;
  price: number;
  image: string;
  createdAt: Date;
  updatedAt: Date;
}
export const DOCUMENT_NAME = "Book";

const bookSchema = new Schema(
  {
    name: {
      type: String,
      required: true,
    },
    description: {
      type: String,
    },
    level: { type: Schema.Types.ObjectId, ref: "Level" },
    section: { type: Schema.Types.ObjectId, ref: "Section", default: null },
    image: {
      type: String,
    },
    price: { type: Number },
  },
  {
    versionKey: false,
    timestamps: true,
  }
);

bookSchema.plugin(mongoosePagination);
bookSchema.plugin(MongooseDelete, {
  overrideMethods: "all",
  indexFields: true,
});

export const Book = model<BookDocument, Pagination<BookDocument>>(
  DOCUMENT_NAME,
  bookSchema
);
