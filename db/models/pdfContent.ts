import { Document, model, Schema } from "mongoose";
import MongooseDelete from "mongoose-delete";
import { mongoosePagination, Pagination } from "mongoose-paginate-ts";

export const BONUS_AMOUNT_DOCUMENT_NAME = "PdfContent";
export interface pdfContentDocument extends Document {
  title: string;
  content: string;
  createdAt: Date;
  updatedAt: Date;
  deletedAt: Date;
}

const PdfContentSchema = new Schema<pdfContentDocument>(
  {
    title: { type: String, required: true, unique: true },
    content: { type: String, required: true },
    deletedAt: {
      type: Date,
      default: null,
    },
  },
  {
    versionKey: false,
    timestamps: true,
  }
);

PdfContentSchema.plugin(mongoosePagination);
PdfContentSchema.plugin(MongooseDelete, {
  overrideMethods: "all",
  indexFields: true,
});

export const PdfContent = model<
  pdfContentDocument,
  Pagination<pdfContentDocument> &
    MongooseDelete.SoftDeleteModel<pdfContentDocument>
>(BONUS_AMOUNT_DOCUMENT_NAME, PdfContentSchema);
