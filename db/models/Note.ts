import { string } from "joi";
import { Document, model, ObjectId, Schema, Types } from "mongoose";
import MongooseDelete from "mongoose-delete";
import { mongoosePagination, Pagination } from "mongoose-paginate-ts";
import { FILE_DOCUMENT_NAME } from "./File";
import { TagDocument } from "./Tag";
import { UserDocument } from "./User";

export const NOTE_DOCUMENT_NAME = "Note";
export const COLLECTION_NAME = "notes";
export interface NoteDocument extends Document {
  title: string;
  tags: TagDocument[];
  content: string;
  createdBy: UserDocument;
}

const NoteSchema = new Schema(
  {
    title: {
      type: String,
      unique: true,
      required: true,
    },
    tags: [
      {
        type: Schema.Types.ObjectId,
        ref: "Tag",
      },
    ],
    content: {
      type: String,
      required: true,
    },
    createdBy: {
      type: Types.ObjectId,
      ref: "User",
    },
    mentions: [{ type: Types.ObjectId }],
    files: {
      type: [Schema.Types.ObjectId],
      ref: FILE_DOCUMENT_NAME,
    },
    deletedAt: {
      type: Date,
      default: null,
    },
  },
  {
    versionKey: false,
    timestamps: true,
  }
);

NoteSchema.plugin(mongoosePagination);
NoteSchema.plugin(MongooseDelete, {
  overrideMethods: "all",
  indexFields: true,
});
export const Note = model<
  NoteDocument,
  Pagination<NoteDocument> & MongooseDelete.SoftDeleteModel<NoteDocument>
>(NOTE_DOCUMENT_NAME, NoteSchema, COLLECTION_NAME);
