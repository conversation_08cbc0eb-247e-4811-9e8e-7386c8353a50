import { Document, model, Schema } from "mongoose";
import MongooseDelete, { SoftDeleteModel } from "mongoose-delete";
import { mongoosePagination, Pagination } from "mongoose-paginate-ts";
import { BookDocument } from "./Book";
import { OfficeDocument } from "./Office";
import { UserDocument } from "./User";



export const DOCUMENT_NAME = "BookSaleTransaction";

export interface BookSaleDocument extends Document {
  book: BookDocument;
  quantitySold: number;
  salePrice: number;
}

export interface BookSaleTransactionDocument extends Document {
  office: OfficeDocument;
  items: [BookSaleDocument];
  totalAmount: number;
  note?: string;
  createdBy: UserDocument;
  createdAt: Date;
  updatedAt: Date;
}

const bookSaleSchema = new Schema(
  {
    book: {
      type: Schema.Types.ObjectId,
      ref: "Book",
      // required: true,
    }, // References the Book being sold
    quantitySold: {
      type: Number,
      required: true,
      min: 1,
    }, // Quantity of books sold
    salePrice: {
      type: Number,
      required: true,
      min: 0,
    }, // Price per unit at the time of sale
  },
  { _id: false, versionKey: false }
);

const bookSaleTransactionSchema = new Schema(
  {
    office: {
      type: Schema.Types.ObjectId,
      ref: "Office",
      // required: true,
    },
    items: [bookSaleSchema],
    totalAmount: {
      type: Number,
      required: true,
      min: 0,
    }, // Total amount for the transaction
    note: {
      type: String,
    },
    createdBy: { type: Schema.Types.ObjectId, ref: "User" },
  },
  {
    versionKey: false,
    timestamps: true,
  }
);

bookSaleTransactionSchema.plugin(mongoosePagination);
bookSaleTransactionSchema.plugin(MongooseDelete, {
  overrideMethods: "all",
  indexFields: true,
});

export const BookSaleTransaction = model<
  BookSaleTransactionDocument,
  Pagination<BookSaleTransactionDocument> &
    SoftDeleteModel<BookSaleTransactionDocument>
>(DOCUMENT_NAME, bookSaleTransactionSchema);
