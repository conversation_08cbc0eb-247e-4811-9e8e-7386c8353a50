import { Document, model, Schema } from "mongoose";
import { mongoosePagination, Pagination } from "mongoose-paginate-ts";
import { UserDocument } from "./User";

export interface CallDocument extends Document {
  user: UserDocument;
  date: Date | string;
  totalCalls: number;
  calls?: {
    maked: number;
    received: number;
  };
  createdAt: Date;
  updatedAt: Date;
}
export const DOCUMENT_NAME = "Call";
export const COLLECTION_NAME = "calls";

const CallSchema = new Schema(
  {
    user: {
      type: Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    office: {
      type: Schema.Types.ObjectId,
      ref: "Office",
    },
    date: {
      type: Date,
      required: true,
      match: [
        /^\d{4}-\d{2}-\d{2}$/,
        "Please enter a valid date in the format YYYY-MM-DD",
      ],
    },
    calls: {
      maked: {
        type: Number,
        default: 0,
        required: true,
      },
      received: {
        type: Number,
        default: 0,
        required: true,
      },
    },
    totalCalls: {
      type: Number,
    },
  },
  {
    versionKey: false,
    timestamps: true,
    new: true,
  }
);

CallSchema.plugin(mongoosePagination);

CallSchema.pre("save", function (next) {
  this.totalCalls = this.calls.received + this.calls.maked;
  next();
});

export const Call = model<CallDocument, Pagination<CallDocument>>(
  DOCUMENT_NAME,
  CallSchema,
  COLLECTION_NAME
);
