import {
  AggregatePaginateModel,
  Document,
  model,
  ObjectId,
  Schema,
  Types,
} from "mongoose";
import { mongoosePagination, Pagination } from "mongoose-paginate-ts";
import aggregatePaginate from "mongoose-aggregate-paginate-v2";
import { UserDocument, USER_DOCUMENT_NAME } from "./User";

export const SESSION_DOCUMENT_NAME = "Session";

export enum EventType {
  LOGIN = "login",
  LOGOUT = "logout",
}
export enum SessionNotes {
  NONE = "",
  CONDUCTED_LOGOUT = "Super Admin Conducted Logout",
  SELF_LOGOUT = "Self Logout",
  AUTOMATED_LOGOUT = "Automated Logout",
}

type Browser = {
  name: String | null;
  version: String | null;
  os: String | null;
};

export interface SessionDocument extends Document {
  user: UserDocument | ObjectId;
  eventType: EventType;
  ipAddress: String | null;
  browser: Browser;
  notes?: SessionNotes;
  createdAt: Date;
  updatedAt: Date;
}

const sessionSchema = new Schema(
  {
    user: {
      type: Types.ObjectId,
      ref: USER_DOCUMENT_NAME,
      required: true,
    },
    eventType: {
      type: String,
      enum: EventType,
      required: true,
    },
    ipAddress: {
      type: String,
      default: null,
    },
    browser: {
      name: {
        type: String,
        default: null,
      },
      version: {
        type: String,
        default: null,
      },
      os: {
        type: String,
        default: null,
      },
    },
    notes: {
      type: String,
      enum: SessionNotes,
      default: SessionNotes.NONE,
    },
  },
  {
    versionKey: false,
    timestamps: true,
  }
);

sessionSchema.plugin(mongoosePagination);
sessionSchema.plugin(aggregatePaginate);

interface SessionModel
  extends AggregatePaginateModel<SessionDocument>,
    Pagination<SessionDocument> {}

export const Session = model<SessionDocument, SessionModel>(
  SESSION_DOCUMENT_NAME,
  sessionSchema
);
