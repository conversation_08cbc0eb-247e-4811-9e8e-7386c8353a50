import mongoose, { Types } from "mongoose";
import MongooseDelete from "mongoose-delete";
import { mongoosePagination, Pagination } from "mongoose-paginate-ts";
import { OfficeDocument } from "./Office";
import { UserDocument, USER_DOCUMENT_NAME } from "./User";
export const OPERATION_DOCUMENT_NAME = "Operation";

export enum OperationType {
  CREDIT = "credit",
  DEPOSIT = "deposit",
}

export interface OperationDocument extends mongoose.Document {
  type: OperationType;
  amount: number;
  notes: string;
  office: OfficeDocument;
  admin: UserDocument;
  createdAt: Date;
  updatedAt: Date;
  deletedAt: Date;
}

const operationSchema = new mongoose.Schema(
  {
    type: {
      type: String,
      enum: OperationType,
      default: OperationType.CREDIT,
    },
    amount: {
      type: Number,
      required: [true, "Please tell us amount"],
    },
    notes: {
      type: String,
      required: [true, "Please tell us notes"],
    },
    office: {
      type: Types.ObjectId,
      ref: "Office",
      required: [true, "Please tell us office"],
    },
    admin: {
      type: Types.ObjectId,
      ref: "User",
      required: [true, "Please tell us admin"],
    },
  },
  { versionKey: false, timestamps: true }
);

operationSchema.plugin(mongoosePagination);

operationSchema.plugin(MongooseDelete, {
  overrideMethods: "all",
  deletedAt: true,
  indexFields: true,
});

const Operation = mongoose.model<
  OperationDocument,
  Pagination<OperationDocument>
>(OPERATION_DOCUMENT_NAME, operationSchema);
export default Operation;
