import { Document, model, Schema, Types } from "mongoose";
import MongooseDelete from "mongoose-delete";
import { mongoosePagination, Pagination } from "mongoose-paginate-ts";
import { NOTE_DOCUMENT_NAME } from "./Note";
import { TodoDocument, TODO_DOCUMENT_NAME } from "./Todo";
import { UserDocument, USER_DOCUMENT_NAME } from "./User";

export const NOTIF_DOCUMENT_NAME = "Notification";
export const NOTIF_COLLECTION_NAME = "notifications";

export const enum NOTIFICATIONS_DOCS {
  TODO = "Todo",
  NOTE = "Note",
}

export interface NotificationDocument extends Document {
  from: string | UserDocument;
  to: string | UserDocument;
  message: string;
  seen: boolean;
  seenAt: Date;
  doc: string | TodoDocument;
  docModel: NOTIFICATIONS_DOCS;
}

const notificationSchema = new Schema<NotificationDocument>(
  {
    from: { type: Types.ObjectId, ref: USER_DOCUMENT_NAME },
    to: { type: Types.ObjectId, ref: USER_DOCUMENT_NAME },
    message: { type: String, required: true },
    doc: {
      type: Schema.Types.ObjectId,
      required: [true, "Please provide document id"],
      refPath: "docModel",
    },
    docModel: {
      type: String,
      required: [true, "Please provide document model"],
      enum: [NOTIFICATIONS_DOCS.NOTE, NOTIFICATIONS_DOCS.TODO],
    },
    seen: { type: Boolean, default: false },
    seenAt: { type: Date, default: null },
  },
  { versionKey: false, timestamps: true }
);

notificationSchema.plugin(mongoosePagination);

notificationSchema.plugin(MongooseDelete, {
  overrideMethods: "all",
  indexFields: true,
});

export const Notification = model<
  NotificationDocument,
  Pagination<NotificationDocument> &
    MongooseDelete.SoftDeleteModel<NotificationDocument>
>(NOTIF_DOCUMENT_NAME, notificationSchema, NOTIF_COLLECTION_NAME);
