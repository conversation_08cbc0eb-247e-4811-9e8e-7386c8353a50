import { Document, model, Schema, Types } from "mongoose";
import { mongoosePagination, Pagination } from "mongoose-paginate-ts";
import { CashDeskPaymentDocument } from "./CashDeskPayment";
import { OfficeDocument } from "./Office";
import { UserDocument } from "./User";

export const DOCUMENT_NAME = "CashDeskPaymentHistory";

export enum CashDeskPaymentHistoryAction {
  CREATE = "create",
  UPDATE = "update",
  DELETE = "delete",
}

export interface CashDeskPaymentHistoryDocument extends Document {
  _id: Types.ObjectId;
  cashDeskPayment: CashDeskPaymentDocument;
  action: CashDeskPaymentHistoryAction;
  amount: number;
  cashedTo: UserDocument | Schema.Types.ObjectId;
  cashedBy: UserDocument | Schema.Types.ObjectId;
  office: OfficeDocument;
  notes?: string;
  createdBy: UserDocument;
}

const CashDeskPaymentHistorySchema = new Schema(
  {
    cashDeskPayment: {
      type: Types.ObjectId,
      ref: "CashDeskPayment",
    },
    action: {
      type: String,
      enum: CashDeskPaymentHistoryAction,
    },
    amount: {
      type: Number,
    },
    cashedTo: {
      type: Schema.Types.ObjectId,
      ref: "User",
    },
    office: {
      type: Schema.Types.ObjectId,
      ref: "Office",
    },
    notes: {
      type: String,
      default: null,
    },
    cashedBy: {
      type: Schema.Types.ObjectId,
      ref: "User",
    },
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: "User",
    },
  },
  { versionKey: false, timestamps: true }
);

CashDeskPaymentHistorySchema.plugin(mongoosePagination);

export const CashDeskPaymentHistory = model<
  CashDeskPaymentHistoryDocument,
  Pagination<CashDeskPaymentHistoryDocument>
>(DOCUMENT_NAME, CashDeskPaymentHistorySchema);
