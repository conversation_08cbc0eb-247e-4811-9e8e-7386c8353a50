import { Document, model, Schema } from "mongoose";
import MongooseDelete from "mongoose-delete";
import { mongoosePagination, Pagination } from "mongoose-paginate-ts";
import { UserDocument } from "./User";

export interface SectionDocument extends Document {
  name: string;
  description: string;
  createdAt: Date;
  updatedAt: Date;
}
export const DOCUMENT_NAME = "Section";

const SectionSchema = new Schema(
  {
    name: {
      type: String,
      required: true,
      unique: true,
    },
    description: {
      type: String,
    },
  },
  {
    versionKey: false,
    timestamps: true,
  }
);

SectionSchema.plugin(mongoosePagination);
SectionSchema.plugin(MongooseDelete, {
  overrideMethods: "all",
  indexFields: true,
});

export const Section = model<SectionDocument, Pagination<SectionDocument>>(
  DOCUMENT_NAME,
  SectionSchema
);
