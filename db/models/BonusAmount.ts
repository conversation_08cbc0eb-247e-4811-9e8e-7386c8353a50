import { Document, model, Schema } from "mongoose";
import MongooseDelete from "mongoose-delete";
import { mongoosePagination, Pagination } from "mongoose-paginate-ts";

export const BONUS_AMOUNT_DOCUMENT_NAME = "BonusAmount";
export const BONUS_AMOUNT_COLLECTION_NAME = "bonusAmounts";

interface Ranges {
  amount: number;
  minSeniority: number;
  maxSeniority: number;
}
export interface BonusAmountDocument extends Document {
  ranges: Ranges[];
  startDate: Date;
  endDate: Date;
  createdDate: Date;
  updatedAt: Date;
  deletedAt: Date;
}

const BonusAmountSchema = new Schema(
  {
    ranges: [
      {
        minSeniority: { type: Number, required: true, min: 0 },
        maxSeniority: {
          type: Number,
          required: true,
          validate: {
            validator: function (value) {
              return value >= this.minSeniority;
            },
            message:
              "maxSeniority must be greater than or equal to minSeniority.",
          },
        },
        amount: { type: Number, required: true },
        _id: false,
      },
    ],
    startDate: { type: Date, required: true },
    endDate: { type: Date, required: true },

    deletedAt: {
      type: Date,
      default: null,
    },
  },
  {
    versionKey: false,
    timestamps: true,
  }
);

BonusAmountSchema.plugin(mongoosePagination);
BonusAmountSchema.plugin(MongooseDelete, {
  overrideMethods: "all",
  indexFields: true,
});

export const BonusAmount = model<
  BonusAmountDocument,
  Pagination<BonusAmountDocument> &
    MongooseDelete.SoftDeleteModel<BonusAmountDocument>
>(BONUS_AMOUNT_DOCUMENT_NAME, BonusAmountSchema, BONUS_AMOUNT_COLLECTION_NAME);
