import { Document, model, Schema } from "mongoose";
import MongooseDelete from "mongoose-delete";
import { mongoosePagination, Pagination } from "mongoose-paginate-ts";

export const PAYMENT_NOTE_DOCUMENT_NAME = "PaymentNote";
export interface PaymentNoteDocument extends Document {
  content: string;
}

const PaymentNoteSchema = new Schema(
  {
    name: {
      type: String,
      unique: true,
      required: true,
    },
    description: {
      type: String,
      unique: true,
      required: true,
    },
    deletedAt: {
      type: Date,
      default: null,
    },
  },
  {
    versionKey: false,
    timestamps: true,
  }
);

PaymentNoteSchema.plugin(mongoosePagination);
PaymentNoteSchema.plugin(MongooseDelete, {
  overrideMethods: "all",
  indexFields: true,
});
export const PaymentNote = model<
  PaymentNoteDocument,
  Pagination<PaymentNoteDocument> &
    MongooseDelete.SoftDeleteModel<PaymentNoteDocument>
>(PAYMENT_NOTE_DOCUMENT_NAME, PaymentNoteSchema);
