import mongoose, { Types } from "mongoose";
import MongooseDelete from "mongoose-delete";
import { mongoosePagination, Pagination } from "mongoose-paginate-ts";
import { KpiDocument } from "./Kpi";
import { StatClientDocument } from "./StatClient";
import { UserDocument } from "./User";

export interface StatClientResponseDocument extends mongoose.Document {
  clientName: string;
  admin: UserDocument;
  statClient: StatClientDocument;
  kpis: Array<{
    kpi: KpiDocument;
    response: Array<any>;
  }>;
  createdAt: Date;
  updatedAt: Date;
  deletedAt: Date;
}

const statClientResponseSchema = new mongoose.Schema(
  {
    clientName: {
      type: String,
      required: [true, "Please tell us client name"],
    },
    clientContact: {
      type: String,
      required: [true, "Please tell us client contact"],
    },
    admin: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
    },
    statClient: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "StatClient",
    },
    office: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Office",
    },
    kpis: [
      {
        kpi: {
          type: mongoose.Schema.Types.ObjectId,
          ref: "Kpi",
        },
        response: [{ type: mongoose.Schema.Types.Mixed }],
        _id: false,
      },
    ],
    deletedAt: {
      type: Date,
      default: null,
    },
  },
  { versionKey: false, timestamps: true }
);

statClientResponseSchema.plugin(mongoosePagination);

statClientResponseSchema.plugin(MongooseDelete, {
  overrideMethods: "all",
  indexFields: true,
});

const StatClientResponse = mongoose.model<
  StatClientResponseDocument,
  Pagination<StatClientResponseDocument>
>("StatClientResponse", statClientResponseSchema);
export default StatClientResponse;
