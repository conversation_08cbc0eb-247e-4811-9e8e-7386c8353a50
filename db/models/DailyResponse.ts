import mongoose, { Types } from "mongoose";
import MongooseDelete from "mongoose-delete";
import { mongoosePagination, Pagination } from "mongoose-paginate-ts";
import { DailyKpiDocument } from "./DailyKpi";
import { StatClientDocument } from "./StatClient";
import { UserDocument } from "./User";

export interface DailyResponseDocument extends mongoose.Document {
  clientName: string;
  admin: UserDocument;
  statClient: StatClientDocument;
  dailyKpis: Array<{
    dailyKpi: DailyKpiDocument;
    response: Array<any>;
  }>;
  createdAt: Date;
  updatedAt: Date;
  deletedAt: Date;
}

const dailyResponseSchema = new mongoose.Schema(
  {
    admin: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
    },
    dailyQuestion: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "DailyQuestion",
    },
    office: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Office",
    },
    dailyKpis: [
      {
        dailyKpi: {
          type: mongoose.Schema.Types.ObjectId,
          ref: "DailyKpi",
        },
        response: [{ type: mongoose.Schema.Types.Mixed }],
        _id: false,
      },
    ],
    deletedAt: {
      type: Date,
      default: null,
    },
  },
  { versionKey: false, timestamps: true }
);

dailyResponseSchema.plugin(mongoosePagination);

dailyResponseSchema.plugin(MongooseDelete, {
  overrideMethods: "all",
  indexFields: true,
});

const DailyResponse = mongoose.model<
  DailyResponseDocument,
  Pagination<DailyResponseDocument>
>("DailyResponse", dailyResponseSchema);
export default DailyResponse;
