import { Document, model, Schema } from "mongoose";
import MongooseDelete from "mongoose-delete";
import { mongoosePagination, Pagination } from "mongoose-paginate-ts";
import { UserDocument } from "./User";

export interface LevelDocument extends Document {
  name: string;
  description: string;
  createdAt: Date;
  updatedAt: Date;
}
export const DOCUMENT_NAME = "Level";

const levelSchema = new Schema(
  {
    name: {
      type: String,
      required: true,
      unique: true,
    },
    description: {
      type: String,
    },
  },
  {
    versionKey: false,
    timestamps: true,
  }
);

levelSchema.plugin(mongoosePagination);
levelSchema.plugin(MongooseDelete, {
  overrideMethods: "all",
  indexFields: true,
});

export const Level = model<LevelDocument, Pagination<LevelDocument>>(
  DOCUMENT_NAME,
  levelSchema
);
