import Office from "../db/models/Office";

const officesFromTakiacademy = [
  {
    id: "1",
    name: "<PERSON><PERSON><PERSON>",
    address: "Sahloul 3",
    created_at: "2018-11-16 11:27:48",
    updated_at: "2022-03-23 13:21:31",
  },
  {
    id: "2",
    name: "<PERSON><PERSON><PERSON>",
    address: "Complexe Badr, au dessus de Carrefour  Market Khézama Est",
    created_at: "2018-11-16 11:27:48",
    updated_at: "2023-10-19 08:32:48",
  },
  {
    id: "3",
    name: "<PERSON><PERSON><PERSON>",
    address: "Nabeul",
    created_at: "2018-11-16 11:27:48",
    updated_at: "2018-11-16 11:27:48",
  },
  {
    id: "4",
    name: "<PERSON><PERSON>",
    address: "Bardo",
    created_at: "2018-11-29 11:26:09",
    updated_at: "2018-11-29 11:26:09",
  },
  {
    id: "5",
    name: "<PERSON><PERSON><PERSON>",
    address: "<PERSON><PERSON>x",
    created_at: "2018-11-29 11:26:09",
    updated_at: "2018-11-29 11:26:09",
  },
  {
    id: "6",
    name: "<PERSON><PERSON><PERSON>",
    address:
      'Avenue habib bourguiba, Immeuble "Bali", 1er <PERSON><PERSON>, <PERSON>.13 au dessus de la banque "BH" E<PERSON>hra- <PERSON> Arous 2034.',
    created_at: "2019-12-06 10:21:34",
    updated_at: "2022-01-13 23:03:05",
  },
  {
    id: "7",
    name: "<PERSON> <PERSON>zah",
    address: "47, Av. Moua<PERSON>i، Rue Mouawiya <PERSON> Abi So<PERSON>ne, Ariana 2091",
    created_at: "2020-01-13 12:31:45",
    updated_at: "2020-01-13 12:31:45",
  },
  {
    id: "8",
    name: "Bizerte",
    address: "Bureau 105, bloc B, Le complexe. Rue Hsan Nouri Bizerte",
    created_at: "2020-07-06 12:52:47",
    updated_at: "2020-07-06 12:52:47",
  },
  {
    id: "9",
    name: "Kairouan",
    address: "B1, Imm. 8. Rue Assad Ibn Fourat. Kairouan",
    created_at: "2020-07-06 13:03:04",
    updated_at: "2020-07-06 13:03:04",
  },
  {
    id: "10",
    name: "Monastir",
    address:
      "Bureau 501,Imm Ghomrassi , Av.Combattant Suprême ,Centre ville Monastir",
    created_at: "2020-10-19 15:47:50",
    updated_at: "2020-10-19 15:47:50",
  },
  {
    id: "11",
    name: "Centre Urbain Nord",
    address: "Bloc A A2.8  immeuble yasmine tower centre urbain nord Tunis",
    created_at: "2021-02-19 21:44:00",
    updated_at: "2021-02-19 21:44:00",
  },
  {
    id: "12",
    name: "Gabes",
    address:
      "شارع ? أفريل - باب بحر عمارة ????? ?????? | الطابق الثاني ,مكتب عــــ??ـــدد. فوق محل ??????❜? للملابس الجاهزة :  ?",
    created_at: "2021-02-28 22:02:40",
    updated_at: "2022-03-23 13:21:02",
  },
  {
    id: "13",
    name: "Kébili",
    address: "Immeuble Samer, Route Gabes",
    created_at: "2021-02-28 22:03:12",
    updated_at: "2021-02-28 22:03:12",
  },
  {
    id: "14",
    name: "El Aouina",
    address:
      "avenue Mongi Slim - 2045 - Aouina au dessus de la Banque Arabe de Tunisie -ATB- et du restaurant Baguette & Baguette",
    created_at: "2021-06-30 22:40:07",
    updated_at: "2021-12-24 14:36:32",
  },
  {
    id: "15",
    name: "Medenine",
    address: "Immeuble Tibba , 2 eme etage , bureau 207 Medenine",
    created_at: "2022-06-07 17:08:48",
    updated_at: "2022-06-07 17:08:48",
  },
  {
    id: "16",
    name: "Djerba",
    address: "حومة السوق جربة",
    created_at: "2022-06-27 09:14:41",
    updated_at: "2022-06-27 09:14:41",
  },
  {
    id: "17",
    name: "Sidi Bouzid",
    address: "عمارة الريان ، قبالة المدرسة الاعدادية النموذجية",
    created_at: "2022-10-02 20:16:33",
    updated_at: "2022-10-02 20:16:33",
  },
  {
    id: "18",
    name: "Gafsa",
    address: "gafsa",
    created_at: "2022-10-02 20:16:58",
    updated_at: "2022-10-02 20:16:58",
  },
  {
    id: "19",
    name: "Siliana",
    address:
      "عمارة الخروبي، طريق الطيب المهيري - سليانة 6100.   فوق مقر الصندوق الوطني للتأمين على المرض CNAM   ومحاذي لـ Carrefour Market بسليانة",
    created_at: "2022-12-04 18:22:58",
    updated_at: "2022-12-04 18:22:58",
  },
  {
    id: "20",
    name: "Mourouj",
    address: "المروج 3",
    created_at: "2022-12-19 12:07:49",
    updated_at: "2022-12-19 12:07:49",
  },
  {
    id: "21",
    name: "Jendouba",
    address: "Imm . Abdelhakim Drissi",
    created_at: "2023-01-19 21:17:41",
    updated_at: "2023-01-19 21:17:41",
  },
  {
    id: "22",
    name: "Beja",
    address: "عبد الحكيم المنكبي",
    created_at: "2023-02-05 20:01:46",
    updated_at: "2023-02-05 20:01:46",
  },
  {
    id: "23",
    name: "Zaghouan",
    address: "Zaghouan",
    created_at: "2023-07-09 23:18:59",
    updated_at: "2023-07-09 23:18:59",
  },
  {
    id: "24",
    name: "Msaken",
    address:
      "شارع الطيب حشيشة مساكن  عمارة بوهلال الطابق الرابع 04 - مساكن٫ سوسة - 4070.",
    created_at: "2023-09-12 21:00:25",
    updated_at: "2023-09-12 21:00:25",
  },
  {
    id: "25",
    name: "El Kef",
    address: "Rue Mongi slim - el kef",
    created_at: "2023-09-25 21:28:04",
    updated_at: "2023-09-25 21:28:04",
  },
  {
    id: "26",
    name: "Mahdia",
    address: "Mahdia Centre",
    created_at: "2023-10-15 20:28:51",
    updated_at: "2023-10-15 20:28:51",
  },
  {
    id: "27",
    name: "Tataouine",
    address: "tatouine",
    created_at: "2024-01-01 13:48:49",
    updated_at: "2024-01-01 13:48:49",
  },
  {
    id: "28",
    name: "Tozeur",
    address: "Tozeur",
    created_at: "2024-01-20 23:16:03",
    updated_at: "2024-01-20 23:16:03",
  },
  {
    id: "29",
    name: "Kasserine",
    address: "Kasserine",
    created_at: "2024-01-31 11:54:59",
    updated_at: "2024-01-31 11:54:59",
  },
  {
    id: "30",
    name: "Kélibia",
    address: "colisée kélibia",
    created_at: "2024-04-06 00:49:17",
    updated_at: "2024-04-06 00:49:53",
  },
];

export const updateOffices = async () => {
  for (let officeTaki of officesFromTakiacademy) {
    await Office.updateOne(
      { name: officeTaki?.name },
      { $set: { officeId: parseInt(officeTaki.id) } }
    );
  }
};
