import { Request } from "express";
import { BadRequestError } from "../helpers/ApiError";
import { createFolder, createTreeFolder } from "./file";

const multer = require("multer");
const path = require("path");
const maxSize = 50 * 1024 * 1024;

const mimeTypeAllowed = [
  "image/png",
  "image/jpg",
  "image/jpeg",
  "video/mp4",
  "audio/mpeg",
  "audio/mp3",
  "audio/wav",
  "audio/ogg",
  "application/pdf",
];

const upload = multer({
  storage: multer.diskStorage({
    destination: (req: Request, file: any, cb: any) => {
      createFolder("./public");
      createFolder("./public/uploads");
      const dynamicFolder = req.params.url || "default";
      createTreeFolder(`./public/uploads/${dynamicFolder}`);
      cb(null, path.join(`./public/uploads/${dynamicFolder}`));
    },
    filename: (req: Request, file: any, cb: any) => {
      file.originalname = Buffer.from(file.originalname, "latin1").toString(
        "utf8"
      );
      cb(null, Date.now() + "." + file.mimetype.split("/")[1]);
    },
  }),
  fileFilter: (req: Request, file: any, cb: any) => {
    if (mimeTypeAllowed.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(
        new BadRequestError(
          "Only .png, .jpg, .jpeg, .mp3, .mp4, .ogg, .wav allowed"
        )
      );
    }
  },
  limits: { fileSize: maxSize },
});
export const uploadFile = upload.single("file");
export const uploadFiles = (propertyName = "files[]") =>
  upload.array(propertyName, 10);
