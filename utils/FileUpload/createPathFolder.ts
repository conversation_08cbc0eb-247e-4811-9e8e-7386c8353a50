import { NextFunction, Request, Response } from "express";
import asyncHand<PERSON> from "../../middlewares/asyncHandler";
import FolderRepo from "../../db/repositories/FolderRepo";
import { NotFoundError } from "../../helpers/ApiError";
import { removeDuplicationSlash } from "../removeDuplicationSlash";
export const createPathFolder = asyncHandler(
  async (req: Request, res: Response, next: NextFunction) => {
    const parentFolder = req.body.parentFolder;
    if (!parentFolder) {
      req.body.path = removeDuplicationSlash("/" + req.body.name.trim());
      return next();
    }
    const parentFolderUrl = await FolderRepo.findOne({ _id: parentFolder });

    if (!parentFolder) throw new NotFoundError("Folder not found!");
    req.body.path = removeDuplicationSlash(
      parentFolderUrl?.path + "/" + (req.body?.name.trim() || "untitled folder")
    );
    return next();
  }
);
