export function convertMilliseconds(ms: number, includeTime: boolean = false) {
  if (ms < 0) {
    return "Invalid input"; // Handle negative input if needed
  }
  // Define the lengths of different time units in milliseconds
  const msInSecond = 1000;
  const msInMinute = msInSecond * 60;
  const msInHour = msInMinute * 60;
  const msInDay = msInHour * 24;
  const msInWeek = msInDay * 7;
  const msInMonth = msInDay * 30; // Approximate month length
  const msInYear = msInDay * 365; // Approximate year length

  // Calculate years
  const years = Math.floor(ms / msInYear);
  ms %= msInYear;

  // Calculate months
  const months = Math.floor(ms / msInMonth);
  ms %= msInMonth;

  // Calculate weeks
  const weeks = Math.floor(ms / msInWeek);
  ms %= msInWeek;

  // Calculate days
  const days = Math.floor(ms / msInDay);
  ms %= msInDay;

  let hours = 0;
  let minutes = 0;
  let seconds = 0;

  if (includeTime) {
    // Calculate hours
    hours = Math.floor(ms / msInHour);
    ms %= msInHour;

    // Calculate minutes
    minutes = Math.floor(ms / msInMinute);
    ms %= msInMinute;

    // Calculate seconds
    seconds = Math.floor(ms / msInSecond);
  }

  // Construct the result string
  let result = "";
  if (years > 0) {
    result += `${years} Year${years !== 1 ? "s" : ""} `;
  }
  if (months > 0) {
    result += `${months} Month${months !== 1 ? "s" : ""} `;
  }
  if (weeks > 0) {
    result += `${weeks} Week${weeks !== 1 ? "s" : ""} `;
  }
  if (days > 0) {
    result += `${days} Day${days !== 1 ? "s" : ""} `;
  }

  if (includeTime) {
    if (hours > 0) {
      result += `${hours} Hour${hours !== 1 ? "s" : ""} `;
    }
    if (minutes > 0) {
      result += `${minutes} Minute${minutes !== 1 ? "s" : ""} `;
    }
    if (seconds > 0) {
      result += `${seconds} Second${seconds !== 1 ? "s" : ""} `;
    }
  }

  return result.trim();
}

export function convertMilliSecondsToYears(ms: number): number {
  return +(ms / 31536000000).toFixed(2);
}
