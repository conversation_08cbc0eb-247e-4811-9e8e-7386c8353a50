import puppeteer from "puppeteer";
import fs from "fs";
import path from "path";
import { chromiumBrowserPath, environment } from "../../config";

export const pdfGenerator = async (
  dynamicContent: string,
  outputPath: string
) => {
  try {
    const config =
      environment === "production"
        ? {
            executablePath: chromiumBrowserPath,
            args: ["--no-sandbox", "--disable-setuid-sandbox"],
          }
        : {};

    const browser = await puppeteer.launch(config);
    const page = await browser.newPage();

    const logoPath = path.resolve(__dirname, "./images/logo.png");
    const logoBase64 = getBase64Image(logoPath);

    let htmlContent = `
    <html>
      <head>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Kufi+Arabic:wght@100..900&family=Noto+Naskh+Arabic:wght@400..700&family=Public+Sans:ital,wght@0,100..900;1,100..900&display=swap" rel="stylesheet">
        <style>
          body {
              font-family: 'Noto Naskh Arabic', serif;
            padding:2.5rem;
          }
          .header {
            padding-bottom: 20px;
          }
          .logo {
            max-width: 150px;
          }
          .content {
            margin-top: 20px;
          }
          .ql-align-center{
            text-align: center;
          }
          .ql-align-right{
            text-align: right;
          }
          .ql-direction-rtl {
            direction: rtl;
            text-align: right;
          }
        </style>
      </head>
      <body>
          <div class="header">
            <img src=${logoBase64} alt="logo" class="logo" />
          </div>
          <div class="content" id="dynamic-content">
            ${dynamicContent}
          </div>
      </body>
    </html>
  `;

    await page.setContent(htmlContent, {
      waitUntil: ["domcontentloaded", "networkidle0"],
    });
    const pdfBuffer = await page.pdf({
      path: outputPath,
      format: "a4",
      printBackground: true,
    });

    await browser.close();
    return pdfBuffer;
  } catch (err) {
    console.error("Error generating PDF:", err.message);
  }
};
function getBase64Image(filePath: string) {
  const file = fs.readFileSync(filePath);
  return `data:image/png;base64,${file.toString("base64")}`;
}
