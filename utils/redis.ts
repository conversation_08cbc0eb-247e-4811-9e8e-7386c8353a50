import { createClient } from "redis";
import { redisHost, redisPort } from "../config";

const redisOptions = {
  socket: {
    host: redisHost,
    port: redisPort,
  },
};

export const client = createClient(redisOptions);

const redisClient = async () => {
  client.on("error", (err) => console.log("Redis Client Error", err));
  client.on("connect", function () {
    console.log("Redis client connected");
  });
  await client.connect();
};

export default redisClient;
