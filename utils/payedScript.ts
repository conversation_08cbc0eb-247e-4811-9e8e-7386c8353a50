import mongoose from "mongoose";
import { database_secret } from "../config";
import Code from "../db/models/Code";
const DB: any = database_secret;

mongoose
  .connect(DB)
  .then(() => {
    console.log(`DB connection successful on ⏰`);
  })
  .catch((err) => {
    console.log(err);
  });

export const codeScript = async () => {
  const codes = await Code.find({});
  for (let code of codes) {
    await Code.updateOne(
      { _id: code?._id },
      { $set: { isPayed: true, payedAt: code?.createdAt } }
    );
  }
  console.log("success");
};

codeScript();
