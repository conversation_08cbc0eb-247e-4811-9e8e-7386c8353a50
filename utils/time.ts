import { format } from "date-fns";
import dayjs from "dayjs";

export function fDate(date: Date | string, newFormat?: string) {
  const fm = newFormat || "dd MMM yyyy";

  return date ? format(new Date(date), fm) : "";
}

export function parseTimeToDate(time: string): Date {
  return dayjs(`1970-01-01T${time}`).toDate();
}

export function formatDateToTime(date: Date): string {
  return dayjs(date).format("HH:mm");
}

// Helper function to create a Date object for a specific time
export function createDateForTime(baseDate: Date, time: Date): Date {
  // const [hours, minutes] = time.split(":").map(Number);

  const hours = time.getHours();
  const minutes = time.getMinutes();

  const result = new Date(baseDate);
  result.setHours(hours, minutes, 0, 0);
  return result;
}
