import cron from "node-cron";
import { EventType, SessionNotes } from "../db/models/Session";
import SessionRepo from "../db/repositories/SessionRepo";
import agentPerformanceService from "../services/agentPerformanceService";
import authService from "../services/authService";
import checkCalandarService from "../services/checkCalandarService";
import codeService from "../services/codeService";
import sessionService from "../services/sessionService";
import statisticService from "../services/statisticService";

export const updateCodes = async () => {
  cron.schedule(
    "0 0 23 * * *",
    async () => {
      await codeService.reloadCodes();
    },
    {
      scheduled: true,
      timezone: "CET",
    }
  );
};

export const checkCalandarAuthorizationDate = async () => {
  cron.schedule(
    "0 1 * * *",
    async () => {
      await checkCalandarService
        .updateAuthorization()
        .then((res) => {
          console.log("Calandar check success !");
        })
        .catch((err) => console.log("Error !"));
    },
    {
      scheduled: true,
      timezone: "CET",
    }
  );
};

export const logoutAllUsers = async () => {
  cron.schedule(
    "0 23 * * *",
    async () => {
      const activeAdmins = await sessionService.getAdminsWithActiveSessions();

      if (activeAdmins.length > 0) {
        await authService
          .removeAllTokens(activeAdmins)
          .then(async () => {
            const sessions = activeAdmins.map((user) => ({
              user: user._id,
              eventType: EventType.LOGOUT,
              notes: SessionNotes.AUTOMATED_LOGOUT,
            }));

            await SessionRepo.createMany(sessions);

            console.log("token removal success !");
          })
          .catch((err) => console.log(err.message));
      }
    },
    {
      scheduled: true,
      timezone: "CET",
    }
  );
};

export const agentPerformances = async () => {
  cron.schedule(
    "0 2 * * *",
    async () => {
      const currentYear = new Date().getFullYear();
      const currentMonth = new Date().getMonth();

      await agentPerformanceService.refreshAgentPerformances(
        currentYear,
        currentMonth
      );
    },
    {
      scheduled: true,
      timezone: "CET",
    }
  );
};

export const balancePerMonth = async () => {
  cron.schedule(
    "30 1 * * *",
    async () => {
      const currentYear = new Date().getFullYear();
      const startYear = 2024;

      for (let year = currentYear; year >= startYear; year--) {
        await statisticService.balanceAnalyticsPerMonthCron(year);
      }
    },
    {
      scheduled: true,
      timezone: "CET",
    }
  );
};
