import express from 'express'
import { UserDocument } from '../db/models/User'

declare global {
  namespace Express {
    interface Request {
      user?: UserDocument
      file?: Record<string, any>
    }
  }

  export interface PaginationModel {
    meta: {
      limit: number
      hasPrevPage: boolean
      hasNextPage: boolean
      hasMore: boolean
      totalDocs: number
      totalPages: number
      page: number
      perPage: number
      pagingCounter: number
    }
    docs: []
  }
}

declare interface ProtectedRequest extends express.Request {
  user?: UserDocument
}
