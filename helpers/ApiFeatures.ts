import worktimeService from "../services/worktimeService";

class APIFeatures {
  query: any;
  queryString: any;

  constructor(query: any, queryString: any) {
    this.query = query;
    this.queryString = queryString;
  }
  filter() {
    const queryObj = { ...this.queryString };
    const excludedFields = ["fields"];
    excludedFields.forEach((el) => delete queryObj[el]);
    let queryStr = JSON.stringify(queryObj);
    queryStr = queryStr.replace(/\b(gte|gt|lte|lt)\b/g, (match) => `$${match}`);
    const convertDates = worktimeService.convertDateStringsToDateObjects(
      JSON.parse(queryStr)
    );
    this.query = this.query.find(convertDates);

    return this;
  }

  search(searchFields?: string[]) {
    if (this.queryString.search && searchFields) {
      const searchValue = this.queryString.search.split(",").join(" ");
      const regex = new RegExp(searchValue, "i");
      const searchQueries = searchFields.map((field: string) => ({
        [field]: { $regex: regex },
      }));
      const combinedQuery = { $or: searchQueries };
      this.query = this.query.find(combinedQuery);
    }

    return this;
  }

  sort() {
    if (this.queryString.sort) {
      const sortBy = this.queryString.sort.split(",").join(" ");
      this.query = this.query.sort(sortBy);
    } else {
      this.query = this.query.sort("-createdAt");
    }

    return this;
  }

  limitFields() {
    if (this.queryString.fields) {
      const fields = this.queryString.fields.split(",").join(" ");
      this.query = this.query.select(fields);
    } else {
      this.query = this.query.select("-__v");
    }

    return this;
  }
  paginate() {
    const page = this.queryString.page * 1 || 1;
    const limit = this.queryString.limit * 1 || 100;
    const skip = (page - 1) * limit;

    this.query = this.query.skip(skip).limit(limit);

    return this;
  }
}
export default APIFeatures;
