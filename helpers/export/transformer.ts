import { format } from "date-fns";
import { AgentPerformanceDocument } from "../../db/models/AgentPerformance";
import { CallDocument } from "../../db/models/Call";
import { CashDeskPaymentDocument } from "../../db/models/CashDeskPayment";
import { CodeDocument } from "../../db/models/Code";
import { KpiDocument } from "../../db/models/Kpi";
import { OfficeDocument } from "../../db/models/Office";
import { OperationDocument } from "../../db/models/Operation";
import { StatClientResponseDocument } from "../../db/models/StatClientResponse";
import { UserDocument } from "../../db/models/User";
import { convertMilliseconds } from "../../utils/convertTime";


export default {
  kpi: (el: KpiDocument, index: number) => ({
    _id: index + 1,
    label: el.label,
    name: el.name,
    frontType: el.frontType,
    isRequired: el.isRequired,
    choices: el.choices,
    createdAt: el.createdAt && format(new Date(el.createdAt), "MM/dd/yyyy"),
    updatedAt: el.updatedAt && format(new Date(el.updatedAt), "MM/dd/yyyy"),
  }),
  office: (el: OfficeDocument, index: number) => ({
    _id: index + 1,
    name: el.name,
    address: el.address,
    createdAt: el.createdAt && format(new Date(el.createdAt), "MM/dd/yyyy"),
    updatedAt: el.updatedAt && format(new Date(el.updatedAt), "MM/dd/yyyy"),
  }),
  operation: (el: OperationDocument, index: number) => ({
    _id: index + 1,
    admin: el.admin?.username,
    amount: el.amount,
    type: el.type,
    office: el.office?.name,
    notes: el.notes,
    createdAt: el.createdAt && format(new Date(el.createdAt), "MM/dd/yyyy"),
    updatedAt: el.updatedAt && format(new Date(el.updatedAt), "MM/dd/yyyy"),
  }),
  code: (el: CodeDocument, index: number) => ({
    _id: index + 1,
    code: el.code,
    admin: el.admin?.username,
    office: el.office?.name,
    clientName: el.clientName,
    clientPhoneNumber: el.clientPhoneNumber,
    clientLevel: el.clientLevel,
    clientOffer: el.clientOffer,
    checkNumber: el.checkNumber,
    checkDate: el.checkDate,
    notes: el?.notes || "",
    createdAt: el.createdAt && format(new Date(el.createdAt), "MM/dd/yyyy"),
    updatedAt: el.updatedAt && format(new Date(el.updatedAt), "MM/dd/yyyy"),
  }),
  cashDeskPayment: (el: CashDeskPaymentDocument, index: number) => ({
    _id: index + 1,
    amount: el.amount,
    cashedTo: el.cashedTo?.username || el.cashedTo?.name,
    cashedBy: el.cashedBy?.username || el.cashedBy?.name,
    office: el.office?.name,
    notes: el?.notes || "",
    createdAt: el.createdAt && format(new Date(el.createdAt), "MM/dd/yyyy"),
    updatedAt: el.updatedAt && format(new Date(el.updatedAt), "MM/dd/yyyy"),
  }),
  user: (el: UserDocument, index: number) => ({
    _id: index + 1,
    username: el.username,
    email: el.email,
    avatar: el.avatar,
    office: el.office?.name,
    role: el?.role,
    deviceId: el?.deviceId,
    singleDevice: el?.singleDevice,
    permissionGroup: el.permissionGroup?.map(
      (permissionGroup) => permissionGroup.name
    ),
    extraPermission: el.extraPermissions?.map((permission) => ({
      model: permission.model,
      method: permission.method,
    })),
    createdAt: el.createdAt && format(new Date(el.createdAt), "MM/dd/yyyy"),
    updatedAt: el.updatedAt && format(new Date(el.updatedAt), "MM/dd/yyyy"),
  }),
  statClientResponse: (el: StatClientResponseDocument, index: number) => ({
    _id: index + 1,
    clientName: el.clientName,
    statClient: el.statClient?.name,
    kpis: el.kpis.map((res) => ({
      kpi: res.kpi?.name,
      response: res.response,
    })),
    admin: el?.admin?.username || el?.admin?.name,
    createdAt: el.createdAt && format(new Date(el.createdAt), "MM/dd/yyyy"),
    updatedAt: el.updatedAt && format(new Date(el.updatedAt), "MM/dd/yyyy"),
  }),
  call: (el: CallDocument, index: number) => ({
    _id: index + 1,
    admin: el.user?.username || el.user?.name,
    makedCalls: el.calls.maked,
    receivedCalls: el.calls.received,
    date: el.date && format(new Date(el.date), "MM/dd/yyyy"),
    createdAt: el.createdAt && format(new Date(el.createdAt), "MM/dd/yyyy"),
    updatedAt: el.updatedAt && format(new Date(el.updatedAt), "MM/dd/yyyy"),
  }),
  agentPerformance: (el: AgentPerformanceDocument, index: number) => ({
    _id: index + 1,
    user: el.user?.username || el.user?.name,
    date: el.date && format(new Date(el.date), "MM/dd/yyyy"),
    seniority: el.seniority && convertMilliseconds(el.seniority, true),
    lateness:
      el?.lateness &&
      convertMilliseconds(
        el?.lateness > 0 ? el?.lateness : -el?.lateness,
        true
      ),
    authorization:
      el?.authorization && convertMilliseconds(el?.authorization, true),
    statClientResponses: el?.statClientResponses,
    todos: el?.todos,
    leaves: el?.leave,
    calls: el?.calls ? el?.calls : "-",
    tam: el?.tam ? el?.tam : "-",
    eliminationFault: el?.eliminationFault ? el?.eliminationFault : "-",
    notes: el?.notes ? el?.notes : "-",
    bonusAmountPct: el?.bonusAmountPct ? el?.bonusAmountPct : "-",
    bonusAmountDT: el?.bonusAmountDT ? el?.bonusAmountDT : "-",
    achievedTasks: el?.todos ? el?.todos : "-",
  }),

  agentPerformancePerUser: (el: any, index: number) => ({
    month: el?.month,
    seniority: el?.data?.seniority && convertMilliseconds(el?.data?.seniority),
    lateness:
      el?.data?.lateness &&
      convertMilliseconds(
        el?.data?.lateness > 0 ? el?.data?.lateness : -el?.data?.lateness,
        true
      ),
    authorization:
      el?.data?.authorization &&
      convertMilliseconds(el?.data?.authorization, true),
    statClientResponses: el?.data?.statClientResponses,
    todos: el?.data?.todos,
    leaves: el?.data?.leave,
    calls: el?.data?.calls ? el?.data?.calls : "-",
    tam: el?.data?.tam ? el?.data?.tam : "-",
    eliminationFault: el?.data?.eliminationFault
      ? el?.data?.eliminationFault
      : "-",
    notes: el?.data?.notes ? el?.data?.notes : "-",
    bonusAmountPct: el?.data?.bonusAmountPct ? el?.data?.bonusAmountPct : "-",
    bonusAmountDT: el?.data?.bonusAmountDT ? el?.data?.bonusAmountDT : "-",
    achievedTasks: el?.data?.todos ? el?.data?.todos : "-",
  }),
  dailyResponse: (el: any, index: number) => ({
    _id: index + 1,
    clientName: el.clientName,
    dailyQuestion: el.dailyQuestion?.name,
    dailyKpis: el.dailyKpis.map((res) => ({
      dailyKpi: res.dailyKpi?.name,
      response: res.response,
    })),
    admin: el?.admin?.username || el?.admin?.name,
    createdAt: el.createdAt && format(new Date(el.createdAt), "MM/dd/yyyy"),
    updatedAt: el.updatedAt && format(new Date(el.updatedAt), "MM/dd/yyyy"),
  }),
};
