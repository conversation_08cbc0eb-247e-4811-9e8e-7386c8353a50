import { endOfDay, startOfDay } from "date-fns";
import { json2csv } from "json-2-csv";
import { Readable } from "stream";
import asyncHandler from "../../middlewares/asyncHandler";

export const exportData = <IDoc, T>(
  Repo: any,
  fileName: string,
  transformer: (doc: IDoc & T, index: number) => object
) =>
  asyncHandler(async (req, res) => {
    const { startDate, endDate } = req.query;
    let filter: Object = {};

    if (startDate && endDate) {
      filter = {
        createdAt: {
          $gte: startOfDay(startDate as string),
          $lte: endOfDay(endDate as string),
        },
      };
    }
    const docs = (await Repo.find(req.query, filter)) as unknown as (IDoc &
      T)[];
    const customData = docs.map(transformer);

    const data =
      customData.length !== 0 ? json2csv(customData) : "No data to export";

    const readable = Readable.from([data], {
      encoding: "utf-8",
    });
    res.writeHead(200, {
      "Content-Type": "text/csv",
      "content-Disposition": `attachment; filename=${fileName}.csv`,
    });

    readable.pipe(res);
  });
  
export default exportData;
