import { sendInBlueInfo, mailTrapInfo, environment } from '../config'
import ejs from 'ejs'
import nodemailer from 'nodemailer'
const path = require('path')

export default class emailService {
  public static async sendEmail(obj: any) {
    ejs.renderFile(
      path.join(__dirname, '..', 'templates', 'notificationEmail.ejs'),
      {
        receiverName: obj?.username,
        todoUrl: obj.todoUrl,
        senderName: obj?.senderName,
        senderEmail: obj?.senderEmail,
      },

      async (err: any, data: any) => {
        if (err) {
          console.log(err)
        } else {
          let transporter
          if (environment === 'production') {
            //Sendinblue
            transporter = nodemailer.createTransport({
              host: sendInBlueInfo.host,
              port: Number(sendInBlueInfo.port) || 587,
              auth: {
                user: sendInBlueInfo.username,
                pass: sendInBlueInfo.password,
              },
            })
          } else {
            // mailtrap
            transporter = nodemailer.createTransport({
              host: mailTrapInfo.host,
              port: Number(mailTrapInfo.port) || 587,
              auth: {
                user: mailTrapInfo.username,
                pass: mailTrapInfo.password,
              },
            })
          }
          const mailOptions = {
            from: sendInBlueInfo.from,
            to: obj.email,
            subject: obj.subject,
            html: data,
          }
          try {
            await transporter.sendMail(mailOptions)
          } catch (err: any) {
            console.log('ERROR: ', err.message)
          }
        }
      }
    )
  }
}
