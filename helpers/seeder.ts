import mongoose from "mongoose";
import {
  adminGroupPermissionName,
  database_secret,
  superAdminCredentials,
} from "../config";
import { Level } from "../db/models/Level";
import Permission from "../db/models/Permission";
import PermissionGroup from "../db/models/PermissionGroup";
import { Section } from "../db/models/Section";
import { levels } from "./seeds/levels";
import { adminGroupPermissions, newPermissions } from "./seeds/permissions";
import { sections } from "./seeds/sections";

//Connect to databse
const DB = database_secret;
async function connectionDB() {
  await mongoose
    .connect(DB)
    .then(() => {
      console.log("DB conenction successful!");
    })
    .catch((err) => {
      console.log(err);
    });
}
connectionDB();

let seed = async () => {
  // seed the seeds
  await seedPermissions();
  await seedPermissionGroup();
  await seedLevels();
  await seedSections();
  // await seedCashDeskPaymentPDFContent();
};

// const seedCashDeskPaymentPDFContent = async () => {
//   const title = cashDeskPayementPDFTitle;
//   const content = cashDeskPaymentPdfContent;
//   const foundPdfContent = await PdfContentRepo.findOne({ title });
//   if (foundPdfContent) {
//     console.log("PDF content already exists with this title !");
//   } else {
//     await PdfContentRepo.create({ title, content });
//     console.log("PDF content inserted successfully");
//   }
// };

//Permissions
let seedPermissions = async () => {
  let foundPermissions = await Permission.find();
  if (foundPermissions.length !== newPermissions.length) {
    const permissionsToBeseeded = findNonMatchedPermissions(
      newPermissions,
      foundPermissions
    );
    await Permission.insertMany(permissionsToBeseeded);
    console.log("Permissions inserted successfully");
  } else {
    console.log("Permissions already exists !");
  }
};

//Group permissions
let seedPermissionGroup = async () => {
  //Get all permissions
  let allPermissions = await Permission.find();
  //Get Admin Permissions
  let adminPermissions = await Permission.find({ $or: adminGroupPermissions });
  //Check permission group
  let permissionGroup = await PermissionGroup.find();

  if (permissionGroup.length == 0) {
    await PermissionGroup.insertMany([
      {
        name: superAdminCredentials.superAdminGroupName,
        permissions: allPermissions,
      },
      {
        name: adminGroupPermissionName,
        permissions: adminPermissions,
      },
    ]);
    console.log("Permission groups inserted successfully");
  } else if (permissionGroup[0]?.permissions.length !== allPermissions.length) {
    await PermissionGroup.updateOne(
      { name: superAdminCredentials.superAdminGroupName },
      {
        permissions: allPermissions,
      }
    );
    console.log("Permission group updated successfully");
  } else {
    console.log("Permission group already exists !");
  }
};

// Levels
let seedLevels = async () => {
  let foundLevels = await Level.find();
  if (foundLevels.length === 0) {
    await Level.insertMany(levels);

    console.log("Levels inserted successfully");
  } else {
    console.log("Levels already exists !");
  }
};

// Sections
let seedSections = async () => {
  let foundSections = await Section.find();
  if (foundSections.length === 0) {
    await Section.insertMany(sections);

    console.log("Sections inserted successfully");
  } else {
    console.log("Sections already exists !");
  }
};

seed()
  .then(() => {
    console.log("\x1b[32m\x1b[1m✨ Seeder completed successfully! 🎉\x1b[0m");

    process.exit(0);
  })
  .catch((error) => {
    console.error("Seeder encountered an error:", error);

    process.exit(1);
  });

function findNonMatchedPermissions(newPermissions, foundPermissions) {
  return newPermissions.filter(
    (item1) =>
      !foundPermissions.some(
        (item2) => item2.model === item1.model && item2.method === item1.method
      )
  );
}
