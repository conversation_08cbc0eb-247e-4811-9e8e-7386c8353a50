import { MethodCode, ModelCode } from "../../db/models/Permission";

export const newPermissions = [
  //USER
  {
    model: ModelCode.USER,
    method: MethodCode.CREATE,
  },
  {
    model: ModelCode.USER,
    method: MethodCode.LIST,
  },
  {
    model: ModelCode.USER,
    method: MethodCode.VIEW,
  },
  {
    model: ModelCode.USER,
    method: MethodCode.EDIT,
  },
  {
    model: ModelCode.USER,
    method: MethodCode.DELETE,
  },
  //PERMISSION
  {
    model: ModelCode.PERMISSION,
    method: MethodCode.LIST,
  },
  //PERMISSION GROUP
  {
    model: ModelCode.PERMISSION_GROUP,
    method: MethodCode.CREATE,
  },
  {
    model: ModelCode.PERMISSION_GROUP,
    method: MethodCode.LIST,
  },
  {
    model: ModelCode.PERMISSION_GROUP,
    method: MethodCode.VIEW,
  },
  {
    model: ModelCode.PERMISSION_GROUP,
    method: MethodCode.EDIT,
  },
  {
    model: ModelCode.PERMISSION_GROUP,
    method: MethodCode.DELETE,
  },
  // KPI
  {
    model: ModelCode.KPI,
    method: MethodCode.CREATE,
  },
  {
    model: ModelCode.KPI,
    method: MethodCode.LIST,
  },
  {
    model: ModelCode.KPI,
    method: MethodCode.VIEW,
  },
  {
    model: ModelCode.KPI,
    method: MethodCode.EDIT,
  },
  {
    model: ModelCode.KPI,
    method: MethodCode.DELETE,
  },
  // WORKTIME
  {
    model: ModelCode.WORKTIME,
    method: MethodCode.CREATE,
  },
  {
    model: ModelCode.WORKTIME,
    method: MethodCode.LIST,
  },
  {
    model: ModelCode.WORKTIME,
    method: MethodCode.VIEW,
  },
  {
    model: ModelCode.WORKTIME,
    method: MethodCode.EDIT,
  },
  {
    model: ModelCode.WORKTIME,
    method: MethodCode.DELETE,
  },
  // CALL
  {
    model: ModelCode.CALL,
    method: MethodCode.CREATE,
  },
  {
    model: ModelCode.CALL,
    method: MethodCode.LIST,
  },
  {
    model: ModelCode.CALL,
    method: MethodCode.VIEW,
  },
  {
    model: ModelCode.CALL,
    method: MethodCode.EDIT,
  },
  // MY_WORKTIME
  {
    model: ModelCode.MY_WORKTIME,
    method: MethodCode.CREATE,
  },
  {
    model: ModelCode.MY_WORKTIME,
    method: MethodCode.LIST,
  },
  {
    model: ModelCode.MY_WORKTIME,
    method: MethodCode.VIEW,
  },
  {
    model: ModelCode.MY_WORKTIME,
    method: MethodCode.EDIT,
  },
  {
    model: ModelCode.MY_WORKTIME,
    method: MethodCode.DELETE,
  },
  // STAT_CLIENT
  {
    model: ModelCode.STAT_CLIENT,
    method: MethodCode.CREATE,
  },
  {
    model: ModelCode.STAT_CLIENT,
    method: MethodCode.LIST,
  },
  {
    model: ModelCode.STAT_CLIENT,
    method: MethodCode.VIEW,
  },
  {
    model: ModelCode.STAT_CLIENT,
    method: MethodCode.EDIT,
  },
  {
    model: ModelCode.STAT_CLIENT,
    method: MethodCode.DELETE,
  },
  // STAT_CLIENT_RESPONSE
  {
    model: ModelCode.STAT_CLIENT_RESPONSE,
    method: MethodCode.CREATE,
  },
  {
    model: ModelCode.STAT_CLIENT_RESPONSE,
    method: MethodCode.LIST,
  },
  {
    model: ModelCode.STAT_CLIENT_RESPONSE,
    method: MethodCode.VIEW,
  },
  {
    model: ModelCode.STAT_CLIENT_RESPONSE,
    method: MethodCode.EDIT,
  },
  {
    model: ModelCode.STAT_CLIENT_RESPONSE,
    method: MethodCode.DELETE,
  },
  // ACCESS_USER_CALL
  {
    model: ModelCode.ACCESS_USER_CALL,
    method: MethodCode.VIEW,
  },
  // TODO
  {
    model: ModelCode.TODO,
    method: MethodCode.CREATE,
  },
  {
    model: ModelCode.TODO,
    method: MethodCode.LIST,
  },
  {
    model: ModelCode.TODO,
    method: MethodCode.VIEW,
  },
  {
    model: ModelCode.TODO,
    method: MethodCode.EDIT,
  },
  {
    model: ModelCode.TODO,
    method: MethodCode.DELETE,
  },
  // NOTIFICATION
  {
    model: ModelCode.NOTIFICATION,
    method: MethodCode.LIST,
  },
  {
    model: ModelCode.NOTIFICATION,
    method: MethodCode.VIEW,
  },
  {
    model: ModelCode.NOTIFICATION,
    method: MethodCode.EDIT,
  },
  // OFFICES
  {
    model: ModelCode.OFFICE,
    method: MethodCode.LIST,
  },
  // ANALYTICS
  {
    model: ModelCode.ANALYTICS,
    method: MethodCode.LIST,
  },
  {
    model: ModelCode.ANALYTICS,
    method: MethodCode.VIEW,
  },
  // TAGS
  {
    model: ModelCode.TAG,
    method: MethodCode.CREATE,
  },
  {
    model: ModelCode.TAG,
    method: MethodCode.LIST,
  },
  {
    model: ModelCode.TAG,
    method: MethodCode.VIEW,
  },
  {
    model: ModelCode.TAG,
    method: MethodCode.EDIT,
  },
  {
    model: ModelCode.TAG,
    method: MethodCode.DELETE,
  },
  // NOTES
  {
    model: ModelCode.NOTE,
    method: MethodCode.CREATE,
  },
  {
    model: ModelCode.NOTE,
    method: MethodCode.LIST,
  },
  {
    model: ModelCode.NOTE,
    method: MethodCode.VIEW,
  },
  {
    model: ModelCode.NOTE,
    method: MethodCode.EDIT,
  },
  {
    model: ModelCode.NOTE,
    method: MethodCode.DELETE,
  },
  // CODES
  {
    model: ModelCode.CODE,
    method: MethodCode.CREATE,
  },
  {
    model: ModelCode.CODE,
    method: MethodCode.LIST,
  },
  {
    model: ModelCode.CODE,
    method: MethodCode.VIEW,
  },
  {
    model: ModelCode.CODE,
    method: MethodCode.EDIT,
  },
  {
    model: ModelCode.CODE,
    method: MethodCode.DELETE,
  },
  // BALANCE ANALYTICS
  {
    model: ModelCode.BALANCE_ANALYTICS,
    method: MethodCode.VIEW,
  },
  // CASH DESK PAYMENT
  {
    model: ModelCode.CASH_DESK_PAYMENT,
    method: MethodCode.CREATE,
  },
  {
    model: ModelCode.CASH_DESK_PAYMENT,
    method: MethodCode.LIST,
  },
  {
    model: ModelCode.CASH_DESK_PAYMENT,
    method: MethodCode.VIEW,
  },
  {
    model: ModelCode.CASH_DESK_PAYMENT,
    method: MethodCode.EDIT,
  },
  {
    model: ModelCode.CASH_DESK_PAYMENT,
    method: MethodCode.DELETE,
  },
  // OPERATIONS
  {
    model: ModelCode.OPERATION,
    method: MethodCode.CREATE,
  },
  {
    model: ModelCode.OPERATION,
    method: MethodCode.LIST,
  },
  {
    model: ModelCode.OPERATION,
    method: MethodCode.VIEW,
  },
  {
    model: ModelCode.OPERATION,
    method: MethodCode.EDIT,
  },
  {
    model: ModelCode.OPERATION,
    method: MethodCode.DELETE,
  },
  {
    model: ModelCode.FOLDER,
    method: MethodCode.LIST,
  },
  {
    model: ModelCode.FOLDER,
    method: MethodCode.VIEW,
  },
  {
    model: ModelCode.FOLDER,
    method: MethodCode.EDIT,
  },
  {
    model: ModelCode.FOLDER,
    method: MethodCode.DELETE,
  },
  {
    model: ModelCode.LOGOUT_ADMIN,
    method: MethodCode.DELETE,
  },
  ,
  {
    model: ModelCode.FOLDER,
    method: MethodCode.CREATE,
  },
  {
    model: ModelCode.SESSION,
    method: MethodCode.LIST,
  },
  {
    model: ModelCode.FILE_UPLOAD,
    method: MethodCode.CREATE,
  },
  // Agent metrics
  {
    model: ModelCode.AGENT_PERFORMANCE,
    method: MethodCode.LIST,
  },
  {
    model: ModelCode.AGENT_PERFORMANCE,
    method: MethodCode.VIEW,
  },
  {
    model: ModelCode.AGENT_PERFORMANCE,
    method: MethodCode.EDIT,
  },
  {
    model: ModelCode.AGENT_PERFORMANCE,
    method: MethodCode.CREATE,
  },
  {
    model: ModelCode.AGENT_PERFORMANCE,
    method: MethodCode.DELETE,
  },

  // BONUS_AMOUNT
  {
    model: ModelCode.BONUS_AMOUNT,
    method: MethodCode.CREATE,
  },
  {
    model: ModelCode.BONUS_AMOUNT,
    method: MethodCode.LIST,
  },
  {
    model: ModelCode.BONUS_AMOUNT,
    method: MethodCode.VIEW,
  },
  {
    model: ModelCode.BONUS_AMOUNT,
    method: MethodCode.EDIT,
  },
  {
    model: ModelCode.BONUS_AMOUNT,
    method: MethodCode.DELETE,
  },
  // PDF_CONTENT
  {
    model: ModelCode.PDF_CONTENT,
    method: MethodCode.EDIT,
  },
  {
    model: ModelCode.PDF_CONTENT,
    method: MethodCode.VIEW,
  },
  {
    model: ModelCode.PDF_CONTENT,
    method: MethodCode.LIST,
  },
  // PAYMENT_NOTE
  {
    model: ModelCode.PAYMENT_NOTE,
    method: MethodCode.CREATE,
  },
  {
    model: ModelCode.PAYMENT_NOTE,
    method: MethodCode.LIST,
  },
  {
    model: ModelCode.PAYMENT_NOTE,
    method: MethodCode.VIEW,
  },
  {
    model: ModelCode.PAYMENT_NOTE,
    method: MethodCode.EDIT,
  },
  {
    model: ModelCode.PAYMENT_NOTE,
    method: MethodCode.DELETE,
  },
  // BOOK
  {
    model: ModelCode.BOOK,
    method: MethodCode.CREATE,
  },
  {
    model: ModelCode.BOOK,
    method: MethodCode.LIST,
  },
  {
    model: ModelCode.BOOK,
    method: MethodCode.VIEW,
  },
  {
    model: ModelCode.BOOK,
    method: MethodCode.EDIT,
  },
  {
    model: ModelCode.BOOK,
    method: MethodCode.DELETE,
  },
  // BOOK_STOCK
  {
    model: ModelCode.BOOK_STOCK,
    method: MethodCode.CREATE,
  },
  {
    model: ModelCode.BOOK_STOCK,
    method: MethodCode.LIST,
  },
  {
    model: ModelCode.BOOK_STOCK,
    method: MethodCode.VIEW,
  },
  {
    model: ModelCode.BOOK_STOCK,
    method: MethodCode.EDIT,
  },
  {
    model: ModelCode.BOOK_STOCK,
    method: MethodCode.DELETE,
  },
  // BOOK_SALE_TRANSACTION
  {
    model: ModelCode.BOOK_SALE_TRANSACTION,
    method: MethodCode.CREATE,
  },
  {
    model: ModelCode.BOOK_SALE_TRANSACTION,
    method: MethodCode.LIST,
  },
  {
    model: ModelCode.BOOK_SALE_TRANSACTION,
    method: MethodCode.VIEW,
  },

  // DEPARTMENT
  {
    model: ModelCode.DEPARTMENT,
    method: MethodCode.CREATE,
  },
  {
    model: ModelCode.DEPARTMENT,
    method: MethodCode.LIST,
  },
  {
    model: ModelCode.DEPARTMENT,
    method: MethodCode.VIEW,
  },
  {
    model: ModelCode.DEPARTMENT,
    method: MethodCode.EDIT,
  },
  {
    model: ModelCode.DEPARTMENT,
    method: MethodCode.DELETE,
  },

  // DEPARTMENT WORKTIME
  {
    model: ModelCode.DEPARTMENT_WORKTIME,
    method: MethodCode.CREATE,
  },
  {
    model: ModelCode.DEPARTMENT_WORKTIME,
    method: MethodCode.LIST,
  },
  {
    model: ModelCode.DEPARTMENT_WORKTIME,
    method: MethodCode.VIEW,
  },
  {
    model: ModelCode.DEPARTMENT_WORKTIME,
    method: MethodCode.EDIT,
  },
  {
    model: ModelCode.DEPARTMENT_WORKTIME,
    method: MethodCode.DELETE,
  },
  // DAILY_KPI
  {
    model: ModelCode.DAILY_KPI,
    method: MethodCode.CREATE,
  },
  {
    model: ModelCode.DAILY_KPI,
    method: MethodCode.LIST,
  },
  {
    model: ModelCode.DAILY_KPI,
    method: MethodCode.VIEW,
  },
  {
    model: ModelCode.DAILY_KPI,
    method: MethodCode.EDIT,
  },
  {
    model: ModelCode.DAILY_KPI,
    method: MethodCode.DELETE,
  },
  // DAILY_RESPONSE
  {
    model: ModelCode.DAILY_RESPONSE,
    method: MethodCode.CREATE,
  },
  {
    model: ModelCode.DAILY_RESPONSE,
    method: MethodCode.LIST,
  },
  {
    model: ModelCode.DAILY_RESPONSE,
    method: MethodCode.VIEW,
  },
  {
    model: ModelCode.DAILY_RESPONSE,
    method: MethodCode.EDIT,
  },
  {
    model: ModelCode.DAILY_RESPONSE,
    method: MethodCode.DELETE,
  },
];

export const adminGroupPermissions = [
  // KPI
  {
    model: ModelCode.KPI,
    method: MethodCode.LIST,
  },
  {
    model: ModelCode.KPI,
    method: MethodCode.VIEW,
  },
  // CALL
  {
    model: ModelCode.CALL,
    method: MethodCode.CREATE,
  },
  {
    model: ModelCode.CALL,
    method: MethodCode.LIST,
  },
  {
    model: ModelCode.CALL,
    method: MethodCode.VIEW,
  },
  {
    model: ModelCode.CALL,
    method: MethodCode.EDIT,
  },
  // MY_WORKTIME
  {
    model: ModelCode.MY_WORKTIME,
    method: MethodCode.CREATE,
  },
  {
    model: ModelCode.MY_WORKTIME,
    method: MethodCode.LIST,
  },
  {
    model: ModelCode.MY_WORKTIME,
    method: MethodCode.VIEW,
  },
  {
    model: ModelCode.MY_WORKTIME,
    method: MethodCode.EDIT,
  },
  {
    model: ModelCode.MY_WORKTIME,
    method: MethodCode.DELETE,
  },
  // STAT_CLIENT
  {
    model: ModelCode.STAT_CLIENT,
    method: MethodCode.LIST,
  },
  {
    model: ModelCode.STAT_CLIENT,
    method: MethodCode.VIEW,
  },
  // STAT_CLIENT_RESPONSE
  {
    model: ModelCode.STAT_CLIENT_RESPONSE,
    method: MethodCode.CREATE,
  },
  {
    model: ModelCode.STAT_CLIENT_RESPONSE,
    method: MethodCode.LIST,
  },
  {
    model: ModelCode.STAT_CLIENT_RESPONSE,
    method: MethodCode.VIEW,
  },
  {
    model: ModelCode.STAT_CLIENT_RESPONSE,
    method: MethodCode.EDIT,
  },
  {
    model: ModelCode.STAT_CLIENT_RESPONSE,
    method: MethodCode.DELETE,
  },
  // TODO
  {
    model: ModelCode.TODO,
    method: MethodCode.CREATE,
  },
  {
    model: ModelCode.TODO,
    method: MethodCode.LIST,
  },
  {
    model: ModelCode.TODO,
    method: MethodCode.VIEW,
  },
  {
    model: ModelCode.TODO,
    method: MethodCode.EDIT,
  },
  {
    model: ModelCode.TODO,
    method: MethodCode.DELETE,
  },
  // NOTIFICATION
  {
    model: ModelCode.NOTIFICATION,
    method: MethodCode.LIST,
  },
  {
    model: ModelCode.NOTIFICATION,
    method: MethodCode.VIEW,
  },
  {
    model: ModelCode.NOTIFICATION,
    method: MethodCode.EDIT,
  },
  // OFFICES
  {
    model: ModelCode.OFFICE,
    method: MethodCode.LIST,
  },
  {
    model: ModelCode.OFFICE,
    method: MethodCode.EDIT,
  },
  // SESSIONS
  {
    model: ModelCode.SESSION,
    method: MethodCode.LIST,
  },
  // Agent metrics
  {
    model: ModelCode.AGENT_PERFORMANCE,
    method: MethodCode.LIST,
  },
  {
    model: ModelCode.AGENT_PERFORMANCE,
    method: MethodCode.VIEW,
  },
  {
    model: ModelCode.AGENT_PERFORMANCE,
    method: MethodCode.EDIT,
  },
  {
    model: ModelCode.AGENT_PERFORMANCE,
    method: MethodCode.CREATE,
  },
  {
    model: ModelCode.AGENT_PERFORMANCE,
    method: MethodCode.DELETE,
  },
  // BONUS_AMOUNT
  {
    model: ModelCode.BONUS_AMOUNT,
    method: MethodCode.CREATE,
  },
  {
    model: ModelCode.BONUS_AMOUNT,
    method: MethodCode.LIST,
  },
  {
    model: ModelCode.BONUS_AMOUNT,
    method: MethodCode.VIEW,
  },
  {
    model: ModelCode.BONUS_AMOUNT,
    method: MethodCode.EDIT,
  },
  {
    model: ModelCode.BONUS_AMOUNT,
    method: MethodCode.DELETE,
  },
  // PDF_CONTENT
  {
    model: ModelCode.PDF_CONTENT,
    method: MethodCode.EDIT,
  },
  {
    model: ModelCode.PDF_CONTENT,
    method: MethodCode.VIEW,
  },
  {
    model: ModelCode.PDF_CONTENT,
    method: MethodCode.LIST,
  },
  // PAYMENT_NOTE
  {
    model: ModelCode.PAYMENT_NOTE,
    method: MethodCode.CREATE,
  },
  {
    model: ModelCode.PAYMENT_NOTE,
    method: MethodCode.LIST,
  },
  {
    model: ModelCode.PAYMENT_NOTE,
    method: MethodCode.VIEW,
  },
  {
    model: ModelCode.PAYMENT_NOTE,
    method: MethodCode.EDIT,
  },
  {
    model: ModelCode.PAYMENT_NOTE,
    method: MethodCode.DELETE,
  },
  // BOOK
  {
    model: ModelCode.BOOK,
    method: MethodCode.CREATE,
  },
  {
    model: ModelCode.BOOK,
    method: MethodCode.LIST,
  },
  {
    model: ModelCode.BOOK,
    method: MethodCode.VIEW,
  },
  {
    model: ModelCode.BOOK,
    method: MethodCode.EDIT,
  },
  {
    model: ModelCode.BOOK,
    method: MethodCode.DELETE,
  },
  // BOOK_STOCK
  {
    model: ModelCode.BOOK_STOCK,
    method: MethodCode.CREATE,
  },
  {
    model: ModelCode.BOOK_STOCK,
    method: MethodCode.LIST,
  },
  {
    model: ModelCode.BOOK_STOCK,
    method: MethodCode.VIEW,
  },
  {
    model: ModelCode.BOOK_STOCK,
    method: MethodCode.EDIT,
  },
  {
    model: ModelCode.BOOK_STOCK,
    method: MethodCode.DELETE,
  },
  // BOOK_SALE_TRANSACTION
  {
    model: ModelCode.BOOK_SALE_TRANSACTION,
    method: MethodCode.CREATE,
  },
  {
    model: ModelCode.BOOK_SALE_TRANSACTION,
    method: MethodCode.LIST,
  },
  {
    model: ModelCode.BOOK_SALE_TRANSACTION,
    method: MethodCode.VIEW,
  },
  {
    model: ModelCode.BOOK_SALE_TRANSACTION,
    method: MethodCode.EDIT,
  },
  {
    model: ModelCode.BOOK_SALE_TRANSACTION,
    method: MethodCode.DELETE,
  },
  // BOOKS_ANALYTICS
  {
    model: ModelCode.BOOKS_ANALYTICS,
    method: MethodCode.LIST,
  },
  {
    model: ModelCode.BOOKS_ANALYTICS,
    method: MethodCode.VIEW,
  },
  // BOOK_CASH_DESK_PAYMENT
  {
    model: ModelCode.BOOK_CASH_DESK_PAYMENT,
    method: MethodCode.CREATE,
  },
  {
    model: ModelCode.BOOK_CASH_DESK_PAYMENT,
    method: MethodCode.LIST,
  },
  {
    model: ModelCode.BOOK_CASH_DESK_PAYMENT,
    method: MethodCode.VIEW,
  },
  {
    model: ModelCode.BOOK_CASH_DESK_PAYMENT,
    method: MethodCode.EDIT,
  },
  {
    model: ModelCode.BOOK_CASH_DESK_PAYMENT,
    method: MethodCode.DELETE,
  },
  // DAILY_KPI
  {
    model: ModelCode.DAILY_KPI,
    method: MethodCode.CREATE,
  },
  {
    model: ModelCode.DAILY_KPI,
    method: MethodCode.LIST,
  },
  {
    model: ModelCode.DAILY_KPI,
    method: MethodCode.VIEW,
  },
  {
    model: ModelCode.DAILY_KPI,
    method: MethodCode.EDIT,
  },
  {
    model: ModelCode.DAILY_KPI,
    method: MethodCode.DELETE,
  },
  // DAILY_RESPONSE
  {
    model: ModelCode.DAILY_RESPONSE,
    method: MethodCode.CREATE,
  },
  {
    model: ModelCode.DAILY_RESPONSE,
    method: MethodCode.LIST,
  },
  {
    model: ModelCode.DAILY_RESPONSE,
    method: MethodCode.VIEW,
  },
  {
    model: ModelCode.DAILY_RESPONSE,
    method: MethodCode.EDIT,
  },
  {
    model: ModelCode.DAILY_RESPONSE,
    method: MethodCode.DELETE,
  },
];
