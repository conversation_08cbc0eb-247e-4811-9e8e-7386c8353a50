{"name": "branch-offices-api", "version": "1.0.0", "description": "", "main": "server.ts", "scripts": {"build": "rm -rf dist && tsc && npm run copyMySpecialFiles", "start": "node dist/server.js", "dev": "ts-node-dev --respawn server.ts", "seed": "ts-node-dev --respawn helpers/seeder.ts", "serve": "npm run build && npm run start", "copyMySpecialFiles": "copyfiles --error utils/PdfUtils/images/* dist", "codeScript": "ts-node-dev utils/payedScript.ts"}, "author": "", "license": "ISC", "dependencies": {"@types/ejs": "^3.1.5", "@types/mongoose-aggregate-paginate-v2": "^1.0.12", "@types/nodemailer": "^6.4.14", "axios": "^1.6.7", "bcryptjs": "^2.4.3", "cookie": "^0.6.0", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "date-fns": "^3.3.1", "date-fns-tz": "^3.0.0", "dayjs": "^1.11.13", "detect-browser": "^5.3.0", "dotenv": "^16.3.1", "ejs": "^3.1.9", "express": "^4.18.1", "helmet": "^7.1.0", "joi": "^17.12.1", "json-2-csv": "^5.5.1", "jsonwebtoken": "^9.0.2", "md5": "^2.3.0", "moment-timezone": "^0.5.45", "mongoose": "^6.5.2", "mongoose-aggregate-paginate-v2": "^1.1.2", "mongoose-delete": "^1.0.1", "mongoose-paginate-ts": "^1.3.1", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "nodemailer": "^6.9.10", "puppeteer": "^10.0.0", "query-string": "^8.2.0", "redis": "^4.6.13"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cookie": "^0.6.0", "@types/cookie-parser": "^1.4.7", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.5", "@types/md5": "^2.3.5", "@types/mongoose-delete": "^1.0.3", "@types/multer": "^1.4.11", "@types/node-cron": "^3.0.11", "copyfiles": "^2.4.1", "nodemon": "^3.0.2", "ts-node": "^10.9.1", "ts-node-dev": "^2.0.0", "typescript": "^4.7.4"}}