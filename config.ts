import dotenv from "dotenv";
dotenv.config({ path: "./.env" });

export const environment = process.env.NODE_ENV;
export const port = process.env.PORT;
export const database_secret = process.env.DATABASE_SECRET || "";
export const reactAppFrontUrl = process.env.REACT_APP_FRONTEND_URL || "";
export const takiAcademyUrl = process.env.TAKIACADEMY_URL || "";
export const oauth_takiacademy_url = process.env.OAUTH_TAKIACADEMY_URL;
export const adminGroupPermissionName = process.env.ADMIN_GROUP_PERMISSION_NAME;
export const redisHost = process.env.REDIS_HOST || "";
export const chromiumBrowserPath = process.env.CHROMIUM_BROWSER_PATH || "";
export const redisPort = parseInt(process.env.REDIS_PORT) || 0;
export const startMontAgentPerformance = parseInt(
  process.env.START_MONTH_AGENT_PERFORMANCE,
  10
);

export const agentWorkHoursPerDay =
  parseInt(process.env.AGENT_HOURS_PER_DAY) || 8;
export const cashDeskPayementPDFTitle =
  process.env.CASH_DESK_PAYEMENT_PDF_TITLE;

export const tokenInfo = {
  jwt_secret: process.env.JWT_SECRET || "",
  jwt_expires_in: process.env.JWT_EXPIRES_IN || 0,
  jwt_cookie_expires_in: parseInt(process.env.JWT_COOKIE_EXPIRES_IN || "0"),
};

export const superAdminCredentials = {
  superAdminPassword: process.env.SUPER_ADMIN_PASSWORD || "",
  superAdminEmail: process.env.SUPER_ADMIN_EMAIL || "",
  superAdminFirstName: process.env.SUPER_ADMIN_FIRSTNAME || "",
  superAdminLastName: process.env.SUPER_ADMIN_LASTNAME || "",
  superAdminGroupName: process.env.SUPER_ADMIN_GROUP_NAME || "",
};

export const mailTrapInfo = {
  host: process.env.MAILTRAP_EMAIL_HOST,
  port: process.env.MAILTRAP_EMAIL_PORT,
  username: process.env.MAILTRAP_EMAIL_USERNAME,
  password: process.env.MAILTRAP_EMAIL_PASSWORD,
};

export const sendInBlueInfo = {
  from: process.env.SENDINBLUE_FROM,
  host: process.env.SENDINBLUE_HOST,
  port: process.env.SENDINBLUE_PORT,
  username: process.env.SENDINBLUE_USERNAME,
  password: process.env.SENDINBLUE_PASSWORD,
};

export const apiInfo = {
  key: process.env.API_KEY,
  secret: process.env.API_SECRET,
  password: process.env.API_PASSWORD,
};
export const frontendNotificationURL = process.env.FRONTEND_NOTIFICATION_URL;
export const codeTakiacademyApiUrl = process.env.CODE_TAKIACADEMY_API_URL;
