import cookieParser from "cookie-parser";
import cors from "cors";
import express, { NextFunction, Request, Response } from "express";
import helmet from "helmet";
import morgan from "morgan";
import path from "path";
import { reactAppFrontUrl, takiAcademyUrl } from "./config";
import { ApiError, InternalError, NotFoundError } from "./helpers/ApiError";
import routes from "./routes/index";
import {
  agentPerformances,
  balancePerMonth,
  checkCalandarAuthorizationDate,
  logoutAllUsers,
  updateCodes,
} from "./utils/cronJobs";
import { getRootDirectory } from "./utils/getRootDirectory";
import redisClient from "./utils/redis";
import codeService from "./services/codeService";
const app = express();
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(cookieParser());

// REDIS
redisClient();

//CORS
const corsOptions = {
  origin: [reactAppFrontUrl, takiAcademyUrl],
  credentials: true,
};
app.use(cors(corsOptions));
app.use(helmet());
app.use(morgan("dev"));
//Routes

app.use("/public", express.static(path.join(getRootDirectory(), "public")));
app.use("/api", routes);

//Not Found Routes
app.all("*", (req, res) => {
  throw new NotFoundError(`Can't find ${req.originalUrl} on this server!`);
});

//Middleware Error Handler
app.use((err: Error | any, req: Request, res: Response, next: NextFunction) => {
  if (err instanceof ApiError) {
    ApiError.handle(err, res);
  } else {
    if (err.name === "MulterError") {
      return res.status(400).json({
        status: "fail",
        message: err.message,
      });
    }
    //Mongoose Duplicate Error
    if (err.code && err.code === 11000) {
      return res.status(400).json({
        status: "fail",
        message: err.keyPattern
          ? `${Object.keys(err.keyPattern)[0]} must be unique !`
          : `Duplicate key error. One or more documents already exist!`,
      });
    }
    //Development
    if (process.env.NODE_ENV === "development") {
      return res.status(400).json({
        status: "fail",
        message: err.message,
      });
    }
    console.log(err);
    //Production
    ApiError.handle(new InternalError(), res);
  }
});

//Cron jobs
logoutAllUsers();
updateCodes();
checkCalandarAuthorizationDate();
agentPerformances();
balancePerMonth();
export default app;
