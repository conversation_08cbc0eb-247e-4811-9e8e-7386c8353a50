NODE_ENV=development
PORT=5000
DATABASE_SECRET=mongodb://localhost:27017/branch-offices
# mongodb+srv://fares:<EMAIL>/branch-offices
REACT_APP_FRONTEND_URL=*


JWT_SECRET = secret-code-secret-code-secree-c
JWT_EXPIRES_IN = 5d
JWT_COOKIE_EXPIRES_IN = 5


# SUPER_ADMIN
SUPER_ADMIN_PASSWORD = Takiacademy123*
SUPER_ADMIN_EMAIL = <EMAIL>
SUPER_ADMIN_GROUP_NAME = Super Admin
OAUTH_TAKIACADEMY_URL = https://api.takiacademy.com/api/auth/admin-login-externally

# Mail Trap Info
MAILTRAP_EMAIL_HOST=
MAILTRAP_EMAIL_PORT=
MAILTRAP_EMAIL_USERNAME=
MAILTRAP_EMAIL_PASSWORD=

# Sendinblue Info
SENDINBLUE_FROM = 
SENDINBLUE_HOST = 
SENDINBLUE_PORT = 
SENDINBLUE_USERNAME = 
SENDINBLUE_PASSWORD = 

FRONTEND_NOTIFICATION_URL= http://localhost:8080/notifications

API_KEY = takiacademytakiacademytakiacademy
API_SECRET = takiacademytakiacademytakiacademy
API_PASSWORD = abcdabcdabcdabcdabcdabcdabcd

START_MONTH_AGENT_PERFORMANCE=3
AGENT_HOURS_PER_DAY=8

CASH_DESK_PAYEMENT_PDF_TITLE= Financial amount receipt

CHROMIUM_BROWSER_PATH = /usr/bin/chromium-browser
