import EventEmitter from "events";
import { frontendNotificationURL } from "../config";
import emailService from "../helpers/mailer";

const eventEmitter = new EventEmitter();

export const events = {
  SEND_EMAIL: "SEND_EMAIL",
};

eventEmitter.on(
  events.SEND_EMAIL,
  async ({ currentUser, doc, concernedUsers }) => {
    // Send email
    const confirmURL = `${frontendNotificationURL}/${doc}`;
    for (const user of concernedUsers) {
      await emailService.sendEmail({
        email: user.email,
        subject: "New Todo Assignment Notification",
        todoUrl: confirmURL,
        receiverName: user.name,
        senderName: currentUser.name,
        senderEmail: currentUser.email,
      });
    }
  }
);

export default eventEmitter;
