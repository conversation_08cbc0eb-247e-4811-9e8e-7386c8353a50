import EventEmitter from 'events'
import notifService from '../services/notifService'

const eventEmitter = new EventEmitter()

export const events = {
  SEND_NOTIF: 'SEND_NOTIF',
}

eventEmitter.on(
  events.SEND_NOTIF,
  ({ currentUser, doc, docModel, concernedUsers }) => {
    notifService.send({
      currentUser,
      doc,
      docModel,
      concernedUsers,
    });
  }
);

export default eventEmitter
