import { NextFunction, Request, Response } from "express";
import { RoleCode } from "../../db/models/User";
import { ForbiddenError } from "../../helpers/ApiError";
export const authorization = (model: string, method: string) => {
  return (req: Request, res: Response, next: NextFunction) => {
    let hasDirectPermission;
    let hasGroupPermission;

    if (req.user?.role !== RoleCode.SUPER_ADMIN) {
      if (!req.user?.hasAccess) {
        throw new ForbiddenError("Sorry, you do not have access to the app.");
      }

      hasDirectPermission = req.user!.extraPermissions?.find(
        (userPermissions: any) =>
          userPermissions?.model === model && userPermissions?.method === method
      );

      hasGroupPermission = req.user!.permissionGroup?.find(
        (permissionGroup: any) =>
          permissionGroup.permissions.find(
            (userPermission: any) =>
              userPermission?.model === model &&
              userPermission?.method === method
          )
      );
      if (!hasDirectPermission && !hasGroupPermission) {
        throw new ForbiddenError();
      }
    }
    next();
  };
};
