import { NextFunction, Request, Response } from "express";
import jwt, { JwtPayload, TokenExpiredError } from "jsonwebtoken";
import { AccessTokenError, AuthFailureError } from "../../helpers/ApiError";
import asyncHandler from "../asyncHandler";
import AuthRepo from "../../db/repositories/AuthRepo";
import { apiInfo, tokenInfo } from "../../config";

export const apiKeyAuth = asyncHandler(
  async (req: Request, res: Response, next: NextFunction) => {
    // 1) Getting token and check if it's there
    let token: string;
    if (req.headers["api-key"]) {
      token = req.headers["api-key"].toString();
    }
    if (!token) {
      throw new AuthFailureError("error token");
    }

    // 2) validate token
    let jwtPayload: any;
    try {
      jwtPayload = jwt.verify(token, apiInfo.password, {
        algorithms: ["HS256"],
      });
      if (jwtPayload.iss === apiInfo.key + ":" + apiInfo.secret) {
        next();
      } else {
        throw new AuthFailureError("Unauthorized");
      }
    } catch (error) {
      if (error instanceof TokenExpiredError)
        throw new AccessTokenError('error.message');
      throw error;
    }
  }
);
