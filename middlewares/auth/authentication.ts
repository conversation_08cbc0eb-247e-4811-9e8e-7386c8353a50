import { NextFunction, Request, Response } from "express";
import jwt, { TokenExpiredError } from "jsonwebtoken";
import { AccessTokenError, AuthFailureError } from "../../helpers/ApiError";
import asyncHandler from "../asyncHandler";
import AuthRepo from "../../db/repositories/AuthRepo";
import { tokenInfo } from "../../config";
import { ProtectedRequest } from "../../types/index";
import { client } from "../../utils/redis";

export const authentication = asyncHandler(
  async (req: Request, res: Response, next: NextFunction) => {
    // 1) Getting token and check if it's there
    let token;
    if (
      req.headers.authorization &&
      req.headers.authorization.startsWith("Bearer")
    ) {
      token = req.headers.authorization.split(" ")[1];
    }

    if (!token) {
      throw new AuthFailureError("error token");
    }
    // check token in redis

    // 2) validate token
    let decoded: any;
    try {
      decoded = jwt.verify(token, tokenInfo.jwt_secret);
    } catch (error) {
      if (error instanceof TokenExpiredError)
        throw new AccessTokenError(error.message);
      throw error;
    }
    // 3)check if user still exist
    const currentUser = await AuthRepo.findById(decoded.id);

    const redisToken = await client.get(`token-${decoded.id}`);

    if (!redisToken || !currentUser) {
      throw new AuthFailureError("user disconnected");
    }
    req.user = currentUser;
    next();
  }
);
