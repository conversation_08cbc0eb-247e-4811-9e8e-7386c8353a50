import { NextFunction, Request, Response } from "express";
import statClientResponseService from "../services/statClientResponseService";
import asyncHandler from "./asyncHandler";

const handleQueryMiddleware = asyncHandler(
  async (req: Request, res: Response, next: NextFunction) => {
    const query = statClientResponseService.handleQuery(req.query);
    req.query = query;
    next();
  }
);

export default handleQueryMiddleware;
