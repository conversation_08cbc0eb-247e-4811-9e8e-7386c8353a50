import { NextFunction, Request, Response } from "express";
import dailyResponseService from "../services/dailyResponseService";
import asyncHandler from "./asyncHandler";

const handleQueryMiddleware = asyncHandler(
  async (req: Request, res: Response, next: NextFunction) => {
    const query = dailyResponseService.handleQuery(req.query);
    req.query = query;
    next();
  }
);

export default handleQueryMiddleware;