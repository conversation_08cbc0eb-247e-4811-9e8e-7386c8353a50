import { NextFunction, Request, Response } from "express";
import FolderRepo from "../db/repositories/FolderRepo";
import { NotFoundError } from "../helpers/ApiError";
import asyncHandler from "./asyncHandler";

export const checkFolderIdExist = asyncHandler(
  async (req: Request, res: Response, next: NextFunction) => {
    const { folderId } = req.params;
    const folderExit = await FolderRepo.findOne({ _id: folderId });

    if (!folderExit) throw new NotFoundError("Folder not found!");
    req.params.url = folderExit.path;
    next();
  }
);
