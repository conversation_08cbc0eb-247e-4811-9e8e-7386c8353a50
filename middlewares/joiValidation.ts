import { Request, Response, NextFunction } from "express";
import <PERSON><PERSON>, { CustomHelpers } from "joi";
import { Types } from "mongoose";
import { BadRequestError } from "../helpers/ApiError";
import asyncHandler from "./asyncHandler";

export enum ValidationSource {
  BODY = "body",
  HEADER = "headers",
  QUERY = "query",
  PARAM = "params",
  // FILE = "file",
}

export const JoiObjectId = () =>
  Joi.string().custom((value: string, helpers) => {
    if (!Types.ObjectId.isValid(value)) return helpers.error("any.invalid");
    return value;
  }, "Object Id Validation");

export const validate = (
  schema: Joi.ObjectSchema,
  source: ValidationSource = ValidationSource.BODY
) =>
  asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
    const { error } = schema.validate(req[source]);
    if (error) {
      throw new BadRequestError(error.details[0].message);
    } else {
      next();
    }
  });

export const customJoi = Joi.extend((joi) => ({
  type: "startOfMonth",
  base: joi.date(),
  messages: {
    "startOfMonth.base": "{{#label}} must be a valid date",
    "startOfMonth.startOfMonth": "{{#label}} must be the start of the month",
  },
  validate(value, helpers) {
    const date = new Date(value);
    if (
      date.getUTCDate() !== 1 ||
      date.getUTCHours() !== 0 ||
      date.getUTCMinutes() !== 0 ||
      date.getUTCSeconds() !== 0 ||
      date.getUTCMilliseconds() !== 0
    ) {
      return { value, errors: helpers.error("startOfMonth.startOfMonth") };
    }
    return { value };
  },
}));

export function validateDateRange(value, helpers: CustomHelpers) {
  const { startDate, endDate } = helpers.state.ancestors[0];

  if (startDate && endDate) {
    const start = new Date(startDate);
    const end = new Date(endDate);

    if (end.getTime() - start.getTime() <= 0) {
      return helpers.message({
        custom: "endDate must be greater than startDate",
      });
    }
  }
  return value;
}
