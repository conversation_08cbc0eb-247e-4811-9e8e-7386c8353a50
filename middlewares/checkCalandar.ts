import { addDays, endOfDay } from "date-fns";
import { NextFunction, Request, Response } from "express";
import { RoleCode } from "../db/models/User";
import UserRepo from "../db/repositories/UserRepo";
import WorktimeRepo from "../db/repositories/WortimeRepo";
import { BadRequestError } from "../helpers/ApiError";
import asyncHandler from "./asyncHandler";

export const checkCalandar = asyncHandler(
  async (req: Request, res: Response, next: NextFunction) => {
    if (
      req.user?.role === RoleCode.SUPER_ADMIN ||
      !req.user?.office ||
      req.user?.isAuthorized
    ) {
      next();
    } else {
      const userId = req.user?._id;
      const currentDate = new Date();
      const dateRange = [];
      let worktimesNumber = 0;

      // Generate dates from current date to current date + 14 days
      for (let i = 0; i < 14; i++) {
        const date = new Date();
        date.setDate(currentDate.getDate() + i);
        date.setHours(0, 0, 0, 0); // Ensure the time part is set to 00:00:00
        dateRange.push(date);
      }

      for (let date of dateRange) {
        const worktime = await WorktimeRepo.findOne({
          userId,
          startDate: { $gte: date, $lte: endOfDay(date) },
        });

        if (worktime) {
          worktimesNumber++;
        }
      }

      if (worktimesNumber >= 12) {
        await UserRepo.update(userId, {
          isAuthorized: true,
          authorizedUntil: addDays(currentDate, 14),
        });
        next();
      } else {
        throw new BadRequestError(
          "Authorization denied: You must complete your calendar with at least 12 worktimes in the next 14 days !"
        );
      }
    }
  }
);
