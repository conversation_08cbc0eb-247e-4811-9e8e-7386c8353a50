import express from "express";
import {
  create,
  deleteMany,
  deleteOne,
  getAll,
  getByFilter,
  getOne,
  update,
} from "../../controllers/dailyResponseController";
import { MethodCode, ModelCode } from "../../db/models/Permission";
import DailyResponseRepo from "../../db/repositories/DailyResponseRepo";
import exportData from "../../helpers/export/exportData";
import transformer from "../../helpers/export/transformer";
import { authentication } from "../../middlewares/auth/authentication";
import { authorization } from "../../middlewares/auth/authorization";
import { validate, ValidationSource } from "../../middlewares/joiValidation";
// import handleQuery from "../../middlewares/dailyResponse";
import dailyResponseSchema from "./dailyResponsesSchema";
import handleQueryMiddleware from "../../middlewares/dailyResponse";

const router = express.Router();

// EXPORT
router.get(
  "/export",
  authentication,
  authorization(ModelCode.DAILY_RESPONSE, MethodCode.LIST),
  handleQueryMiddleware,
  exportData(DailyResponseRepo, "daily-answers", transformer.dailyResponse)
);

router.post(
  "/",
  authentication,
  authorization(ModelCode.DAILY_RESPONSE, MethodCode.CREATE),
  validate(dailyResponseSchema.create),
  create
);

router.get(
  "/",
  authentication,
  authorization(ModelCode.DAILY_RESPONSE, MethodCode.LIST),
  getAll
);

router.get(
  "/filter",
  authentication,
  authorization(ModelCode.DAILY_RESPONSE, MethodCode.LIST),
  validate(dailyResponseSchema.filter, ValidationSource.QUERY),
  getByFilter
);

router.get(
  "/:dailyResponseId",
  authentication,
  authorization(ModelCode.DAILY_RESPONSE, MethodCode.VIEW),
  validate(dailyResponseSchema.idParams, ValidationSource.PARAM),
  getOne
);

router.patch(
  "/:dailyResponseId",
  authentication,
  authorization(ModelCode.DAILY_RESPONSE, MethodCode.EDIT),
  validate(dailyResponseSchema.idParams, ValidationSource.PARAM),
  validate(dailyResponseSchema.update),
  update
);

router.delete(
  "/:dailyResponseId",
  authentication,
  authorization(ModelCode.DAILY_RESPONSE, MethodCode.DELETE),
  validate(dailyResponseSchema.idParams, ValidationSource.PARAM),
  deleteOne
);

router.delete(
  "/",
  authentication,
  authorization(ModelCode.DAILY_RESPONSE, MethodCode.DELETE),
  validate(dailyResponseSchema.deleteMany),
  deleteMany
);

export default router;
