import Joi from "joi";
import { BadRequestError } from "../../helpers/ApiError";
import { JoiObjectId } from "../../middlewares/joiValidation";

export default {
  //Check route
  idParams: Joi.object().keys({
    dailyResponseId: JoiObjectId().required(),
  }),

  //Create
  create: Joi.object().keys({
    dailyQuestion: JoiObjectId().required(),
    dailyKpis: Joi.array().items(
      Joi.object()
        .keys({
          dailyKpi: JoiObjectId().required(),
          response: Joi.array().items(Joi.any()).required(),
        })
        .required()
    ),
  }),

  //Update
  update: Joi.object().keys({
    dailyQuestion: JoiObjectId().optional(),
    dailyKpis: Joi.array()
      .items(
        Joi.object().keys({
          dailyKpi: JoiObjectId().required(),
          response: Joi.array().items(Joi.any()).required(),
        })
      )
      .optional(),
  }),

  //delete
  deleteMany: Joi.object().keys({
    dailyResponseIds: Joi.array().items(JoiObjectId()).optional(),
  }),

  filter: Joi.object().keys({
    clientContact: Joi.string().optional(),
    clientName: Joi.string().optional(),
    dailyQuestion: JoiObjectId().optional(),
    office: JoiObjectId().optional(),
    admin: Joi.array().items(JoiObjectId()).optional(),
    dailyKpis: Joi.array()
      .items(
        Joi.object().keys({
          dailyKpi: JoiObjectId().required(),
          response: Joi.array().items(Joi.any()).required(),
        })
      )
      .optional(),
    startDate: Joi.string()
      .regex(/^\d{4}\-(0?[1-9]|1[012])\-(0?[1-9]|[12][0-9]|3[01])$/)
      .messages({
        "string.pattern.base": `startDate must be a valid date at this format : YYYY-MM-DD`,
      })
      .optional(),
    endDate: Joi.string()
      .regex(/^\d{4}\-(0?[1-9]|1[012])\-(0?[1-9]|[12][0-9]|3[01])$/)
      .messages({
        "string.pattern.base": `endDate must be a valid date at this format : YYYY-MM-DD`,
      })
      .optional(),
  }),
};
