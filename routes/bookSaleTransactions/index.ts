import express from "express";
import {
  create,
  deleteOne,
  getAll,
  getOne,
  updateOne,
} from "../../controllers/bookSaleTransactionController";
import { MethodCode, ModelCode } from "../../db/models/Permission";
import { authentication } from "../../middlewares/auth/authentication";
import { authorization } from "../../middlewares/auth/authorization";
import { validate, ValidationSource } from "../../middlewares/joiValidation";
import bookSaleTransactionSchema from "./bookSaleTransactionSchema";

const router = express.Router();

router.post(
  "/",
  authentication,
  authorization(ModelCode.BOOK_SALE_TRANSACTION, MethodCode.CREATE),
  validate(bookSaleTransactionSchema.create),
  create
);

router.get(
  "/",
  authentication,
  authorization(ModelCode.BOOK_SALE_TRANSACTION, MethodCode.LIST),
  getAll
);

router.get(
  "/:bookSaleTransactionId",
  authentication,
  authorization(ModelCode.BOOK_SALE_TRANSACTION, MethodCode.VIEW),
  validate(bookSaleTransactionSchema.getOne, ValidationSource.PARAM),
  getOne
);

export default router;
