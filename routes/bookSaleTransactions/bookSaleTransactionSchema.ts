import Joi from "joi";
import { JoiObjectId } from "../../middlewares/joiValidation";

export default {
  create: Joi.object().keys({
    note: Joi.string().max(200).optional(),
    office: JoiObjectId().optional(),
    items: Joi.array().items(
      Joi.object().keys({
        book: JoiObjectId().required(),
        quantitySold: Joi.number().integer().min(0).required(),
        salePrice: Joi.number().required(),
      })
    ),
  }),
  getOne: Joi.object().keys({
    bookSaleTransactionId: JoiObjectId().required(),
  }),
};
