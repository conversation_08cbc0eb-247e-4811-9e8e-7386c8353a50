import Jo<PERSON> from "joi";
import { JoiObjectId } from "../../middlewares/joiValidation";

export default {
  update: Joi.object().keys({
    ipAddress: Joi.string().allow(null).ip().optional(),
  }),
  id: Joi.object().keys({
    officeId: JoiObjectId().required(),
  }),
  filter: Joi.object().keys({
    address: Joi.string().optional(),
    name: Joi.string().optional(),
    sort: Joi.string().optional(),
    search: Joi.string().optional(),
    startDate: Joi.string()
      .regex(/^\d{4}\-(0?[1-9]|1[012])\-(0?[1-9]|[12][0-9]|3[01])$/)
      .messages({
        "string.pattern.base": `startDate must be a valid date at this format : YYYY-MM-DD`,
      })
      .optional(),
    endDate: Joi.string()
      .regex(/^\d{4}\-(0?[1-9]|1[012])\-(0?[1-9]|[12][0-9]|3[01])$/)
      .messages({
        "string.pattern.base": `endDate must be a valid date at this format : YYYY-MM-DD`,
      })
      .optional(),
  }),
};
