import express from "express";
import { getAll, getOne, update } from "../../controllers/officeController";
import { MethodCode, ModelCode } from "../../db/models/Permission";
import OfficeRepo from "../../db/repositories/OfficeRepo";
import exportData from "../../helpers/export/exportData";
import transformer from "../../helpers/export/transformer";
import { authentication } from "../../middlewares/auth/authentication";
import { authorization } from "../../middlewares/auth/authorization";
import { validate, ValidationSource } from "../../middlewares/joiValidation";
import officeSchema from "./officeSchema";

const router = express.Router();

// EXPORT
router.get(
  "/export",
  authentication,
  authorization(ModelCode.OFFICE, MethodCode.LIST),
  validate(officeSchema.filter, ValidationSource.QUERY),
  exportData(OfficeRepo, "office", transformer.office)
);

// GET ALL
router.get(
  "/",
  authentication,
  authorization(ModelCode.OFFICE, MethodCode.LIST),
  getAll
);

// GET ALL
router.get(
  "/:officeId",
  authentication,
  authorization(ModelCode.OFFICE, MethodCode.VIEW),
  validate(officeSchema.id, ValidationSource.PARAM),
  getOne
);

// EDIT
router.patch(
  "/:officeId",
  authentication,
  authorization(ModelCode.OFFICE, MethodCode.EDIT),
  validate(officeSchema.id, ValidationSource.PARAM),
  validate(officeSchema.update),
  update
);

export default router;
