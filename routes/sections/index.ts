import express from "express";
import {
  create,
  deleteOne,
  getAll,
  getOne,
  updateOne,
} from "../../controllers/sectionController";
import { MethodCode, ModelCode } from "../../db/models/Permission";
import { authentication } from "../../middlewares/auth/authentication";
import { authorization } from "../../middlewares/auth/authorization";
import { validate, ValidationSource } from "../../middlewares/joiValidation";
import sectionSchema from "./sectionSchema";

const router = express.Router();

router.post(
  "/",
  authentication,
  authorization(ModelCode.SECTION, MethodCode.CREATE),
  validate(sectionSchema.create),
  create
);

router.get(
  "/",
  authentication,
  authorization(ModelCode.SECTION, MethodCode.VIEW),
  getAll
);

router.get(
  "/:sectionId",
  authentication,
  authorization(ModelCode.SECTION, MethodCode.VIEW),
  validate(sectionSchema.getOne, ValidationSource.PARAM),
  getOne
);

router.patch(
  "/:sectionId",
  authentication,
  authorization(ModelCode.SECTION, MethodCode.EDIT),
  validate(sectionSchema.getOne, ValidationSource.PARAM),
  validate(sectionSchema.update),
  updateOne
);

router.delete(
  "/:sectionId",
  authentication,
  authorization(ModelCode.SECTION, MethodCode.DELETE),
  validate(sectionSchema.getOne, ValidationSource.PARAM),
  deleteOne
);

export default router;
