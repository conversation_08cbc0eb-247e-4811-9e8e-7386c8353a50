import Jo<PERSON> from "joi";
import { JoiObjectId } from "../../middlewares/joiValidation";

export default {
  create: Joi.object().keys({
    name: Joi.string().max(100).required(),
    description: Joi.string().max(500).optional(),
  }),
  update: Joi.object().keys({
    name: Joi.string().max(100).optional(),
    description: Joi.string().max(500).optional(),
  }),
  getOne: Joi.object().keys({
    sectionId: JoiObjectId().required(),
  }),
};
