import express from "express";
import { authentication } from "../../middlewares/auth/authentication";
import { MethodCode, ModelCode } from "../../db/models/Permission";
import { authorization } from "../../middlewares/auth/authorization";
import { validate, ValidationSource } from "../../middlewares/joiValidation";
import {
  create,
  getAll,
  getOne,
  update,
  deleteOne,
} from "../../controllers/bookCashDeskPaymentController";
import bookCashDeskPaymentSchema from "./bookCashDeskPaymentSchema";
const router = express.Router();

router.post(
  "/",
  authentication,
  authorization(ModelCode.BOOK_CASH_DESK_PAYMENT, MethodCode.CREATE),
  validate(bookCashDeskPaymentSchema.create),
  create
);

router.get(
  "/",
  authentication,
  authorization(ModelCode.BOOK_CASH_DESK_PAYMENT, MethodCode.LIST),
  getAll
);

router.get(
  "/:booksCashDeskPaymentId",
  authentication,
  authorization(ModelCode.BOOK_CASH_DESK_PAYMENT, MethodCode.VIEW),
  validate(bookCashDeskPaymentSchema.id, ValidationSource.PARAM),
  getOne
);

router.patch(
  "/:booksCashDeskPaymentId",
  authentication,
  authorization(ModelCode.BOOK_CASH_DESK_PAYMENT, MethodCode.EDIT),
  validate(bookCashDeskPaymentSchema.id, ValidationSource.PARAM),
  validate(bookCashDeskPaymentSchema.update),
  update
);

router.delete(
  "/:booksCashDeskPaymentId",
  authentication,
  authorization(ModelCode.BOOK_STOCK, MethodCode.DELETE),
  validate(bookCashDeskPaymentSchema.id, ValidationSource.PARAM),
  deleteOne
);

export default router;
