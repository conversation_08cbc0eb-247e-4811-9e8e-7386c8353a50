import Joi from "joi";
import { JoiObjectId } from "../../middlewares/joiValidation";

export default {
  create: Joi.object().keys({
    amount: Joi.number().greater(0).required(),
    cashedTo: JoiObjectId().required(),
    notes: Joi.string().allow(null).optional(),
    office: JoiObjectId().optional(),
  }),
  update: Joi.object().keys({
    amount: Joi.number().greater(0).optional(),
    cashedTo: JoiObjectId().optional(),
    notes: Joi.string().allow(null).optional(),
    office: JoiObjectId().optional(),
  }),
  id: Joi.object().keys({
    booksCashDeskPaymentId: JoiObjectId().required(),
  }),
  filter: Joi.object().keys({
    id: JoiObjectId().optional(),
    amount: Joi.number().optional(),
    search: Joi.string().optional(),
    sort: Joi.string().optional(),
    cashedTo: JoiObjectId().optional(),
    cashedBy: JoiObjectId().optional(),
    office: JoiObjectId().optional(),
    notes: Joi.string().optional(),
    startDate: Joi.string()
      .regex(/^\d{4}\-(0?[1-9]|1[012])\-(0?[1-9]|[12][0-9]|3[01])$/)
      .messages({
        "string.pattern.base": `startDate must be a valid date at this format : YYYY-MM-DD`,
      })
      .optional(),
    endDate: Joi.string()
      .regex(/^\d{4}\-(0?[1-9]|1[012])\-(0?[1-9]|[12][0-9]|3[01])$/)
      .messages({
        "string.pattern.base": `endDate must be a valid date at this format : YYYY-MM-DD`,
      })
      .optional(),
  }),
};
