import express from "express";
import {
  create,
  deleteMany,
  deleteOne,
  getAll,
  getOne,
  updateDailyKpi,
} from "../../controllers/dailyKpiController";
import { MethodCode, ModelCode } from "../../db/models/Permission";
import KpiRepo from "../../db/repositories/KpiRepo";
import exportData from "../../helpers/export/exportData";
import transformer from "../../helpers/export/transformer";
import { authentication } from "../../middlewares/auth/authentication";
import { authorization } from "../../middlewares/auth/authorization";
import { validate, ValidationSource } from "../../middlewares/joiValidation";
import dailyKpiSchema from "./dailyKpiSchema";

const router = express.Router();

// EXPORT
router.get(
  "/export",
  authentication,
  authorization(ModelCode.DAILY_KPI, MethodCode.LIST),
  validate(dailyKpiSchema.filter, ValidationSource.QUERY),
  exportData(KpiRepo, "kpis", transformer.kpi)
);

//POST
router.post(
  "/",
  authentication,
  authorization(ModelCode.DAILY_KPI, MethodCode.CREATE),
  validate(dailyKpiSchema.create),
  create
);

// GET ALL
router.get(
  "/",
  authentication,
  authorization(ModelCode.DAILY_KPI, MethodCode.LIST),
  getAll
);

// GET ONE
router.get(
  "/:kpiId",
  authentication,
  authorization(ModelCode.DAILY_KPI, MethodCode.VIEW),
  validate(dailyKpiSchema.getOne, ValidationSource.PARAM),
  getOne
);

// UPDATE ONE
router.patch(
  "/:kpiId",
  authentication,
  authorization(ModelCode.DAILY_KPI, MethodCode.EDIT),
  validate(dailyKpiSchema.getOne, ValidationSource.PARAM),
  validate(dailyKpiSchema.update),
  updateDailyKpi
);

// DELETE ONE
router.delete(
  "/:kpiId",
  authentication,
  authorization(ModelCode.DAILY_KPI, MethodCode.DELETE),
  validate(dailyKpiSchema.getOne, ValidationSource.PARAM),
  deleteOne
);

// DELETE MANY
router.delete(
  "/",
  authentication,
  authorization(ModelCode.DAILY_KPI, MethodCode.DELETE),
  validate(dailyKpiSchema.delete, ValidationSource.BODY),
  deleteMany
);

export default router;
