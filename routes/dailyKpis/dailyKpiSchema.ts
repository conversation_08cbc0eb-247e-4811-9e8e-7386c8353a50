import Joi from "joi";
import { JoiObjectId } from "../../middlewares/joiValidation";

export default {
  create: Joi.object().keys({
    name: Joi.string().min(3).max(30).required(),
    label: Joi.string(),
    frontType: Joi.string()

      .valid("textarea", "input", "checkbox", "select", "radio", "switch")
      .required(),
    choices: Joi.array().items(Joi.any()).optional(),
    isRequired: Joi.boolean().optional(),
  }),
  update: Joi.object().keys({
    name: Joi.string().min(3).max(30).optional(),
    label: Joi.string().optional(),
    frontType: Joi.string()

      .valid("textarea", "checkbox", "select", "input", "radio", "switch")
      .optional(),
    choices: Joi.array().items(Joi.any()).optional(),
    isRequired: Joi.boolean().optional(),
  }),
  getOne: Joi.object().keys({
    kpiId: JoiObjectId().required(),
  }),
  delete: Joi.object().keys({
    data: Joi.array().min(1).items(JoiObjectId()).required(),
  }),
  filter: Joi.object().keys({
    name: Joi.string().min(3).max(30).optional(),
    label: Joi.string().optional(),
    search: Joi.string().optional(),
    sort: Joi.string().optional(),
    frontType: Joi.string()

      .valid("textarea", "input", "checkbox", "select", "radio", "switch")
      .optional(),
    isRequired: Joi.boolean().optional(),
    startDate: Joi.string()
      .regex(/^\d{4}\-(0?[1-9]|1[012])\-(0?[1-9]|[12][0-9]|3[01])$/)
      .messages({
        "string.pattern.base": `startDate must be a valid date at this format : YYYY-MM-DD`,
      })
      .optional(),
    endDate: Joi.string()
      .regex(/^\d{4}\-(0?[1-9]|1[012])\-(0?[1-9]|[12][0-9]|3[01])$/)
      .messages({
        "string.pattern.base": `endDate must be a valid date at this format : YYYY-MM-DD`,
      })
      .optional(),
  }),
};
