import express from 'express'
import {
  getAll,
  getUnreadNotifications,
  markAllMyNotificationsAsRead,
  markOneNotificationAsRead,
} from '../../controllers/notificationController'
import { MethodCode, ModelCode } from '../../db/models/Permission'
import { authentication } from '../../middlewares/auth/authentication'
import { authorization } from '../../middlewares/auth/authorization'

const router = express.Router()

// GET ALL
router.get(
  '/',
  authentication,
  authorization(ModelCode.NOTIFICATION, MethodCode.LIST),
  getAll
)

// UPDATE ALL
router.patch(
  '/',
  authentication,
  authorization(ModelCode.NOTIFICATION, MethodCode.EDIT),
  markAllMyNotificationsAsRead
)

// UPDATE ONE
router.patch(
  '/:notificationId',
  authentication,
  authorization(ModelCode.NOTIFICATION, MethodCode.EDIT),
  markOneNotificationAsRead
)

// GET the number of unread notifications
router.get(
  '/unread',
  authentication,
  authorization(ModelCode.NOTIFICATION, MethodCode.LIST),
  getUnreadNotifications
)

export default router
