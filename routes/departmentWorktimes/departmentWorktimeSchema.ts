import Joi from "joi";
import { JoiObjectId } from "../../middlewares/joiValidation";

export default {
  create: Joi.object()
    .keys({
      department: Joi.array().items(JoiObjectId()).min(1).required(),
      startDate: Joi.date().iso().required(),
      endDate: Joi.date().iso().required(),
      startHour: Joi.any(),
      endHour: Joi.any(),
    })
    .custom((value, helpers) => {
      const { startHour, endHour } = value;

      const start = new Date(`1970-01-01T${startHour}:00`);
      const end = new Date(`1970-01-01T${endHour}:00`);

      if (start >= end) {
        return helpers.message({
          custom: "startHour must be earlier than endHour",
        });
      }

      return value;
    }),
  update: Joi.object()
    .keys({
      department: Joi.array().items(JoiObjectId()).min(1),
      startDate: Joi.date().iso(),
      endDate: Joi.date().iso(),
      startHour: Joi.any(),
      endHour: Joi.any(),
    })
    .custom((value, helpers) => {
      const { startHour, endHour } = value;

      if (startHour && endHour) {
        const start = new Date(`1970-01-01T${startHour}:00`);
        const end = new Date(`1970-01-01T${endHour}:00`);

        if (start >= end) {
          return helpers.message({
            custom: "startHour must be earlier than endHour",
          });
        }
      }

      return value;
    }),

  getOne: Joi.object().keys({
    departmentWorktimeId: JoiObjectId().required(),
  }),

  getOneByFilter: Joi.object().keys({
    departmentId: JoiObjectId().required(),
    year: Joi.number().integer().min(1970).required(),
    month: Joi.number().min(1).max(12).required(),
  }),
};
