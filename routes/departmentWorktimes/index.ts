import express from "express";
import {
  create,
  getOne,
  getOneByFilter,
  getAll,
  updateOne,
  deleteOne,
} from "../../controllers/departmentWorktimeController";
import { MethodCode, ModelCode } from "../../db/models/Permission";
import { authentication } from "../../middlewares/auth/authentication";
import { authorization } from "../../middlewares/auth/authorization";
import { validate, ValidationSource } from "../../middlewares/joiValidation";
import departmentWorktimeSchema from "./departmentWorktimeSchema";

const router = express.Router();

// CREATE
router.post(
  "/",
  authentication,
  authorization(ModelCode.DEPARTMENT_WORKTIME, MethodCode.CREATE),
  validate(departmentWorktimeSchema.create),
  create
);

// Get All
router.get(
  "/",
  authentication,
  authorization(ModelCode.DEPARTMENT_WORKTIME, MethodCode.LIST),
  getAll
);

// Get One by department, year & month
router.get(
  "/filter",
  authentication,
  authorization(ModelCode.DEPARTMENT_WORKTIME, MethodCode.VIEW),
  validate(departmentWorktimeSchema.getOneByFilter, ValidationSource.QUERY),
  getOneByFilter
);

// Get One
router.get(
  "/:departmentWorktimeId",
  authentication,
  authorization(ModelCode.DEPARTMENT_WORKTIME, MethodCode.VIEW),
  validate(departmentWorktimeSchema.getOne, ValidationSource.PARAM),
  getOne
);

router.patch(
  "/:departmentWorktimeId",
  authentication,
  authorization(ModelCode.DEPARTMENT, MethodCode.EDIT),
  validate(departmentWorktimeSchema.getOne, ValidationSource.PARAM),
  validate(departmentWorktimeSchema.update),
  updateOne
);

router.delete(
  "/:departmentWorktimeId",
  authentication,
  authorization(ModelCode.DEPARTMENT, MethodCode.DELETE),
  validate(departmentWorktimeSchema.getOne, ValidationSource.PARAM),
  deleteOne
);
export default router;
