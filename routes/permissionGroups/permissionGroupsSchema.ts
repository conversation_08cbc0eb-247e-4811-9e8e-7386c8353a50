import Joi from 'joi'
import { JoiObjectId } from '../../middlewares/joiValidation'
export default {
  //Check route
  idParams: Joi.object().keys({
    permissionGroupId: JoiObjectId().required(),
  }),
  //Create
  create: Joi.object().keys({
    name: Joi.string().required(),
    permissions: Joi.array().items(JoiObjectId()).optional(),
  }),
  //Update
  update: Joi.object().keys({
    name: Joi.string().optional(),
    permissions: Joi.array().items(JoiObjectId()).optional(),
  }),
}
