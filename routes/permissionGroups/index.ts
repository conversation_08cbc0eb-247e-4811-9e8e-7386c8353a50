import express from "express";
import {
  create,
  deleteOne,
  getAll,
  getOne,
  update,
} from "../../controllers/permissionGroupController";
import { MethodCode, ModelCode } from "../../db/models/Permission";
import { authentication } from "../../middlewares/auth/authentication";
import { authorization } from "../../middlewares/auth/authorization";
import { validate, ValidationSource } from "../../middlewares/joiValidation";
import permissionGroupsSchema from "./permissionGroupsSchema";

const router = express.Router();

router.post(
  "/",
  authentication,
  authorization(ModelCode.PERMISSION_GROUP, MethodCode.CREATE),
  validate(permissionGroupsSchema.create),
  create
);

router.get(
  "/",
  authentication,
  authorization(ModelCode.PERMISSION_GROUP, MethodCode.LIST),
  getAll
);

router.get(
  "/:permissionGroupId",
  authentication,
  authorization(ModelCode.PERMISSION_GROUP, MethodCode.VIEW),
  validate(permissionGroupsSchema.idParams, ValidationSource.PARAM),
  getOne
);

router.patch(
  "/:permissionGroupId",
  authentication,
  authorization(ModelCode.PERMISSION_GROUP, MethodCode.EDIT),
  validate(permissionGroupsSchema.idParams, ValidationSource.PARAM),
  validate(permissionGroupsSchema.update),
  update
);

router.delete(
  "/:permissionGroupId",
  authentication,
  authorization(ModelCode.PERMISSION_GROUP, MethodCode.DELETE),
  validate(permissionGroupsSchema.idParams, ValidationSource.PARAM),
  deleteOne
);

export default router;
