import express from "express";
import {
  create,
  getAll,
  getOne,
  updateOne,
  deleteOne,
} from "../../controllers/departmentController";
import { MethodCode, ModelCode } from "../../db/models/Permission";
import { authentication } from "../../middlewares/auth/authentication";
import { authorization } from "../../middlewares/auth/authorization";
import { validate, ValidationSource } from "../../middlewares/joiValidation";
import departmentSchema from "./departmentSchema";

const router = express.Router();

// CREATE
router.post(
  "/",
  authentication,
  authorization(ModelCode.DEPARTMENT, MethodCode.CREATE),
  validate(departmentSchema.create),
  create
);

router.get(
  "/",
  authentication,
  authorization(ModelCode.DEPARTMENT, MethodCode.VIEW),
  getAll
);

// Get One
router.get(
  "/:departmentId",
  authentication,
  authorization(ModelCode.DEPARTMENT, MethodCode.VIEW),
  validate(departmentSchema.getOne, ValidationSource.PARAM),
  getOne
);

router.patch(
  "/:departmentId",
  authentication,
  authorization(ModelCode.DEPARTMENT, MethodCode.EDIT),
  validate(departmentSchema.getOne, ValidationSource.PARAM),
  validate(departmentSchema.update),
  updateOne
);

router.delete(
  "/:departmentId",
  authentication,
  authorization(ModelCode.DEPARTMENT, MethodCode.DELETE),
  validate(departmentSchema.getOne, ValidationSource.PARAM),
  deleteOne
);

export default router;
