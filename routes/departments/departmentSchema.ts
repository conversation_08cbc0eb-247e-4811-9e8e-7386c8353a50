import Joi from "joi";
import { JoiObjectId } from "../../middlewares/joiValidation";

export default {
  create: Joi.object().keys({
    name: Joi.string().min(3).max(30).required(),
    description: Joi.string().max(255).optional(),
  }),
  update: Joi.object().keys({
    name: Joi.string().min(3).max(30).optional(),
    description: Joi.string().max(255).optional(),
  }),

  getOne: Joi.object().keys({
    departmentId: JoiObjectId().required(),
  }),
};
