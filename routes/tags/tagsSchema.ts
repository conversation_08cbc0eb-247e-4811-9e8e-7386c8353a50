import Joi from "joi";
import { JoiObjectId } from "../../middlewares/joiValidation";

export default {
  // Create
  create: Joi.object().keys({
    title: Joi.string().required(),
    color: Joi.string()
      .regex(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/)
      .messages({
        "string.pattern.base": `Color must be a valid hexadecimal color code, EXP: #FFFFFF`,
      })
      .required(),
  }),
  // Update
  update: Joi.object({
    title: Joi.string().optional(),
    color: Joi.string()
      .regex(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/)
      .messages({
        "string.pattern.base": `Color must be a valid hexadecimal color code, EXP: #FFFFFF`,
      })
      .optional(),
  }),

  //Check route
  idParams: Joi.object().keys({
    tagId: JoiObjectId().required(),
  }),
};
