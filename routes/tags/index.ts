import express from "express";
import {
  createTags,
  deleteTag,
  getAllTags,
  getOne,
  updateTag,
} from "../../controllers/tagController";
import { MethodCode, ModelCode } from "../../db/models/Permission";
import { authentication } from "../../middlewares/auth/authentication";
import { authorization } from "../../middlewares/auth/authorization";
import { validate, ValidationSource } from "../../middlewares/joiValidation";
import tagsSchema from "./tagsSchema";

const router = express.Router();

router.post(
  "/",
  authentication,
  authorization(ModelCode.TAG, MethodCode.CREATE),
  validate(tagsSchema.create),
  createTags
);

router.get(
  "/",
  authentication,
  authorization(ModelCode.TAG, MethodCode.LIST),
  getAllTags
);

router.get(
  "/:tagId",
  authentication,
  authorization(ModelCode.TAG, MethodCode.LIST),
  validate(tagsSchema.idParams, ValidationSource.PARAM),
  getOne
);

router.patch(
  "/:tagId",
  authentication,
  authorization(ModelCode.TAG, MethodCode.EDIT),
  validate(tagsSchema.idParams, ValidationSource.PARAM),
  validate(tagsSchema.update),
  updateTag
);
router.delete(
  "/:tagId",
  authentication,
  authorization(ModelCode.TAG, MethodCode.DELETE),
  validate(tagsSchema.idParams, ValidationSource.PARAM),
  deleteTag
);

export default router;
