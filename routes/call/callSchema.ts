import Joi from "joi";
import { JoiObjectId } from "../../middlewares/joiValidation";

export default {
  create: Joi.object().keys({
    date: Joi.string()
      .regex(/^\d{4}\-(0?[1-9]|1[012])\-(0?[1-9]|[12][0-9]|3[01])$/)
      .messages({
        "string.pattern.base": `startDate must be a valid date at this format : YYYY-MM-DD`,
      })
      .required(),
    calls: {
      maked: Joi.number().required().min(0),
      received: Joi.number().required().min(0),
    },
  }),
  update: Joi.object().keys({
    date: Joi.date().iso().required(),
    calls: {
      maked: Joi.number().required().min(0),
      received: Joi.number().required().min(0),
    },
  }),
  getByDate: Joi.object().keys({
    date: Joi.date().iso().required(),
  }),
  getAll: Joi.object().keys({
    startDate: Joi.date().iso(),
    endDate: Joi.date().iso(),
    limit: Joi.number().required(),
    page: Joi.number().required(),
    sort: Joi.string().optional(),
    search: Joi.string().optional(),
  }),

  filter: Joi.object()
    .keys({
      period: Joi.string().valid("day", "week", "month").optional(),
      startDate: Joi.date().iso().required(),
      endDate: Joi.date().iso().required(),
      user: JoiObjectId(),
      office: JoiObjectId(),
      limit: Joi.number().optional(),
      page: Joi.number().optional(),
      sort: Joi.string().optional(),
    })
    .with("startDate", "endDate"), 
};
