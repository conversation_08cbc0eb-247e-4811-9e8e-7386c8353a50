import express from "express";
import {
  createCall,
  getAllCalls,
  getByDate,
  getByFilter,
  getMyCalls,
  getOneCall,
  updateCall,
} from "../../controllers/callController";
import { MethodCode, ModelCode } from "../../db/models/Permission";
import CallRepo from "../../db/repositories/CallRepo";
import exportData from "../../helpers/export/exportData";
import transformer from "../../helpers/export/transformer";
import { authentication } from "../../middlewares/auth/authentication";
import { authorization } from "../../middlewares/auth/authorization";
import { validate, ValidationSource } from "../../middlewares/joiValidation";
import callSchema from "./callSchema";

const router = express.Router();

router.use(authentication);

// EXPORT
router.get(
  "/export",
  authentication,
  authorization(ModelCode.CALL, MethodCode.LIST),
  validate(callSchema.filter, ValidationSource.QUERY),
  exportData(CallRepo, "calls", transformer.call)
);

router.get(
  "/",
  authorization(ModelCode.CALL, MethodCode.LIST),
  validate(callSchema.getAll, ValidationSource.QUERY),
  getAllCalls
);

router.post(
  "/",
  authorization(ModelCode.CALL, MethodCode.LIST),
  validate(callSchema.create),
  createCall
);
router.get("/me", authorization(ModelCode.CALL, MethodCode.VIEW), getMyCalls);
router.get(
  "/date",
  authorization(ModelCode.CALL, MethodCode.VIEW),
  validate(callSchema.getByDate, ValidationSource.QUERY),
  getByDate
);
router.get(
  "/filter",
  authorization(ModelCode.ACCESS_USER_CALL, MethodCode.VIEW),
  validate(callSchema.filter, ValidationSource.QUERY),
  getByFilter
);
router.get(
  "/:callId",
  authorization(ModelCode.CALL, MethodCode.VIEW),
  getOneCall
);
router.patch(
  "/:callId",
  authorization(ModelCode.CALL, MethodCode.EDIT),
  validate(callSchema.update),
  updateCall
);

export default router;
