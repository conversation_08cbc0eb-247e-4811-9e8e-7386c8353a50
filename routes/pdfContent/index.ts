import express from "express";
import {
  getOne,
  updateOne,
  getAll,
} from "../../controllers/pdfContentController";
import { MethodCode, ModelCode } from "../../db/models/Permission";
import { authentication } from "../../middlewares/auth/authentication";
import { authorization } from "../../middlewares/auth/authorization";
import { validate, ValidationSource } from "../../middlewares/joiValidation";
import pdfContentSchema from "./pdfContentSchema";

const router = express.Router();

router.patch(
  "/:pdfContentId",
  authentication,
  authorization(ModelCode.PDF_CONTENT, MethodCode.EDIT),
  validate(pdfContentSchema.getOne, ValidationSource.PARAM),
  validate(pdfContentSchema.update),
  updateOne
);

router.get(
  "/",
  authentication,
  authorization(ModelCode.PDF_CONTENT, MethodCode.LIST),
  getAll
);

router.get(
  "/:pdfContentId",
  authentication,
  authorization(ModelCode.PDF_CONTENT, MethodCode.VIEW),
  validate(pdfContentSchema.getOne, ValidationSource.PARAM),
  getOne
);

export default router;
