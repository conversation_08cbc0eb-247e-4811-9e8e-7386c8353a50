import express from "express";
import {
  create,
  deleteMany,
  deleteOne,
  getAll,
  getOne,
  updateOne,
} from "../../controllers/paymentNoteController";
import { MethodCode, ModelCode } from "../../db/models/Permission";
import { authentication } from "../../middlewares/auth/authentication";
import { authorization } from "../../middlewares/auth/authorization";
import { validate, ValidationSource } from "../../middlewares/joiValidation";
import paymentNoteSchema from "./paymentNoteSchema";

const router = express.Router();

router.post(
  "/",
  authentication,
  authorization(ModelCode.PAYMENT_NOTE, MethodCode.CREATE),
  validate(paymentNoteSchema.create),
  create
);

router.get(
  "/",
  authentication,
  authorization(ModelCode.PAYMENT_NOTE, MethodCode.LIST),
  getAll
);

router.get(
  "/:paymentNoteId",
  authentication,
  authorization(ModelCode.PAYMENT_NOTE, MethodCode.VIEW),
  validate(paymentNoteSchema.id, ValidationSource.PARAM),
  getOne
);

router.patch(
  "/:paymentNoteId",
  authentication,
  authorization(ModelCode.PAYMENT_NOTE, MethodCode.EDIT),
  validate(paymentNoteSchema.id, ValidationSource.PARAM),
  validate(paymentNoteSchema.update),
  updateOne
);

router.delete(
  "/:paymentNoteId",
  authentication,
  authorization(ModelCode.PAYMENT_NOTE, MethodCode.EDIT),
  validate(paymentNoteSchema.id, ValidationSource.PARAM),
  deleteOne
);

router.delete(
  "/",
  authentication,
  authorization(ModelCode.PAYMENT_NOTE, MethodCode.EDIT),
  validate(paymentNoteSchema.deleteMany),
  deleteMany
);

export default router;
