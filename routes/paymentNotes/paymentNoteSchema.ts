import Jo<PERSON> from "joi";
import { JoiObjectId } from "../../middlewares/joiValidation";

export default {
  // Create
  create: Joi.object().keys({
    name: Joi.string().min(1).max(30).required(),
    description: Joi.string().min(1).max(255).required(),
  }),

  // update
  update: Joi.object().keys({
    name: Joi.string().min(1).max(30).optional(),
    description: Joi.string().min(1).max(255).optional(),
  }),
  // deleteMany
  deleteMany: Joi.object().keys({
    paymentNoteIds: Joi.array().items(JoiObjectId()).required(),
  }),

  // id
  id: Joi.object().keys({
    paymentNoteId: JoiObjectId().required(),
  }),
};
