import Joi from "joi";
import { JoiObjectId } from "../../middlewares/joiValidation";
export default {
  //dates
  dates: Joi.object().keys({
    startDate: Joi.string()
      .regex(/^\d{4}\-(0?[1-9]|1[012])\-(0?[1-9]|[12][0-9]|3[01])$/)
      .messages({
        "string.pattern.base": `startDate must be a valid date at this format : YYYY-MM-DD`,
      })
      .optional(),
    endDate: Joi.string()
      .regex(/^\d{4}\-(0?[1-9]|1[012])\-(0?[1-9]|[12][0-9]|3[01])$/)
      .messages({
        "string.pattern.base": `endDate must be a valid date at this format : YYYY-MM-DD`,
      })
      .optional(),
  }),

  requiredDates: Joi.object().keys({
    startDate: Joi.string()
      .regex(/^\d{4}\-(0?[1-9]|1[012])\-(0?[1-9]|[12][0-9]|3[01])$/)
      .messages({
        "string.pattern.base": `startDate must be a valid date at this format : YYYY-MM-DD`,
      })
      .required(),
    endDate: Joi.string()
      .regex(/^\d{4}\-(0?[1-9]|1[012])\-(0?[1-9]|[12][0-9]|3[01])$/)
      .messages({
        "string.pattern.base": `endDate must be a valid date at this format : YYYY-MM-DD`,
      })
      .required(),
  }),

  balance: Joi.object()
    .keys({
      startDate: Joi.string()
        .regex(/^\d{4}\-(0?[1-9]|1[012])\-(0?[1-9]|[12][0-9]|3[01])$/)
        .messages({
          "string.pattern.base": `startDate must be a valid date at this format : YYYY-MM-DD`,
        })
        .optional(),
      endDate: Joi.string()
        .regex(/^\d{4}\-(0?[1-9]|1[012])\-(0?[1-9]|[12][0-9]|3[01])$/)
        .messages({
          "string.pattern.base": `endDate must be a valid date at this format : YYYY-MM-DD`,
        })
        .optional(),
      office: JoiObjectId().optional(),
    })
    .with("startDate", "endDate")
    .with("endDate", "startDate"),

  balanceAnalyticsPerMonth: Joi.object().keys({
    year: Joi.number().greater(2023).required(),
    month: Joi.number().integer().min(0).max(11).optional(),
    sort: Joi.string().optional(),
  }),

  startEndDate: Joi.object()
    .keys({
      startDate: Joi.string()
        .regex(/^\d{4}\-(0?[1-9]|1[012])\-(0?[1-9]|[12][0-9]|3[01])$/)
        .messages({
          "string.pattern.base": `startDate must be a valid date at this format : YYYY-MM-DD`,
        })
        .optional(),
      endDate: Joi.string()
        .regex(/^\d{4}\-(0?[1-9]|1[012])\-(0?[1-9]|[12][0-9]|3[01])$/)
        .messages({
          "string.pattern.base": `endDate must be a valid date at this format : YYYY-MM-DD`,
        })
        .optional(),
    })
    .with("startDate", "endDate")
    .with("endDate", "startDate"),

  monthYear: Joi.object().keys({
    month: Joi.string()
      .valid(
        "january",
        "february",
        "march",
        "april",
        "may",
        "june",
        "july",
        "august",
        "september",
        "october",
        "november",
        "december"
      )
      .optional(),
    year: Joi.number().greater(2023).required(),
  }),

  kpiMonthYear: Joi.object().keys({
    kpiId: JoiObjectId().required(),
    month: Joi.string()
      .valid(
        "january",
        "february",
        "march",
        "april",
        "may",
        "june",
        "july",
        "august",
        "september",
        "october",
        "november",
        "december"
      )
      .optional(),
    year: Joi.number().greater(2023).required(),
  }),

  kpi: Joi.object()
    .keys({
      kpiId: JoiObjectId().required(),
      startDate: Joi.string()
        .regex(/^\d{4}\-(0?[1-9]|1[012])\-(0?[1-9]|[12][0-9]|3[01])$/)
        .messages({
          "string.pattern.base": `startDate must be a valid date at this format : YYYY-MM-DD`,
        })
        .optional(),
      endDate: Joi.string()
        .regex(/^\d{4}\-(0?[1-9]|1[012])\-(0?[1-9]|[12][0-9]|3[01])$/)
        .messages({
          "string.pattern.base": `endDate must be a valid date at this format : YYYY-MM-DD`,
        })
        .optional(),
    })
    .with("startDate", "endDate")
    .with("endDate", "startDate"),
};
