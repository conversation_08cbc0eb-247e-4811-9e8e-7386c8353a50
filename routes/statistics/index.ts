import express from "express";
import {
  getBalanceAnalytics,
  getBalanceAnalyticsPerMonth,
  getBalanceAnalyticsPerMonthPerOffice,
  getBalanceAnalyticsPerOffice,
  getCashDeskAnalytics,
  getCodesAnalytics,
  getExpensesAnalytics,
  getKpiResponseStat,
  getKpiResponseStatByRangeDate,
  getNbStatClientResponsesByOffice,
  getNbStatClientResponsesByStatClient,
  getNbStatClientResponsesByStatClientByOffice,
  getNumbers,
  totalNotPayedCodes,
} from "../../controllers/statisticController";
import { MethodCode, ModelCode } from "../../db/models/Permission";
import { authentication } from "../../middlewares/auth/authentication";
import { authorization } from "../../middlewares/auth/authorization";
import { validate, ValidationSource } from "../../middlewares/joiValidation";
import statisticSchema from "./statisticSchema";
const router = express.Router();

router.get(
  "/",
  authentication,
  authorization(ModelCode.ANALYTICS, MethodCode.LIST),
  validate(statisticSchema.dates, ValidationSource.QUERY),
  getNumbers
);

router.get(
  "/balance",
  authentication,
  authorization(ModelCode.BALANCE_ANALYTICS, MethodCode.VIEW),
  validate(statisticSchema.balance, ValidationSource.QUERY),
  getBalanceAnalytics
);

router.get(
  "/balance-codes",
  authentication,
  authorization(ModelCode.BALANCE_ANALYTICS, MethodCode.VIEW),
  validate(statisticSchema.balance, ValidationSource.QUERY),
  getCodesAnalytics
);

router.get(
  "/balance-expenses",
  authentication,
  authorization(ModelCode.BALANCE_ANALYTICS, MethodCode.VIEW),
  validate(statisticSchema.balance, ValidationSource.QUERY),
  getExpensesAnalytics
);

router.get(
  "/balance-cash-desks",
  authentication,
  authorization(ModelCode.BALANCE_ANALYTICS, MethodCode.VIEW),
  validate(statisticSchema.balance, ValidationSource.QUERY),
  getCashDeskAnalytics
);

router.get(
  "/balance-per-month",
  authentication,
  authorization(ModelCode.ANALYTICS, MethodCode.VIEW),
  validate(statisticSchema.balanceAnalyticsPerMonth, ValidationSource.QUERY),
  getBalanceAnalyticsPerMonth
);

router.get(
  "/balance-per-month-per-office",
  authentication,
  authorization(ModelCode.BALANCE_ANALYTICS, MethodCode.VIEW),
  validate(statisticSchema.balanceAnalyticsPerMonth, ValidationSource.QUERY),
  getBalanceAnalyticsPerMonthPerOffice
);
router.get(
  "/nb-stat-client-responses-by-office",
  authentication,
  authorization(ModelCode.ANALYTICS, MethodCode.LIST),
  validate(statisticSchema.dates, ValidationSource.QUERY),
  getNbStatClientResponsesByOffice
);
router.get(
  "/nb-stat-client-response",
  authentication,
  authorization(ModelCode.ANALYTICS, MethodCode.VIEW),
  validate(statisticSchema.startEndDate, ValidationSource.QUERY),
  getNbStatClientResponsesByStatClientByOffice
);

router.get(
  "/nb-stat-client-response-by-stat-client",
  authentication,
  authorization(ModelCode.ANALYTICS, MethodCode.VIEW),
  validate(statisticSchema.monthYear, ValidationSource.QUERY),
  getNbStatClientResponsesByStatClient
);

router.get(
  "/kpi-response-stat",
  authentication,
  authorization(ModelCode.ANALYTICS, MethodCode.VIEW),
  validate(statisticSchema.kpi, ValidationSource.QUERY),
  getKpiResponseStat
);

router.get(
  "/kpi-response-stat-range-date",
  authentication,
  authorization(ModelCode.ANALYTICS, MethodCode.VIEW),
  validate(statisticSchema.kpiMonthYear, ValidationSource.QUERY),
  getKpiResponseStatByRangeDate
);

router.get(
  "/total-not-payed-codes",
  authentication,
  authorization(ModelCode.ANALYTICS, MethodCode.VIEW),
  totalNotPayedCodes
);

router.get(
  "/balance-per-office",
  authentication,
  authorization(ModelCode.ANALYTICS, MethodCode.VIEW),
  validate(statisticSchema.requiredDates, ValidationSource.QUERY),
  getBalanceAnalyticsPerOffice
);

export default router;
