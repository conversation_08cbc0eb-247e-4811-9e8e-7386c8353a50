import express from "express";
import {
  create,
  deleteOne,
  generateCashDeskPaymentPDF,
  getAll, getOne,
  update
} from "../../controllers/cashDeskPaymentController";
import { MethodCode, ModelCode } from "../../db/models/Permission";
import CashDeskPaymentRepo from "../../db/repositories/CashDeskPaymentRepo";
import exportData from "../../helpers/export/exportData";
import transformer from "../../helpers/export/transformer";
import { authentication } from "../../middlewares/auth/authentication";
import { authorization } from "../../middlewares/auth/authorization";
import { validate, ValidationSource } from "../../middlewares/joiValidation";
import cashDeskPaymentSchema from "./cashDeskPaymentSchema";
const router = express.Router();

// EXPORT
router.get(
  "/export",
  authentication,
  authorization(ModelCode.CASH_DESK_PAYMENT, MethodCode.LIST),
  validate(cashDeskPaymentSchema.filter, ValidationSource.QUERY),
  exportData(
    CashDeskPaymentRepo,
    "general-cash-register",
    transformer.cashDeskPayment
  )
);

router.post(
  "/financial-receipt-pdf",
  authentication,
  validate(cashDeskPaymentSchema.pdf),
  generateCashDeskPaymentPDF
);

router.post(
  "/",
  authentication,
  authorization(ModelCode.CASH_DESK_PAYMENT, MethodCode.CREATE),
  validate(cashDeskPaymentSchema.create),
  create
);

router.get(
  "/",
  authentication,
  authorization(ModelCode.CASH_DESK_PAYMENT, MethodCode.LIST),
  getAll
);

router.get(
  "/:cashDeskPaymentId",
  authentication,
  authorization(ModelCode.CASH_DESK_PAYMENT, MethodCode.VIEW),
  validate(cashDeskPaymentSchema.id, ValidationSource.PARAM),
  getOne
);

router.patch(
  "/:cashDeskPaymentId",
  authentication,
  authorization(ModelCode.CASH_DESK_PAYMENT, MethodCode.EDIT),
  validate(cashDeskPaymentSchema.id, ValidationSource.PARAM),
  validate(cashDeskPaymentSchema.update),
  update
);

router.delete(
  "/:cashDeskPaymentId",
  authentication,
  authorization(ModelCode.CASH_DESK_PAYMENT, MethodCode.DELETE),
  validate(cashDeskPaymentSchema.id, ValidationSource.PARAM),
  deleteOne
);

export default router;
