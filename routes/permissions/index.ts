import express from "express";
import { create, getAll } from "../../controllers/permissionController";
import { MethodCode, ModelCode } from "../../db/models/Permission";
import { authentication } from "../../middlewares/auth/authentication";
import { authorization } from "../../middlewares/auth/authorization";
const router = express.Router();

router.post("/", create);

router.get(
  "/",
  authentication,
  authorization(ModelCode.PERMISSION, MethodCode.LIST),
  getAll
);

export default router;
