import express from "express";
import {
  exportSessions,
  filterSessions,
  getAll,
} from "../../controllers/sessionController";
import { MethodCode, ModelCode } from "../../db/models/Permission";
import { authentication } from "../../middlewares/auth/authentication";
import { authorization } from "../../middlewares/auth/authorization";
import { validate, ValidationSource } from "../../middlewares/joiValidation";
import sessionSchema from "./sessionSchema";

const router = express.Router();

// EXPORT
router.get(
  "/export",
  authentication,
  authorization(ModelCode.SESSION, MethodCode.LIST),
  validate(sessionSchema.filter, ValidationSource.QUERY),
  exportSessions
);

router.get(
  "/",
  authentication,
  authorization(ModelCode.SESSION, MethodCode.LIST),
  validate(sessionSchema.all, ValidationSource.QUERY),
  getAll
);

router.get(
  "/filter",
  authentication,
  authorization(ModelCode.SESSION, MethodCode.LIST),
  validate(sessionSchema.filter, ValidationSource.QUERY),
  filterSessions
);

export default router;
