import Joi from "joi";
import { EventType } from "../../db/models/Session";
import { JoiObjectId } from "../../middlewares/joiValidation";

export default {
  all: Joi.object()
    .keys({
      limit: Joi.number().required(),
      ipAddress: Joi.string().optional(),
      eventType: Joi.valid(EventType.LOGIN, EventType.LOGOUT).optional(),
      page: Joi.number().required(),
      user: JoiObjectId(),
      startDate: Joi.date().iso().optional(),
      endDate: Joi.date().iso().min(Joi.ref("startDate")).optional(),
      withPagination: Joi.boolean().optional(),
      sort: Joi.string().optional(),
    })
    .when(
      Joi.object({
        withPagination: Joi.exist(),
      }).unknown(),
      {
        then: Joi.object({
          page: Joi.forbidden(),
          limit: Joi.forbidden(),
        }),
      }
    ),
  filter: Joi.object().keys({
    page: Joi.number().optional(),
    limit: Joi.number().optional(),
    ipAddress: Joi.string().optional(),
    eventType: Joi.valid(EventType.LOGIN, EventType.LOGOUT).optional(),
    user: JoiObjectId(),
    startDate: Joi.date().iso().optional(),
    endDate: Joi.date().iso().min(Joi.ref("startDate")).optional(),
    sort: Joi.string().optional(),
  }),
};
