import Jo<PERSON> from "joi";
import { JoiObjectId } from "../../middlewares/joiValidation";
export default {
  //Check route
  idParams: Joi.object().keys({
    dailyQuestionId: JoiObjectId().required(),
  }),

  //Create
  create: Joi.object().keys({
    name: Joi.string().required(),
    dailyKpis: Joi.array().items(JoiObjectId()),
  }),
  //update
  update: Joi.object().keys({
    name: Joi.string().optional(),
    dailyKpis: Joi.array().items(JoiObjectId()).optional(),
  }),
  //delete
  deleteMany: Joi.object().keys({
    dailyQuestionIds: Joi.array().items(JoiObjectId()).optional(),
  }),
};
