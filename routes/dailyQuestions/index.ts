import express from "express";
import {
  create,
  deleteMany,
  deleteOne,
  getAll,
  getOne,
  update,
} from "../../controllers/dailyQuestionController";
import { authentication } from "../../middlewares/auth/authentication";
import { validate, ValidationSource } from "../../middlewares/joiValidation";
import dailyQuestionSchema from "./dailyQuestionSchema";

const router = express.Router();

router.post(
  "/",
  authentication,
  // authorization(ModelCode.STAT_CLIENT, MethodCode.CREATE),
  validate(dailyQuestionSchema.create),
  create
);

router.get(
  "/",
  authentication,
  // authorization(ModelCode.STAT_CLIENT, MethodCode.LIST),
  getAll
);

router.get(
  "/:dailyQuestionId",
  authentication,
  // authorization(ModelCode.STAT_CLIENT, MethodCode.VIEW),
  validate(dailyQuestionSchema.idParams, ValidationSource.PARAM),
  getOne
);

router.patch(
  "/:dailyQuestionId",
  authentication,
  // authorization(ModelCode.STAT_CLIENT, MethodCode.VIEW),
  validate(dailyQuestionSchema.idParams, ValidationSource.PARAM),
  validate(dailyQuestionSchema.update),
  update
);

router.delete(
  "/delete",
  authentication,
  // authorization(ModelCode.STAT_CLIENT, MethodCode.VIEW),
  validate(dailyQuestionSchema.deleteMany),
  deleteMany
);

router.delete(
  "/:dailyQuestionId",
  authentication,
  // authorization(ModelCode.STAT_CLIENT, MethodCode.VIEW),
  validate(dailyQuestionSchema.idParams, ValidationSource.PARAM),
  deleteOne
);

export default router;
