import express from "express";
import {
  create,
  deleteOne,
  getAll,
  getOne,
  updateOne,
} from "../../controllers/bookController";
import { MethodCode, ModelCode } from "../../db/models/Permission";
import { authentication } from "../../middlewares/auth/authentication";
import { authorization } from "../../middlewares/auth/authorization";
import { validate, ValidationSource } from "../../middlewares/joiValidation";
import bookSchema from "./bookSchema";
import { uploadImageSingle } from "../../utils/uploadImage";

const router = express.Router();

router.post(
  "/",
  authentication,
  authorization(ModelCode.BOOK, MethodCode.CREATE),
  uploadImageSingle,
  validate(bookSchema.create),
  create
);

router.get(
  "/",
  authentication,
  authorization(ModelCode.BOOK, MethodCode.VIEW),
  getAll
);

router.get(
  "/:bookId",
  authentication,
  authorization(ModelCode.BOOK, MethodCode.VIEW),
  validate(bookSchema.getOne, ValidationSource.PARAM),
  getOne
);

router.patch(
  "/:bookId",
  authentication,
  authorization(ModelCode.BOOK, MethodCode.EDIT),
  validate(bookSchema.getOne, ValidationSource.PARAM),
  validate(bookSchema.update),
  updateOne
);

router.delete(
  "/:bookId",
  authentication,
  authorization(ModelCode.BOOK, MethodCode.DELETE),
  validate(bookSchema.getOne, ValidationSource.PARAM),
  deleteOne
);

export default router;
