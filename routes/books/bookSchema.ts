import Joi from "joi";
import { JoiObjectId } from "../../middlewares/joiValidation";

export default {
  create: Joi.object().keys({
    name: Joi.string().max(100).required(),
    description: Joi.string().max(500).required(),
    level: JoiObjectId().optional(),
    section: JoiObjectId().allow(null).required(),
    price: Joi.number().required(),
  }),
  update: Joi.object().keys({
    name: Joi.string().max(100).optional(),
    description: Joi.string().max(500).optional(),
    level: JoiObjectId().optional(),
    section: JoiObjectId().allow(null).optional(),
    price: Joi.number().optional(),
  }),
  getOne: Joi.object().keys({
    bookId: JoiObjectId().required(),
  }),
};
