import express from "express";
import {
  create,
  deleteMany,
  deleteOne,
  getAll,
  getByFilter,
  getOne,
  update,
} from "../../controllers/statClientResponseController";
import { MethodCode, ModelCode } from "../../db/models/Permission";
import StatClientResponseRepo from "../../db/repositories/StatClientResponseRepo";
import exportData from "../../helpers/export/exportData";
import transformer from "../../helpers/export/transformer";
import { authentication } from "../../middlewares/auth/authentication";
import { authorization } from "../../middlewares/auth/authorization";
import { validate, ValidationSource } from "../../middlewares/joiValidation";
import handleQuery from "../../middlewares/statClientResponse";
import statClientResponseSchema from "./statClientResponsesSchema";

const router = express.Router();

// EXPORT
router.get(
  "/export",
  authentication,
  authorization(ModelCode.STAT_CLIENT_RESPONSE, MethodCode.LIST),
  handleQuery,
  exportData(
    StatClientResponseRepo,
    "stat-client-answers",
    transformer.statClientResponse
  )
);

router.post(
  "/",
  authentication,
  authorization(ModelCode.STAT_CLIENT_RESPONSE, MethodCode.CREATE),
  validate(statClientResponseSchema.create),
  create
);

router.get(
  "/",
  authentication,
  authorization(ModelCode.STAT_CLIENT_RESPONSE, MethodCode.LIST),
  getAll
);

router.get(
  "/filter",
  authentication,
  authorization(ModelCode.STAT_CLIENT_RESPONSE, MethodCode.LIST),
  validate(statClientResponseSchema.filter, ValidationSource.QUERY),
  getByFilter
);

router.get(
  "/:statClientResponseId",
  authentication,
  authorization(ModelCode.STAT_CLIENT_RESPONSE, MethodCode.VIEW),
  validate(statClientResponseSchema.idParams, ValidationSource.PARAM),
  getOne
);

router.patch(
  "/:statClientResponseId",
  authentication,
  authorization(ModelCode.STAT_CLIENT_RESPONSE, MethodCode.EDIT),
  validate(statClientResponseSchema.idParams, ValidationSource.PARAM),
  validate(statClientResponseSchema.update),
  update
);

router.delete(
  "/delete",
  authentication,
  authorization(ModelCode.STAT_CLIENT_RESPONSE, MethodCode.DELETE),
  validate(statClientResponseSchema.deleteMany),
  deleteMany
);

router.delete(
  "/:statClientResponseId",
  authentication,
  authorization(ModelCode.STAT_CLIENT_RESPONSE, MethodCode.DELETE),
  validate(statClientResponseSchema.idParams, ValidationSource.PARAM),
  deleteOne
);

export default router;
