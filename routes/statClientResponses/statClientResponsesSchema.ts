import Jo<PERSON> from "joi";
import { BadRequestError } from "../../helpers/ApiError";
import { JoiObjectId } from "../../middlewares/joiValidation";

export default {
  //Check route
  idParams: Joi.object().keys({
    statClientResponseId: JoiObjectId().required(),
  }),

  //Create
  create: Joi.object().keys({
    clientName: Joi.string().required(),
    clientContact: Joi.alternatives()
      .try(Joi.string().email(), Joi.string().regex(/^\d{8}$/))
      .required(),
    statClient: JoiObjectId().required(),
    kpis: Joi.array().items(
      Joi.object()
        .keys({
          kpi: JoiObjectId().required(),
          response: Joi.array().items(Joi.any()).required(),
        })
        .required()
    ),
  }),

  //Update
  update: Joi.object().keys({
    clientName: Joi.string().optional(),
    clientContact: Joi.alternatives()
      .try(Joi.string().email(), Joi.string().regex(/^\d{8}$/))
      .optional(),
    statClient: JoiObjectId().optional(),
    kpis: Joi.array()
      .items(
        Joi.object().keys({
          kpi: JoiObjectId().required(),
          response: Joi.array().items(Joi.any()).required(),
        })
      )
      .optional(),
  }),

  //delete
  deleteMany: Joi.object().keys({
    statClientResponseIds: Joi.array().items(JoiObjectId()).optional(),
  }),

  filter: Joi.object().keys({
    clientContact: Joi.string().optional(),
    clientName: Joi.string().optional(),
    statClient: JoiObjectId().optional(),
    office: JoiObjectId().optional(),
    admin: Joi.array().items(JoiObjectId()).optional(),
    kpis: Joi.array()
      .items(
        Joi.object().keys({
          kpi: JoiObjectId().required(),
          response: Joi.array().items(Joi.any()).required(),
        })
      )
      .optional(),
    startDate: Joi.string()
      .regex(/^\d{4}\-(0?[1-9]|1[012])\-(0?[1-9]|[12][0-9]|3[01])$/)
      .messages({
        "string.pattern.base": `startDate must be a valid date at this format : YYYY-MM-DD`,
      })
      .optional(),
    endDate: Joi.string()
      .regex(/^\d{4}\-(0?[1-9]|1[012])\-(0?[1-9]|[12][0-9]|3[01])$/)
      .messages({
        "string.pattern.base": `endDate must be a valid date at this format : YYYY-MM-DD`,
      })
      .optional(),
    limit: Joi.number().optional(),
    page: Joi.number().optional(),
    sort: Joi.string().optional(),
  }),
};
