import express from "express";
import {
  createFolder,
  deleteFolder,
  getAllFolder,
  getOneFolder,
  moveFileFromFolder,
  removeFileFromFolder,
  updateFolder,
  uploadFileToFolder,
} from "../../controllers/folderController";
import { authentication } from "../../middlewares/auth/authentication";
import { checkFolderIdExist } from "../../middlewares/checkFolderIdExist";
import { validate, ValidationSource } from "../../middlewares/joiValidation";
import folderSchema from "./folderSchema";
import { createPathFolder } from "../../utils/FileUpload/createPathFolder";
import { MethodCode, ModelCode } from "../../db/models/Permission";
import { authorization } from "../../middlewares/auth/authorization";
import { uploadFiles } from "../../utils/uploadFile";

const router = express.Router();

router.use(authentication);

router.get("/", authorization(ModelCode.FOLDER, MethodCode.LIST), getAllFolder);

router.post(
  "/",
  authorization(ModelCode.FOLDER, MethodCode.CREATE),
  validate(folderSchema.create),
  createPathFolder,
  createFolder
);
router.post(
  "/file/move",
  validate(folderSchema.moveFile, ValidationSource.QUERY),
  moveFileFromFolder
);
router.get(
  "/:folderId",
  authorization(ModelCode.FOLDER, MethodCode.VIEW),
  validate(folderSchema.checkId, ValidationSource.PARAM),
  getOneFolder
);
router.delete(
  "/:folderId",
  authorization(ModelCode.FOLDER, MethodCode.DELETE),
  validate(folderSchema.checkId, ValidationSource.PARAM),
  deleteFolder
);
router.patch(
  "/:folderId",
  authorization(ModelCode.FOLDER, MethodCode.EDIT),
  validate(folderSchema.checkId, ValidationSource.PARAM),
  updateFolder
);
router.post(
  "/:folderId/upload",
  checkFolderIdExist,
  uploadFiles('files[]'),
  uploadFileToFolder
);
router.delete(
  "/:folderId?/file/:fileId/remove",
  validate(folderSchema.deleteFile, ValidationSource.PARAM),
  removeFileFromFolder
);

export default router;
