import Joi from "joi";
import { JoiObjectId } from "../../middlewares/joiValidation";

export default {
  checkId: Joi.object().keys({
    folderId: JoiObjectId().required(),
  }),
  create: Joi.object().keys({
    name: Joi.string().min(2).max(30).required(),
    parentFolder: JoiObjectId().allow(null),
    subFolder: Joi.array().items(JoiObjectId()),
    show: Joi.array().items(JoiObjectId()),
  }),
  deleteFile: Joi.object().keys({
    folderId: JoiObjectId().optional(),
    fileId: JoiObjectId().required(),
  }),
  moveFile: Joi.object().keys({
    to: JoiObjectId().required(),
    from: JoiObjectId().required(),
    fileId: JoiObjectId().required(),
  }),
};
