import express from "express";
import {
  create,
  deleteOne,
  getAll,
  getOne,
  reloadCodes,
  updateCodePaymentMethod,
  updateOne,
} from "../../controllers/codeController";
import { MethodCode, ModelCode } from "../../db/models/Permission";
import CodeRepo from "../../db/repositories/CodeRepo";
import exportData from "../../helpers/export/exportData";
import transformer from "../../helpers/export/transformer";
import { apiKeyAuth } from "../../middlewares/auth/apiKeyAuth";
import { authentication } from "../../middlewares/auth/authentication";
import { authorization } from "../../middlewares/auth/authorization";
import { validate, ValidationSource } from "../../middlewares/joiValidation";
import codeSchema from "./codeSchema";
const router = express.Router();

// EXPORT
router.get(
  "/export",
  authentication,
  authorization(ModelCode.CODE, MethodCode.LIST),
  validate(codeSchema.filter, ValidationSource.QUERY),
  exportData(CodeRepo, "codes", transformer.code)
);

router.post("/", apiKeyAuth, validate(codeSchema.create), create);
router.get(
  "/",
  authentication,
  authorization(ModelCode.CODE, MethodCode.LIST),
  validate(codeSchema.filter, ValidationSource.QUERY),
  getAll
);

router.patch(
  "/reload",
  authentication,
  authorization(ModelCode.CODE, MethodCode.LIST),
  validate(codeSchema.date, ValidationSource.QUERY),
  reloadCodes
);

router.patch("/payment-method", apiKeyAuth, updateCodePaymentMethod);

router.get(
  "/:codeId",
  authentication,
  authorization(ModelCode.CODE, MethodCode.VIEW),
  validate(codeSchema.id, ValidationSource.PARAM),
  getOne
);

router.patch(
  "/:codeId",
  authentication,
  authorization(ModelCode.CODE, MethodCode.EDIT),
  validate(codeSchema.id, ValidationSource.PARAM),
  updateOne
);

router.delete(
  "/:codeId",
  authentication,
  authorization(ModelCode.CODE, MethodCode.DELETE),
  validate(codeSchema.id, ValidationSource.PARAM),
  deleteOne
);
export default router;
