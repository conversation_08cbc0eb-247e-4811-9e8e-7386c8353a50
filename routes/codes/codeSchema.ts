import Jo<PERSON> from "joi";
import { JoiObjectId } from "../../middlewares/joiValidation";

export default {
  create: Joi.object().keys({
    paymentMethod: Joi.string().required(),
    amount: Joi.number().required(),
    code: Joi.string().required(),
    office: Joi.string().required(),
    officeAddress: Joi.string().required(),
    admin: Joi.string().required(),
    clientName: Joi.string().optional(),
    clientPhoneNumber: Joi.string().optional(),
    clientLevel: Joi.string().optional(),
    clientOffer: Joi.string().optional(),
    checkNumber: Joi.string().optional(),
    checkDate: JoiObjectId().optional(),
    notes: Joi.string().optional(),
    billOfExchangeNum: Joi.string().optional(),
    billOfExchangeName: Joi.string().optional(),
    billOfExchangePhone: Joi.string().optional(),
    billOfExchangeDueDate: Joi.string()
      .regex(/^\d{4}\-(0?[1-9]|1[012])\-(0?[1-9]|[12][0-9]|3[01])$/)
      .messages({
        "string.pattern.base": `startDate must be a valid date at this format : YYYY-MM-DD`,
      })
      .optional(),
    billOfExchangePayed: Joi.boolean().optional(),
  }),
  update: Joi.object().keys({
    paymentMethod: Joi.string().optional(),
    amount: Joi.number().optional(),
    code: Joi.string().optional(),
    office: JoiObjectId().optional(),
    officeAddress: Joi.string().optional(),
    admin: JoiObjectId().optional(),
    clientName: Joi.string().optional(),
    clientPhoneNumber: Joi.string().optional(),
    clientLevel: Joi.string().optional(),
    clientOffer: Joi.string().optional(),
    checkNumber: Joi.string().optional(),
    checkDate: JoiObjectId().optional(),
    notes: Joi.string().optional(),
    billOfExchangeNum: Joi.string().optional(),
    billOfExchangeName: Joi.string().optional(),
    billOfExchangePhone: Joi.string().optional(),
    billOfExchangeDueDate: Joi.string()
      .regex(/^\d{4}\-(0?[1-9]|1[012])\-(0?[1-9]|[12][0-9]|3[01])$/)
      .messages({
        "string.pattern.base": `startDate must be a valid date at this format : YYYY-MM-DD`,
      })
      .optional(),
    billOfExchangePayed: Joi.boolean().optional(),
  }),
  filter: Joi.object()
    .keys({
      paymentMethod: Joi.string().optional(),
      amount: Joi.number().optional(),
      code: Joi.string().optional(),
      office: JoiObjectId().optional(),
      admin: JoiObjectId().optional(),
      clientName: Joi.string().optional(),
      clientPhoneNumber: Joi.string().optional(),
      clientLevel: Joi.string().optional(),
      clientOffer: Joi.string().optional(),
      checkNumber: Joi.string().optional(),
      checkDate: JoiObjectId().optional(),
      startDate: Joi.string()
        .regex(/^\d{4}\-(0?[1-9]|1[012])\-(0?[1-9]|[12][0-9]|3[01])$/)
        .messages({
          "string.pattern.base": `startDate must be a valid date at this format : YYYY-MM-DD`,
        }),
      endDate: Joi.string()
        .regex(/^\d{4}\-(0?[1-9]|1[012])\-(0?[1-9]|[12][0-9]|3[01])$/)
        .messages({
          "string.pattern.base": `endDate must be a valid date at this format : YYYY-MM-DD`,
        }),
      limit: Joi.number().optional(),
      page: Joi.number().optional(),
      sort: Joi.string().optional(),
      search: Joi.string().optional(),
      checkPayable: Joi.boolean().optional(),
      checkPayed: Joi.boolean().optional(),
      isPayed: Joi.boolean().optional(),
    })
    .with("startDate", "endDate")
    .with("endDate", "startDate"),
  id: Joi.object().keys({
    codeId: JoiObjectId().required(),
  }),
  date: Joi.object().keys({
    date: Joi.string()
      .regex(/^\d{4}\-(0?[1-9]|1[012])\-(0?[1-9]|[12][0-9]|3[01])$/)
      .messages({
        "string.pattern.base": `date must be a valid date at this format : YYYY-MM-DD`,
      })
      .optional(),
  }),
};
