import express from "express";
import auth from "./auth/index";
import call from "./call";
import kpis from "./kpis";
import permissionGroups from "./permissionGroups/index";
import permissions from "./permissions/index";
import users from "./users/index";

import notes from "./notes";
import statClientResponses from "./statClientResponses";
import statClients from "./statClients";
import tags from "./tags";
import worktimes from "./worktimes";
import agentPerformance from "./agentPerformance";

import notifications from "./notifications";
import offices from "./offices";
import statistics from "./statistics";
import todos from "./todos";
import codes from "./codes";
import operations from "./operations";
import cashDeskPayments from "./cashDeskPayments";
import folder from "./folder";
import checkCalandars from "./checkCalandars";
import sessions from "./sessions";
import bonusAmouts from "./bonusAmount";
import pdfContent from "./pdfContent";
import paymentNotes from "./paymentNotes";
import sections from "./sections";
import levels from "./levels";
import books from "./books";
import bookStocks from "./bookStocks";
import bookSaleTransactions from "./bookSaleTransactions";
import departments from "./departments";
import departmentWorktimes from "./departmentWorktimes";
import booksAnalytics from "./booksAnalytics";
import bookCashDeskPayments from "./bookCashDeskPayments";
import dailyKpis from "./dailyKpis";
import dailyQuestions from "./dailyQuestions";
import dailyResponses from "./dailyResponses";

const router = express.Router();

router.use("/auth", auth);
router.use("/users", users);
router.use("/permissions", permissions);
router.use("/permission-groups", permissionGroups);
router.use("/kpis", kpis);
router.use("/tags", tags);
router.use("/stat-clients", statClients);
router.use("/stat-client-responses", statClientResponses);
router.use("/notes", notes);
router.use("/calls", call);
router.use("/todos", todos);
router.use("/worktimes", worktimes);
router.use("/stat-clients", statClients);
router.use("/stat-client-responses", statClientResponses);
router.use("/notifications", notifications);
router.use("/statistics", statistics);
router.use("/offices", offices);
router.use("/folder", folder);
router.use("/codes", codes);
router.use("/operations", operations);
router.use("/cash-desk-payments", cashDeskPayments);
router.use("/check-calandar", checkCalandars);
router.use("/sessions", sessions);
router.use("/agent-performances", agentPerformance);
router.use("/bonus-amounts", bonusAmouts);
router.use("/pdf-content", pdfContent);
router.use("/payment-notes", paymentNotes);
router.use("/sections", sections);
router.use("/levels", levels);
router.use("/books", books);
router.use("/book-stocks", bookStocks);
router.use("/book-sale-transactions", bookSaleTransactions);
router.use("/departments", departments);
router.use("/department-worktimes", departmentWorktimes);
router.use("/books-analytics", booksAnalytics);
router.use("/books-cash-desk-payments", bookCashDeskPayments);
router.use("/daily-kpis", dailyKpis);
router.use("/daily-questions", dailyQuestions);
router.use("/daily-responses", dailyResponses);

export default router;
