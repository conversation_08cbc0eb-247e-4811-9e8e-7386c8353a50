import express from "express";
import {
  createUser,
  deleteOne,
  getAll,
  getOne,
  update,
} from "../../controllers/userController";
import { MethodCode, ModelCode } from "../../db/models/Permission";
import UserRepo from "../../db/repositories/UserRepo";
import exportData from "../../helpers/export/exportData";
import transformer from "../../helpers/export/transformer";
import { authentication } from "../../middlewares/auth/authentication";
import { authorization } from "../../middlewares/auth/authorization";
import { validate, ValidationSource } from "../../middlewares/joiValidation";
import userSchema from "./userSchema";
const router = express.Router();

// EXPORT
router.get(
  "/export",
  authentication,
  authorization(ModelCode.USER, MethodCode.LIST),
  validate(userSchema.filter, ValidationSource.QUERY),
  exportData(UserRepo, "users", transformer.user)
);

router.post(
  "/",
  authentication,
  authorization(ModelCode.USER, MethodCode.CREATE),
  validate(userSchema.create),
  createUser
);
router.get(
  "/",
  authentication,
  authorization(ModelCode.USER, MethodCode.LIST),
  getAll
);

router.get(
  "/:userId",
  authentication,
  authorization(ModelCode.USER, MethodCode.LIST),
  validate(userSchema.checkId, ValidationSource.PARAM),
  getOne
);

router.delete(
  "/:userId",
  authentication,
  authorization(ModelCode.USER, MethodCode.DELETE),
  deleteOne
);

router.patch(
  "/:userId",
  authentication,
  authorization(ModelCode.USER, MethodCode.EDIT),
  validate(userSchema.checkId, ValidationSource.PARAM),
  validate(userSchema.update),
  update
);

export default router;
