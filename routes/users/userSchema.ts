import Joi from "joi";
import { RoleCode } from "../../db/models/User";
import { JoiObjectId } from "../../middlewares/joiValidation";

export default {
  create: Joi.object().keys({
    firstName: Joi.string().required().min(2).max(20),
    lastName: Joi.string().required().min(2).max(20),
    email: Joi.string().email().required(),
    phone: Joi.string().required().length(8),
    password: Joi.string()
      .regex(
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[#$@!%&*?])[A-Za-z\d#$@!%&*?]{8,30}$/
      )
      .messages({
        "string.pattern.base": `New Password must be :
      Minimum eight characters, at least one uppercase letter, one lowercase letter, one number and one special character`,
      })
      .required(),
    department: JoiObjectId().optional(),
    extraPermissions: Joi.array().items(JoiObjectId()).optional(),
    permissionGroup: Joi.array().items(JoiObjectId()).optional(),
    firstDayWork: Joi.string()
      .regex(/^\d{4}\-(0?[1-9]|1[012])\-(0?[1-9]|[12][0-9]|3[01])$/)
      .allow(null)
      .messages({
        "string.pattern.base": `firstDayWork must be a valid date at this format : YYYY-MM-DD`,
      })
      .optional(),
  }),
  checkId: Joi.object().keys({
    userId: JoiObjectId().required(),
  }),
  update: Joi.object().keys({
    office: JoiObjectId().allow(null).optional(),
    deviceId: Joi.string().allow(null).optional(),
    singleDevice: Joi.boolean().optional(),
    showAllCodes: Joi.boolean().optional(),
    role: Joi.string().valid(RoleCode.SUPER_ADMIN, RoleCode.ADMIN).optional(),
    extraPermissions: Joi.array().items(JoiObjectId()).optional(),
    permissionGroup: Joi.array().items(JoiObjectId()).optional(),
    department: JoiObjectId().optional(),
    authorizedUntil: Joi.string()
      .allow(null)
      .regex(/^\d{4}\-(0?[1-9]|1[012])\-(0?[1-9]|[12][0-9]|3[01])$/)
      .messages({
        "string.pattern.base": `authorizedUntil must be a valid date at this format : YYYY-MM-DD`,
      })
      .optional(),
    isAuthorized: Joi.boolean().optional(),
    hasAccess: Joi.boolean().optional(),
    phone: Joi.string()
      .allow(null)
      .pattern(
        /^([+]?[(]?[0-9]{3}[)]?[-\s.]?[0-9]{3}[-\s.]?[0-9]{4,6}|[0-9]{8})$/
      )
      .optional(),
    firstDayWork: Joi.string()
      .regex(/^\d{4}\-(0?[1-9]|1[012])\-(0?[1-9]|[12][0-9]|3[01])$/)
      .allow(null)
      .messages({
        "string.pattern.base": `firstDayOfWork must be a valid date at this format : YYYY-MM-DD`,
      })
      .optional(),
  }),
  filter: Joi.object().keys({
    username: Joi.string().min(3).max(30).optional(),
    name: Joi.string().optional(),
    email: Joi.string().optional(),
    office: JoiObjectId().optional(),
    role: Joi.string().valid(RoleCode.SUPER_ADMIN, RoleCode.ADMIN).optional(),
    deviceId: Joi.string().optional(),
    singleDevice: Joi.string().optional(),
    startDate: Joi.string()
      .regex(/^\d{4}\-(0?[1-9]|1[012])\-(0?[1-9]|[12][0-9]|3[01])$/)
      .messages({
        "string.pattern.base": `startDate must be a valid date at this format : YYYY-MM-DD`,
      })
      .optional(),
    endDate: Joi.string()
      .regex(/^\d{4}\-(0?[1-9]|1[012])\-(0?[1-9]|[12][0-9]|3[01])$/)
      .messages({
        "string.pattern.base": `endDate must be a valid date at this format : YYYY-MM-DD`,
      })
      .optional(),
    search: Joi.string().optional(),
    sort: Joi.string().optional(),
  }),
};
