import express from "express";
import {
  create,
  deleteMany,
  deleteOne,
  getAll,
  getOne,
  updateKpi
} from "../../controllers/kpiController";
import { MethodCode, ModelCode } from "../../db/models/Permission";
import KpiRepo from "../../db/repositories/KpiRepo";
import exportData from "../../helpers/export/exportData";
import transformer from "../../helpers/export/transformer";
import { authentication } from "../../middlewares/auth/authentication";
import { authorization } from "../../middlewares/auth/authorization";
import { validate, ValidationSource } from "../../middlewares/joiValidation";
import kpiSchema from "./kpiSchema";

const router = express.Router();

// EXPORT
router.get(
  "/export",
  authentication,
  authorization(ModelCode.KPI, MethodCode.LIST),
  validate(kpiSchema.filter, ValidationSource.QUERY),
  exportData(KpiRepo, "kpis", transformer.kpi)
);

//POST
router.post(
  "/",
  authentication,
  authorization(ModelCode.KPI, MethodCode.CREATE),
  validate(kpiSchema.create),
  create
);
// GET ALL
router.get(
  "/",
  authentication,
  authorization(ModelCode.KPI, MethodCode.LIST),
  getAll
);

// GET ONE
router.get(
  "/:kpiId",
  authentication,
  authorization(ModelCode.KPI, MethodCode.VIEW),
  validate(kpiSchema.getOne, ValidationSource.PARAM),
  getOne
);

// UPDATE ONE
router.patch(
  "/:kpiId",
  authentication,
  authorization(ModelCode.KPI, MethodCode.EDIT),
  validate(kpiSchema.getOne, ValidationSource.PARAM),
  validate(kpiSchema.update),
  updateKpi
);

// DELETE ONE
router.delete(
  "/:kpiId",
  authentication,
  authorization(ModelCode.KPI, MethodCode.DELETE),
  validate(kpiSchema.getOne, ValidationSource.PARAM),
  deleteOne
);

// DELETE MANY
router.delete(
  "/",
  authentication,
  authorization(ModelCode.KPI, MethodCode.DELETE),
  validate(kpiSchema.delete, ValidationSource.BODY),
  deleteMany
);

export default router;
