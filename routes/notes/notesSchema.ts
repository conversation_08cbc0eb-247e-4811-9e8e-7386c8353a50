import Jo<PERSON> from "joi";
import { JoiObjectId } from "../../middlewares/joiValidation";

export default {
  create: Joi.object().keys({
    title: Joi.string().required(),
    content: Joi.string().required(),
    mentions: Joi.array().items(JoiObjectId()).optional(),
    tags: Joi.array().items(JoiObjectId()).optional(),
    files: Joi.array().optional(),
  }),
  update: Joi.object().keys({
    title: Joi.string().optional(),
    content: Joi.string().optional(),
    mentions: Joi.array().items(JoiObjectId()).optional(),
    tags: Joi.array().items(JoiObjectId()).optional(),
    files: Joi.array().optional()
  }),
  deleteMany: Joi.object().keys({
    noteIds: Joi.array().items(JoiObjectId()).required(),
  }),
};
