import express from "express";
import {
  createNote,
  deleteNote,
  deleteNotes,
  getAllNotes,
  getNote,
  updateNote,
} from "../../controllers/noteController";
import { MethodCode, ModelCode } from "../../db/models/Permission";
import { authentication } from "../../middlewares/auth/authentication";
import { authorization } from "../../middlewares/auth/authorization";
import { validate } from "../../middlewares/joiValidation";
import { uploadFiles } from "../../utils/uploadFile";
import noteSchema from "./notesSchema";

const router = express.Router();

router.post(
  "/",
  authentication,
  authorization(ModelCode.NOTE, MethodCode.CREATE),
  uploadFiles("fileUpload[]"),
  validate(noteSchema.create),
  createNote
);
router.get(
  "/",
  authentication,
  authorization(ModelCode.NOTE, MethodCode.LIST),
  getAllNotes
);

router.delete(
  "/many",
  authentication,
  authorization(ModelCode.NOTE, MethodCode.DELETE),
  validate(noteSchema.deleteMany),
  deleteNotes
);

router.get(
  "/:noteId",
  authentication,
  authorization(ModelCode.NOTE, MethodCode.VIEW),
  getNote
);

router.patch(
  "/:noteId",
  authentication,
  uploadFiles("fileUpload[]"),
  authorization(ModelCode.NOTE, MethodCode.EDIT),
  validate(noteSchema.update),
  updateNote
);

router.delete(
  "/:noteId",
  authentication,
  authorization(ModelCode.NOTE, MethodCode.DELETE),
  deleteNote
);

export default router;
