import Joi from "joi";
import { OperationType } from "../../db/models/Operation";
import { JoiObjectId } from "../../middlewares/joiValidation";

export default {
  create: Joi.object().keys({
    type: Joi.string()
      .valid(OperationType.CREDIT, OperationType.DEPOSIT)
      .required(),
    amount: Joi.number().greater(0).required(),
    notes: Joi.string().required(),
    office: JoiObjectId().optional(),
    admin: JoiObjectId().optional(),
  }),
  update: Joi.object().keys({
    type: Joi.string()
      .valid(OperationType.CREDIT, OperationType.DEPOSIT)
      .optional(),
    amount: Joi.number().greater(0).optional(),
    notes: Joi.string().optional(),
    office: JoiObjectId().optional(),
    admin: JoiObjectId().optional(),
  }),
  id: Joi.object().keys({
    operationId: JoiObjectId().required(),
  }),
  filter: Joi.object().keys({
    type: Joi.string()
      .valid(OperationType.CREDIT, OperationType.DEPOSIT)
      .optional(),
    notes: Joi.string().optional(),
    amount: Joi.number().optional(),
    office: JoiObjectId().optional(),
    admin: JoiObjectId().optional(),
    search: Joi.string().optional(),
    sort: Joi.string().optional(),
    startDate: Joi.string()
      .regex(/^\d{4}\-(0?[1-9]|1[012])\-(0?[1-9]|[12][0-9]|3[01])$/)
      .messages({
        "string.pattern.base": `startDate must be a valid date at this format : YYYY-MM-DD`,
      })
      .optional(),
    endDate: Joi.string()
      .regex(/^\d{4}\-(0?[1-9]|1[012])\-(0?[1-9]|[12][0-9]|3[01])$/)
      .messages({
        "string.pattern.base": `endDate must be a valid date at this format : YYYY-MM-DD`,
      })
      .optional(),
  }),
};
