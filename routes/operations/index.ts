import express from "express";
import {
  create,
  deleteOne,
  getAll,
  getOne,
  updateOne,
} from "../../controllers/operationController";
import { MethodCode, ModelCode } from "../../db/models/Permission";
import OperationRepo from "../../db/repositories/OperationRepo";
import exportData from "../../helpers/export/exportData";
import transformer from "../../helpers/export/transformer";
import { authentication } from "../../middlewares/auth/authentication";
import { authorization } from "../../middlewares/auth/authorization";
import { validate, ValidationSource } from "../../middlewares/joiValidation";
import operationSchema from "./operationSchema";
const router = express.Router();

router.get(
  "/export",
  authentication,
  authorization(ModelCode.USER, MethodCode.LIST),
  validate(operationSchema.filter, ValidationSource.QUERY),
  exportData(OperationRepo, "expenses", transformer.operation)
);

router.post(
  "/",
  authentication,
  authorization(ModelCode.OPERATION, MethodCode.CREATE),
  validate(operationSchema.create),
  create
);

router.get(
  "/",
  authentication,
  authorization(ModelCode.OPERATION, MethodCode.LIST),
  getAll
);
router.get(
  "/:operationId",
  authentication,
  authorization(ModelCode.OPERATION, MethodCode.VIEW),
  validate(operationSchema.id, ValidationSource.PARAM),
  getOne
);

router.patch(
  "/:operationId",
  authentication,
  authorization(ModelCode.OPERATION, MethodCode.EDIT),
  validate(operationSchema.id, ValidationSource.PARAM),
  validate(operationSchema.update),
  updateOne
);

router.delete(
  "/:operationId",
  authentication,
  authorization(ModelCode.OPERATION, MethodCode.DELETE),
  validate(operationSchema.id, ValidationSource.PARAM),
  deleteOne
);

export default router;
