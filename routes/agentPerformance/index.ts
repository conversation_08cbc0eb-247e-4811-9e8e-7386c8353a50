import express from "express";
import {
  createAgentPerformance, deleteAgentPerformance, exportAgentPerfomances, getAgentPerfomanceByUser, getAllAgentPerfomancePerMonth, reloadAllAgentPerformances, updateAgentPerformance
} from "../../controllers/agentPerformance";
import { MethodCode, ModelCode } from "../../db/models/Permission";
import { authentication } from "../../middlewares/auth/authentication";
import { authorization } from "../../middlewares/auth/authorization";
import { validate, ValidationSource } from "../../middlewares/joiValidation";
import agentPerformanceSchema from "./agentPerformanceSchema";

const router = express.Router();

router.get(
  "/",
  authentication,
  authorization(ModelCode.AGENT_PERFORMANCE, MethodCode.LIST),
  validate(agentPerformanceSchema.getAll, ValidationSource.QUERY),
  getAllAgentPerfomancePerMonth
);

// EXPORT
router.get(
  "/export",
  authentication,
  authorization(ModelCode.AGENT_PERFORMANCE, MethodCode.LIST),
  exportAgentPerfomances
);

router.get(
  "/user",
  authentication,
  authorization(ModelCode.AGENT_PERFORMANCE, MethodCode.VIEW),
  validate(agentPerformanceSchema.getByUser, ValidationSource.QUERY),
  getAgentPerfomanceByUser
);

router.post(
  "/",
  authentication,
  authorization(ModelCode.AGENT_PERFORMANCE, MethodCode.CREATE),
  validate(agentPerformanceSchema.create),
  createAgentPerformance
);

router.patch(
  "/reload",
  authentication,
  authorization(ModelCode.AGENT_PERFORMANCE, MethodCode.EDIT),
  validate(agentPerformanceSchema.getAll, ValidationSource.QUERY),
  reloadAllAgentPerformances
);

router.patch(
  "/:agentPerformanceId",
  authentication,
  authorization(ModelCode.AGENT_PERFORMANCE, MethodCode.EDIT),
  validate(agentPerformanceSchema.getOne, ValidationSource.PARAM),
  validate(agentPerformanceSchema.update),
  updateAgentPerformance
);

router.delete(
  "/:agentPerformanceId",
  authentication,
  authorization(ModelCode.AGENT_PERFORMANCE, MethodCode.DELETE),
  validate(agentPerformanceSchema.getOne, ValidationSource.PARAM),
  deleteAgentPerformance
);

export default router;
