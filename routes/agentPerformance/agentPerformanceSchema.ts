import Joi from "joi";
import { customJ<PERSON>, JoiObjectId } from "../../middlewares/joiValidation";

export default {
  getAll: Joi.object().keys({
    month: Joi.number().min(0).max(11).optional(),
    year: Joi.number().min(2023).required(),
  }),

  getByUser: Joi.object().keys({
    year: Joi.number().min(2023).required(),
    user: JoiObjectId().optional(),
  }),

  create: Joi.object().keys({
    user: JoiObjectId().required(),
    date: customJoi.startOfMonth().iso().required(),
    eliminationFault: Joi.boolean().required(),
    tam: Joi.boolean().required(),
    notes: Joi.string().allow(null).optional(),
    bonusAmountPct: Joi.number().min(0).max(100).required(),
  }),

  update: Joi.object().keys({
    eliminationFault: Joi.boolean().optional(),
    tam: Joi.boolean().optional(),
    notes: Joi.string().allow(null).optional(),
    bonusAmountPct: Joi.number().min(0).max(100).optional(),
  }),
  getOne: Joi.object().keys({
    agentPerformanceId: JoiObjectId().required(),
  }),
};
