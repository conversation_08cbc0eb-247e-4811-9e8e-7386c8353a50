import Jo<PERSON> from "joi";
import { JoiObjectId } from "../../middlewares/joiValidation";

export default {
  login: Joi.object().keys({
    email: Joi.string().email().required(),
    password: Joi.string().required(),
  }),
  loginTakiacademy: Joi.object().keys({
    credential: Joi.string().required(),
    password: Joi.string().required(),
    ipAddress: Joi.string().allow(null).ip().required(),
  }),
  logout: Joi.object().keys({
    userId: JoiObjectId().required(),
  }),
};
