import express from "express";
import {
  getMe,
  logout,
  logout<PERSON>d<PERSON>,
  OAuthTakiacademy,
} from "../../controllers/authController";
import { authentication } from "../../middlewares/auth/authentication";

import { validate, ValidationSource } from "../../middlewares/joiValidation";
import authSchema from "./authSchema";

const router = express.Router();

router.post(
  "/login-takiacademy",
  validate(authSchema.loginTakiacademy),
  OAuthTakiacademy
);
router.get("/me", authentication, getMe);

router.delete("/logout", authentication, logout);

router.delete(
  "/logout/:userId",
  authentication,
  validate(authSchema.logout, ValidationSource.PARAM),
  logoutAdmin
);

export default router;
