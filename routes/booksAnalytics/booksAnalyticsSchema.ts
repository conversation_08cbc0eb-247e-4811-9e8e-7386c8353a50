import Joi from "joi";
import { JoiObjectId } from "../../middlewares/joiValidation";

export default {
  revenuePerOffice: Joi.object()
    .keys({
      office: JoiObjectId().optional(),
      startDate: Joi.string()
        .regex(/^\d{4}\-(0?[1-9]|1[012])\-(0?[1-9]|[12][0-9]|3[01])$/)
        .messages({
          "string.pattern.base": `startDate must be a valid date at this format : YYYY-MM-DD`,
        })
        .optional(),
      endDate: Joi.string()
        .regex(/^\d{4}\-(0?[1-9]|1[012])\-(0?[1-9]|[12][0-9]|3[01])$/)
        .messages({
          "string.pattern.base": `endDate must be a valid date at this format : YYYY-MM-DD`,
        })
        .optional(),
    })
    .with("startDate", "endDate")
    .with("endDate", "startDate")
    .custom((value, helpers) => {
      const { startDate, endDate } = value;

      if (startDate && endDate) {
        const start = new Date(startDate);
        const end = new Date(endDate);

        if (end < start) {
          return helpers.message({
            custom: "endDate must be greater than or equal to startDate",
          });
        }
      }

      return value;
    }),

  general: Joi.object()
    .keys({
      startDate: Joi.string()
        .regex(/^\d{4}\-(0?[1-9]|1[012])\-(0?[1-9]|[12][0-9]|3[01])$/)
        .messages({
          "string.pattern.base": `startDate must be a valid date at this format : YYYY-MM-DD`,
        })
        .optional(),
      endDate: Joi.string()
        .regex(/^\d{4}\-(0?[1-9]|1[012])\-(0?[1-9]|[12][0-9]|3[01])$/)
        .messages({
          "string.pattern.base": `endDate must be a valid date at this format : YYYY-MM-DD`,
        })
        .optional(),
    })
    .with("startDate", "endDate")
    .with("endDate", "startDate")
    .custom((value, helpers) => {
      const { startDate, endDate } = value;

      if (startDate && endDate) {
        const start = new Date(startDate);
        const end = new Date(endDate);

        if (end < start) {
          return helpers.message({
            custom: "endDate must be greater than or equal to startDate",
          });
        }
      }

      return value;
    }),
};
