import express from "express";
import {
  getAllBooksStockNumber,
  getBooksCashDeskPayments,
  getBooksStockNumberPerOfficeAndBook,
  getBooksStockPerBook,
  getGeneralBooksAnalytics,
  getRevenuePerOffice,
  getTotalRevenue,
} from "../../controllers/booksAnalyticsController";
import { MethodCode, ModelCode } from "../../db/models/Permission";
import { authentication } from "../../middlewares/auth/authentication";
import { authorization } from "../../middlewares/auth/authorization";
import { validate, ValidationSource } from "../../middlewares/joiValidation";
import booksAnalyticsSchema from "./booksAnalyticsSchema";

const router = express.Router();

router.get(
  "/stock",
  authentication,
  authorization(ModelCode.BOOKS_ANALYTICS, MethodCode.VIEW),
  getAllBooksStockNumber
);

router.get(
  "/stock-per-book",
  authentication,
  authorization(ModelCode.BOOKS_ANALYTICS, MethodCode.VIEW),
  getBooksStockPerBook
);

router.get(
  "/revenue-per-office",
  authentication,
  authorization(ModelCode.BOOKS_ANALYTICS, MethodCode.VIEW),
  validate(booksAnalyticsSchema.revenuePerOffice, ValidationSource.QUERY),
  getRevenuePerOffice
);

router.get(
  "/general",
  authentication,
  authorization(ModelCode.BOOKS_ANALYTICS, MethodCode.VIEW),
  validate(booksAnalyticsSchema.general, ValidationSource.QUERY),
  getGeneralBooksAnalytics
);

router.get(
  "/cash-desk-payments",
  authentication,
  authorization(ModelCode.BOOKS_ANALYTICS, MethodCode.VIEW),
  validate(booksAnalyticsSchema.revenuePerOffice, ValidationSource.QUERY),
  getBooksCashDeskPayments
);

router.get(
  "/revenue",
  authentication,
  authorization(ModelCode.BOOKS_ANALYTICS, MethodCode.VIEW),
  validate(booksAnalyticsSchema.revenuePerOffice, ValidationSource.QUERY),
  getTotalRevenue
);

router.get(
  "/stock-per-office-per-book",
  authentication,
  authorization(ModelCode.BOOKS_ANALYTICS, MethodCode.VIEW),
  getBooksStockNumberPerOfficeAndBook
);

export default router;
