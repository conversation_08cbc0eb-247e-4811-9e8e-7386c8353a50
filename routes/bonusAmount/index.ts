import express from "express";
import {
  createBonusAmount,
  getAll,
  getOne,
  updateOne,
  deleteOne,
  deleteMany,
} from "../../controllers/bonusAmount";
import { MethodCode, ModelCode } from "../../db/models/Permission";
import { authentication } from "../../middlewares/auth/authentication";
import { authorization } from "../../middlewares/auth/authorization";
import { validate, ValidationSource } from "../../middlewares/joiValidation";
import bonusAmountSchema from "./bonusAgentSchema";

const router = express.Router();

router.post(
  "/",
  authentication,
  authorization(ModelCode.BONUS_AMOUNT, MethodCode.CREATE),
  validate(bonusAmountSchema.create),
  createBonusAmount
);

router.get(
  "/",
  authentication,
  authorization(ModelCode.BONUS_AMOUNT, MethodCode.LIST),
  validate(bonusAmountSchema.filter, ValidationSource.QUERY),
  getAll
);

router.get(
  "/:bonusAmountId",
  authentication,
  authorization(ModelCode.BONUS_AMOUNT, MethodCode.VIEW),
  validate(bonusAmountSchema.getOne, ValidationSource.PARAM),
  getOne
);

router.patch(
  "/:bonusAmountId",
  authentication,
  authorization(ModelCode.BONUS_AMOUNT, MethodCode.EDIT),
  validate(bonusAmountSchema.getOne, ValidationSource.PARAM),
  validate(bonusAmountSchema.update),
  updateOne
);
router.delete(
  "/",
  authentication,
  authorization(ModelCode.BONUS_AMOUNT, MethodCode.DELETE),
  validate(bonusAmountSchema.deleteMany),
  deleteMany
);

router.delete(
  "/:bonusAmountId",
  authentication,
  authorization(ModelCode.BONUS_AMOUNT, MethodCode.DELETE),
  validate(bonusAmountSchema.getOne, ValidationSource.PARAM),
  deleteOne
);

export default router;
