import Joi from "joi";
import {
  JoiObjectId,
  validateDateRange,
} from "../../middlewares/joiValidation";

export default {
  create: Joi.object().keys({
    ranges: Joi.array().items(
      Joi.object().keys({
        amount: Joi.number().greater(0).required(),
        minSeniority: Joi.number().required(),
        maxSeniority: Joi.number().required(),
      })
    ),
    startDate: Joi.string()
      .regex(/^\d{4}\-(0?[1-9]|1[012])\-(0?[1-9]|[12][0-9]|3[01])$/)
      .messages({
        "string.pattern.base": `startDate must be a valid date at this format : YYYY-MM-DD`,
      })
      .required(),
    endDate: Joi.string()
      .regex(/^\d{4}\-(0?[1-9]|1[012])\-(0?[1-9]|[12][0-9]|3[01])$/)
      .messages({
        "string.pattern.base": `endDate must be a valid date at this format : YYYY-MM-DD`,
      })
      .required()
      .custom(validateDateRange),
  }),

  update: Joi.object().keys({
    ranges: Joi.array().items(
      Joi.object().keys({
        amount: Joi.number().greater(0).required(),
        minSeniority: Joi.number().required(),
        maxSeniority: Joi.number().required(),
      })
    ),
    startDate: Joi.string()
      .regex(/^\d{4}\-(0?[1-9]|1[012])\-(0?[1-9]|[12][0-9]|3[01])$/)
      .messages({
        "string.pattern.base": `startDate must be a valid date at this format : YYYY-MM-DD`,
      })
      .required(),
    endDate: Joi.string()
      .regex(/^\d{4}\-(0?[1-9]|1[012])\-(0?[1-9]|[12][0-9]|3[01])$/)
      .messages({
        "string.pattern.base": `endDate must be a valid date at this format : YYYY-MM-DD`,
      })
      .required()
      .custom(validateDateRange),
  }),
  filter: Joi.object().keys({
    amount: Joi.number().greater(0).optional(),
    startDate: Joi.object()
      .pattern(
        Joi.string().valid("gte", "lte", "gt", "lt"),
        Joi.string()
          .regex(/^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])$/)
          .messages({
            "string.pattern.base":
              "Date must be a valid date in the format: YYYY-MM-DD",
          })
      )
      .optional(),
    endDate: Joi.object()
      .pattern(
        Joi.string().valid("gte", "lte", "gt", "lt"),
        Joi.string()
          .regex(/^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])$/)
          .messages({
            "string.pattern.base":
              "Date must be a valid date in the format: YYYY-MM-DD",
          })
      )
      .custom(validateDateRange)
      .optional(),
    limit: Joi.number().optional(),
    page: Joi.number().optional(),
    sort: Joi.string().optional(),
  }),
  getOne: Joi.object().keys({
    bonusAmountId: JoiObjectId().required(),
  }),
  deleteMany: Joi.object().keys({
    bonusAmountIds: Joi.array().items(JoiObjectId()),
  }),
};
