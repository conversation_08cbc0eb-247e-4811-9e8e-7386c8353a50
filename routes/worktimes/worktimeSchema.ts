import Joi from "joi";
import { WorktimeType } from "../../db/models/Worktime";
import { JoiObjectId } from "../../middlewares/joiValidation";
import worktimeService from "../../services/worktimeService";

export default {
  createMyWorktimes: Joi.object().keys({
    data: Joi.array().items({
      title: Joi.string().min(3).max(100).optional(),
      startDate: Joi.date().iso().required().messages({
        "date.min": "'startDate' must be greater or equal than today's date",
      }),
      endDate: Joi.date()
        .iso()
        .min(Joi.ref("startDate"))
        .required()
        .custom(worktimeService.validateEndDate)
        .error((errors) => {
          errors.forEach((err) => {
            if (err.code === "any.invalid") {
              err.message =
                '"endDate" must be one hour or less greater than "startDate"';
            }
          });
          return errors;
        }),

      type: Joi.string()
        .valid(...Object.values(WorktimeType))
        .optional(),
    }),
  }),

  createEmployeeWorktimes: Joi.object().keys({
    data: Joi.array().items({
      title: Joi.string().min(3).max(100).optional(),
      startDate: Joi.date().iso().required(),
      endDate: Joi.date()
        .iso()
        .min(Joi.ref("startDate"))
        .required()
        .custom(worktimeService.validateEndDate)
        .error((errors) => {
          errors.forEach((err) => {
            if (err.code === "any.invalid") {
              err.message =
                '"endDate" must be one hour or less greater than "startDate"';
            }
          });
          return errors;
        }),
      type: Joi.string()
        .valid(...Object.values(WorktimeType))
        .optional(),
    }),
  }),

  getOne: Joi.object().keys({
    userId: JoiObjectId().required(),
  }),

  updateMyworktime: Joi.object().keys({
    title: Joi.string().min(3).max(100).optional(),
    startDate: Joi.date()
      .min(new Date().toISOString().substr(0, 10))
      .iso()
      .optional()
      .messages({
        "date.min": "'startDate' must be greater or equal than today's date",
      }),
    endDate: Joi.date()
      .iso()
      .min(Joi.ref("startDate"))
      .required()
      .custom(worktimeService.validateEndDate)
      .error((errors) => {
        errors.forEach((err) => {
          if (err.code === "any.invalid") {
            err.message =
              '"endDate" must be one hour or less greater than "startDate"';
          }
        });
        return errors;
      }),
    type: Joi.string()
      .valid(...Object.values(WorktimeType))
      .optional(),
  }),

  updateEmployeeWorktime: Joi.object().keys({
    title: Joi.string().min(3).max(100).optional(),
    startDate: Joi.date().iso().optional().messages({
      "date.min": "'startDate' must be greater or equal than today's date",
    }),
    endDate: Joi.date()
      .iso()
      .min(Joi.ref("startDate"))
      .required()
      .custom(worktimeService.validateEndDate)
      .error((errors) => {
        errors.forEach((err) => {
          if (err.code === "any.invalid") {
            err.message =
              '"endDate" must be one hour or less greater than "startDate"';
          }
        });
        return errors;
      }),
    type: Joi.string()
      .valid(...Object.values(WorktimeType))
      .optional(),
  }),

  worktimeParam: Joi.object().keys({
    worktimeId: JoiObjectId().required(),
  }),

  worktimeEmployeeParam: Joi.object().keys({
    userId: JoiObjectId().required(),
    worktimeId: JoiObjectId().required(),
  }),

  delete: Joi.object().keys({
    data: Joi.array().min(1).items(JoiObjectId()).required(),
  }),

  deleteParam: Joi.object().keys({
    userId: JoiObjectId().required(),
  }),
};
