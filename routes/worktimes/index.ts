import express from 'express'
import {
  createEmployeeWorktimes,
  createMy<PERSON>orktimes,
  deleteEmployeeWorktime,
  deleteMyWorktime,
  getEmployeeWorktimes,
  getMyWorktimes,
  updateEmployeeWorktime,
  updateMyWorktime,
} from '../../controllers/worktimeController'
import { MethodCode, ModelCode } from '../../db/models/Permission'
import { authentication } from '../../middlewares/auth/authentication'
import { authorization } from '../../middlewares/auth/authorization'
import { validate, ValidationSource } from '../../middlewares/joiValidation'
import worktimeSchema from './worktimeSchema'

const router = express.Router()

// POST MY WORKTIMES
router.post(
  '/',
  authentication,
  authorization(ModelCode.MY_WORKTIME, MethodCode.CREATE),
  validate(worktimeSchema.createMyWorktimes),
  createMyWorktimes
)

// POST EMPLOYEE WORKTIMES
router.post(
  '/employees/:userId',
  authentication,
  authorization(ModelCode.WORKTIME, MethodCode.CREATE),
  validate(worktimeSchema.createEmployeeWorktimes),
  createEmployeeWorktimes
)

// GET MY WORKTIMES
router.get(
  '/my-worktime',
  authentication,
  authorization(ModelCode.MY_WORKTIME, MethodCode.LIST),
  getMyWorktimes
)

// GET ELMPLOYEE WORKTIMES
router.get(
  '/employees/:userId',
  authentication,
  authorization(ModelCode.WORKTIME, MethodCode.VIEW),
  validate(worktimeSchema.getOne, ValidationSource.PARAM),
  getEmployeeWorktimes
)

// UPDATE My Worktime
router.patch(
  '/:worktimeId',
  authentication,
  authorization(ModelCode.MY_WORKTIME, MethodCode.EDIT),
  validate(worktimeSchema.worktimeParam, ValidationSource.PARAM),
  validate(worktimeSchema.updateMyworktime),
  updateMyWorktime
)

// UPDATE Employee Worktime
router.patch(
  '/employees/:userId/:worktimeId',
  authentication,
  authorization(ModelCode.WORKTIME, MethodCode.EDIT),
  validate(worktimeSchema.worktimeEmployeeParam, ValidationSource.PARAM),
  validate(worktimeSchema.updateEmployeeWorktime),
  updateEmployeeWorktime
)

// DELETE My worktimes
router.delete(
  '',
  authentication,
  authorization(ModelCode.MY_WORKTIME, MethodCode.DELETE),
  validate(worktimeSchema.delete),
  deleteMyWorktime
)

// DELETE Employee Worktimes
router.delete(
  '/employees/:userId',
  authentication,
  authorization(ModelCode.WORKTIME, MethodCode.DELETE),
  validate(worktimeSchema.deleteParam, ValidationSource.PARAM),
  validate(worktimeSchema.delete),
  deleteEmployeeWorktime
)

export default router
