import Joi from "joi";
import { JoiObjectId } from "../../middlewares/joiValidation";

export default {
  create: Joi.object().keys({
    book: JoiObjectId().required(),
    office: JoiObjectId().required(),
    quantity: Joi.number().integer().required(),
  }),
  update: Joi.object().keys({
    book: JoiObjectId().optional(),
    office: JoiObjectId().optional(),
    quantity: Joi.number().integer().optional(),
  }),
  getOne: Joi.object().keys({
    bookStockId: JoiObjectId().required(),
  }),
};
