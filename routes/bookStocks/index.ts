import express from "express";
import {
  create,
  deleteOne,
  //   deleteOne,
  getAll,
  getAvailableStockPerOffice,
  getOne,
  updateOne,
} from "../../controllers/bookStockController";
import { MethodCode, ModelCode } from "../../db/models/Permission";
import { authentication } from "../../middlewares/auth/authentication";
import { authorization } from "../../middlewares/auth/authorization";
import { validate, ValidationSource } from "../../middlewares/joiValidation";
import bookStockSchema from "./bookStockSchema";
// import bookSchema from "./bookSchema";

const router = express.Router();

router.post(
  "/",
  authentication,
  authorization(ModelCode.BOOK_STOCK, MethodCode.CREATE),
  validate(bookStockSchema.create),
  create
);

router.get(
  "/",
  authentication,
  authorization(ModelCode.BOOK_STOCK, MethodCode.LIST),
  getAll
);

router.get(
  "/available",
  authentication,
  authorization(ModelCode.BOOK_STOCK, MethodCode.LIST),
  getAvailableStockPerOffice
);

router.get(
  "/:bookStockId",
  authentication,
  authorization(ModelCode.BOOK_STOCK, MethodCode.VIEW),
  validate(bookStockSchema.getOne, ValidationSource.PARAM),
  getOne
);

router.patch(
  "/:bookStockId",
  authentication,
  authorization(ModelCode.BOOK_STOCK, MethodCode.EDIT),
  validate(bookStockSchema.getOne, ValidationSource.PARAM),
  validate(bookStockSchema.update),
  updateOne
);

router.delete(
  "/:bookStockId",
  authentication,
  authorization(ModelCode.BOOK_STOCK, MethodCode.DELETE),
  validate(bookStockSchema.getOne, ValidationSource.PARAM),
  deleteOne
);

export default router;
