import express from 'express'
import {
  create,
  deleteMany,
  deleteOne,
  getAll,
  getOne,
  update
} from '../../controllers/statClientController'
import { authentication } from '../../middlewares/auth/authentication'
import { validate, ValidationSource } from '../../middlewares/joiValidation'
import statClientSchema from './statClientSchema'

const router = express.Router()

router.post(
  '/',
  authentication,
  // authorization(ModelCode.STAT_CLIENT, MethodCode.CREATE),
  validate(statClientSchema.create),
  create
)

router.get(
  '/',
  authentication,
  // authorization(ModelCode.STAT_CLIENT, MethodCode.LIST),
  getAll
)

router.get(
  '/:statClientId',
  authentication,
  // authorization(ModelCode.STAT_CLIENT, MethodCode.VIEW),
  validate(statClientSchema.idParams, ValidationSource.PARAM),
  getOne
)

router.patch(
  '/:statClientId',
  authentication,
  // authorization(ModelCode.STAT_CLIENT, MethodCode.VIEW),
  validate(statClientSchema.idParams, ValidationSource.PARAM),
  validate(statClientSchema.update),
  update
)

router.delete(
  '/delete',
  authentication,
  // authorization(ModelCode.STAT_CLIENT, MethodCode.VIEW),
  validate(statClientSchema.deleteMany),
  deleteMany
)

router.delete(
  '/:statClientId',
  authentication,
  // authorization(ModelCode.STAT_CLIENT, MethodCode.VIEW),
  validate(statClientSchema.idParams, ValidationSource.PARAM),
  deleteOne
)

export default router
