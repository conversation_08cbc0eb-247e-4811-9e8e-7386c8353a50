import Joi from 'joi'
import { JoiObjectId } from '../../middlewares/joiValidation'
export default {
  //Check route
  idParams: Joi.object().keys({
    statClientId: JoiObjectId().required(),
  }),

  //Create
  create: Joi.object().keys({
    name: Joi.string().required(),
    kpis: Joi.array().items(JoiObjectId()),
  }),
  //update
  update: Joi.object().keys({
    name: Joi.string().optional(),
    kpis: Joi.array().items(JoiObjectId()).optional(),
  }),
  //delete
  deleteMany: Joi.object().keys({
    statClientIds: Joi.array().items(JoiObjectId()).optional(),
  }),
}
