import express from "express";
import {
  createTodo,
  deleteMany,
  deleteOne,
  getMyTodos,
  getOne,
  getTheTodosFilteredTodos,
  getUsersAndOffices,
  update,
  updateStatus,
} from "../../controllers/todoController";
import { MethodCode, ModelCode } from "../../db/models/Permission";
import { authentication } from "../../middlewares/auth/authentication";
import { authorization } from "../../middlewares/auth/authorization";
import { validate, ValidationSource } from "../../middlewares/joiValidation";
import todoSchema from "./todoSchema";

const router = express.Router();

//POST
router.post(
  "/",
  authentication,
  authorization(ModelCode.TODO, MethodCode.CREATE),
  validate(todoSchema.create),
  createTodo
);

// GET MY TODOS
router.get(
  "/",
  authentication,
  authorization(ModelCode.TODO, MethodCode.LIST),
  validate(todoSchema.getAll, ValidationSource.PARAM),
  getMyTodos
);

// GET TODOS ASSIGNED TO ME
router.get(
  "/filter",
  authentication,
  authorization(ModelCode.TODO, MethodCode.LIST),
  validate(todoSchema.filter, ValidationSource.PARAM),
  getTheTodosFilteredTodos
);

router.get(
  "/users-offices",
  authentication,
  authorization(ModelCode.USER, MethodCode.LIST),
  authorization(ModelCode.OFFICE, MethodCode.LIST),
  getUsersAndOffices
);

// GET ONE TODO
router.get(
  "/:todoId",
  authentication,
  authorization(ModelCode.TODO, MethodCode.VIEW),
  validate(todoSchema.getOne, ValidationSource.PARAM),
  getOne
);

// DELETE ONE TODO
router.delete(
  "/:todoId",
  authentication,
  authorization(ModelCode.TODO, MethodCode.DELETE),
  validate(todoSchema.getOne, ValidationSource.PARAM),
  deleteOne
);

// DELETE Many TODO
router.delete(
  "/",
  authentication,
  authorization(ModelCode.TODO, MethodCode.DELETE),
  validate(todoSchema.deleteMany),
  deleteMany
);

// UPDATE TODO
router.patch(
  "/:todoId",
  authentication,
  authorization(ModelCode.TODO, MethodCode.EDIT),
  validate(todoSchema.getOne, ValidationSource.PARAM),
  validate(todoSchema.update),
  update
);

// UPDATE TODO STATUS
router.patch(
  "/status/:todoId",
  authentication,
  authorization(ModelCode.TODO, MethodCode.EDIT),
  validate(todoSchema.getOne, ValidationSource.PARAM),
  validate(todoSchema.updateStatus),
  updateStatus
);

export default router;
