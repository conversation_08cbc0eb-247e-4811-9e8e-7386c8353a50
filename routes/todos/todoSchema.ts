import Joi from "joi";
import { TodoPriority } from "../../db/models/Todo";
import { JoiObjectId } from "../../middlewares/joiValidation";

export default {
  create: Joi.object().keys({
    description: Joi.string().min(2).max(2000).required(),
    status: Joi.string().valid("todo", "completed").optional(),
    priority: Joi.string()
      .allow(null)
      .valid(
        TodoPriority.URGENT,
        TodoPriority.HIGH,
        TodoPriority.NORMAL,
        TodoPriority.LOW,
        TodoPriority.NONE
      )
      .optional(),
    mentions: Joi.array().items(Joi.string()).optional(),
  }),
  getOne: Joi.object().keys({
    todoId: JoiObjectId().required(),
  }),
  deleteMany: Joi.object().keys({
    todoIds: Joi.array().min(1).items(JoiObjectId()).required(),
  }),
  update: Joi.object().keys({
    description: Joi.string().min(2).max(2000).optional(),
    status: Joi.string().valid("todo", "completed").optional(),
    priority: Joi.string()
      .allow(null)
      .valid(
        TodoPriority.URGENT,
        TodoPriority.HIGH,
        TodoPriority.NORMAL,
        TodoPriority.LOW,
        TodoPriority.NONE
      )
      .optional(),
    mentions: Joi.array().items(JoiObjectId()).optional(),
  }),
  updateStatus: Joi.object().keys({
    status: Joi.string().valid("todo", "completed").optional(),
    priority: Joi.string()
      .allow(null)
      .valid(
        TodoPriority.URGENT,
        TodoPriority.HIGH,
        TodoPriority.NORMAL,
        TodoPriority.LOW,
        TodoPriority.NONE
      )
      .optional(),
  }),
  getAll: Joi.object().keys({
    startDate: Joi.string()
      .regex(/^\d{4}\-(0?[1-9]|1[012])\-(0?[1-9]|[12][0-9]|3[01])$/)
      .messages({
        "string.pattern.base": `startDate must be a valid date at this format : YYYY-MM-DD`,
      })
      .optional(),
    endDate: Joi.string()
      .regex(/^\d{4}\-(0?[1-9]|1[012])\-(0?[1-9]|[12][0-9]|3[01])$/)
      .messages({
        "string.pattern.base": `endDate must be a valid date at this format : YYYY-MM-DD`,
      })
      .optional(),
  }),
  filter: Joi.object().keys({
    startDate: Joi.string()
      .regex(/^\d{4}\-(0?[1-9]|1[012])\-(0?[1-9]|[12][0-9]|3[01])$/)
      .messages({
        "string.pattern.base": `startDate must be a valid date at this format : YYYY-MM-DD`,
      })
      .optional(),
    endDate: Joi.string()
      .regex(/^\d{4}\-(0?[1-9]|1[012])\-(0?[1-9]|[12][0-9]|3[01])$/)
      .messages({
        "string.pattern.base": `endDate must be a valid date at this format : YYYY-MM-DD`,
      })
      .optional(),
    assigned: Joi.boolean().optional(),
  }),
};
