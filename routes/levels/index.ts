import express from "express";
import {
  create,
  deleteOne,
  getAll,
  getOne,
  updateOne,
} from "../../controllers/levelController";
import { MethodCode, ModelCode } from "../../db/models/Permission";
import { authentication } from "../../middlewares/auth/authentication";
import { authorization } from "../../middlewares/auth/authorization";
import { validate, ValidationSource } from "../../middlewares/joiValidation";
import levelSchema from "./levelSchema";

const router = express.Router();

router.post(
  "/",
  authentication,
  authorization(ModelCode.LEVEL, MethodCode.CREATE),
  validate(levelSchema.create),
  create
);

router.get(
  "/",
  authentication,
  authorization(ModelCode.LEVEL, MethodCode.VIEW),
  getAll
);

router.get(
  "/:levelId",
  authentication,
  authorization(ModelCode.LEVEL, MethodCode.VIEW),
  validate(levelSchema.getOne, ValidationSource.PARAM),
  getOne
);

router.patch(
  "/:levelId",
  authentication,
  authorization(ModelCode.LEVEL, MethodCode.EDIT),
  validate(levelSchema.getOne, ValidationSource.PARAM),
  validate(levelSchema.update),
  updateOne
);

router.delete(
  "/:levelId",
  authentication,
  authorization(ModelCode.LEVEL, MethodCode.DELETE),
  validate(levelSchema.getOne, ValidationSource.PARAM),
  deleteOne
);

export default router;
