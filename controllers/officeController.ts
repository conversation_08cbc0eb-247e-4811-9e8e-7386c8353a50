import { Request, Response } from "express";
import OfficeRepo from "../db/repositories/OfficeRepo";
import { NotFoundError } from "../helpers/ApiError";
import { SuccessResponse } from "../helpers/ApiResponse";
import asyncHandler from "../middlewares/asyncHandler";

// Get All
export const getAll = asyncHandler(async (req: Request, res: Response) => {
  const offices = await OfficeRepo.findWithPagination({}, req.query);
  const { docs, ...meta } = offices;
  new SuccessResponse("Offices returned successfully", { docs, meta }).send(
    res
  );
});

// Get one
export const getOne = asyncHandler(async (req: Request, res: Response) => {
  const { officeId } = req.params;

  const office = await OfficeRepo.findOne({ _id: officeId });

  if (!office) {
    throw new NotFoundError("Office not found !");
  }

  new SuccessResponse("Office returned successfully", office).send(res);
});

//UPDATE ONE
export const update = asyncHandler(async (req: Request, res: Response) => {
  const { officeId } = req.params;

  const updatedOffice = await OfficeRepo.findOneAndUpdate(
    { _id: officeId },
    req.body
  );

  if (!updatedOffice) {
    throw new NotFoundError("Office not found !");
  }

  return new SuccessResponse("Office successfully updated", updatedOffice).send(
    res
  );
});
