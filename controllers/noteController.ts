import { Request, Response } from "express";
import asyncHandler from "../middlewares/asyncHandler";
import { BadRequestError, NotFoundError } from "../helpers/ApiError";
import { SuccessMsgResponse, SuccessResponse } from "../helpers/ApiResponse";
import NoteRepo from "../db/repositories/NoteRepo";
import { RoleCode } from "../db/models/User";
import UserRepo from "../db/repositories/UserRepo";
import noteService from "../services/noteService";
import emailEmitter, { events as emailEvent } from "../events/emailEvent";
import notifEmitter, { events as notifEvents } from "../events/notifEvent";
import { NOTIFICATIONS_DOCS } from "../db/models/Notification";

export const createNote = asyncHandler(async (req: Request, res: Response) => {
  const { title, mentions, content } = req.body;
  let mentionList = mentions;
  let concernedUsersPromise;

  // Check unique title
  const noteExist = await NoteRepo.findOne({ title });
  if (noteExist) {
    throw new BadRequestError("Already exist note with this title ");
  }

  // Check mentions
  const everyoneMentioned = content.includes('data-id="everyone"');
  if (everyoneMentioned) {
    mentionList = await UserRepo.findAndselect({ _id: { $ne: req.user._id } });
  }

  // upload File
  const files = req.files;
  let newFiles = [];
  if (Array.isArray(files))
    req.body.files = await noteService.saveFiles(files, req.user?._id);
  // Create note
  const notePromise = NoteRepo.create({
    ...req.body,
    mentions: mentionList,
    createdBy: req.user?._id,
  });

  // Find the concerned users
  if (everyoneMentioned) {
    concernedUsersPromise = mentionList;
  } else {
    concernedUsersPromise =
      mentionList && mentionList.length > 0
        ? UserRepo.findAndselect({
            $or: [
              { _id: { $in: mentionList } },
              { office: { $in: mentionList } },
            ],
            _id: { $ne: req.user._id },
          })
        : Promise.resolve([]);
  }

  // Await both the creation of the note and finding the concerned users
  const [note, concernedUsers] = await Promise.all([
    notePromise,
    concernedUsersPromise,
  ]);

  // emit the event
  if (concernedUsers.length > 0) {
    notifEmitter.emit(notifEvents.SEND_NOTIF, {
      currentUser: req.user,
      doc: note._id,
      docModel: NOTIFICATIONS_DOCS.NOTE,
      concernedUsers,
    });
    emailEmitter.emit(emailEvent.SEND_EMAIL, {
      currentUser: req.user,
      doc: note._id,
      concernedUsers,
    });
  }

  return new SuccessResponse("Note created successfully", note).send(res);
});

export const getAllNotes = asyncHandler(async (req: Request, res: Response) => {
  let filter = {};
  if (req.user?.role !== RoleCode.SUPER_ADMIN) {
    filter = {
      $or: [
        {
          createdBy: req.user?._id,
        },
        {
          $or: [
            { mentions: { $exists: false } },
            { mentions: { $size: 0 } },
            { mentions: { $in: [req.user?._id, req.user?.office?._id] } },
          ],
        },
      ],
    };
  }

  const notes = await NoteRepo.find(filter, req.query);
  const { docs, ...meta } = notes;
  return new SuccessResponse("All Notes are returned successfully", {
    docs,
    meta,
  }).send(res);
});

export const getNote = asyncHandler(async (req: Request, res: Response) => {
  let filter = {};
  const { noteId } = req.params;
  if (req.user?.role !== RoleCode.SUPER_ADMIN) {
    filter = {
      _id: noteId,
      $or: [
        {
          createdBy: req.user?._id,
        },
        {
          $or: [
            { mentions: { $exists: false } },
            { mentions: { $size: 0 } },
            { mentions: { $in: [req.user?._id, req.user?.office?._id] } },
          ],
        },
      ],
    };
  } else {
    filter = {
      _id: noteId,
    };
  }

  const note = await NoteRepo.findOne(filter);
  if (!note) throw new NotFoundError("Note Not Found !");
  return new SuccessResponse("Note returned successfully", note).send(res);
});

export const updateNote = asyncHandler(async (req: Request, res: Response) => {
  const { noteId } = req.params;
  const { mentions, content, files: oldFiles = [] } = req.body;
  let mentionList = mentions;
  const files = req.files;
  let newFiles = [];
  if (Array.isArray(files))
    newFiles = await noteService.saveFiles(files, req?.user?._id);

  // Check mentions
  if (content.includes('data-id="everyone"')) {
    const allUsers = await UserRepo.findAll({ _id: { $ne: req.user._id } });
    mentionList = allUsers.map((user) => user._id);
  }

  // update note
  const newNote = await NoteRepo.findOneAndUpdate(
    { _id: noteId },
    { ...req.body, mentions: mentionList, files: [...oldFiles, ...newFiles] }
  );

  // Notify the concerned users
  const concernedUsers =
    mentionList && mentionList.length > 0
      ? await UserRepo.findAll({
          $or: [
            { _id: { $in: mentionList } },
            { office: { $in: mentionList } },
          ],
          _id: { $ne: req.user._id },
        })
      : [];

  if (concernedUsers.length > 0) {
    notifEmitter.emit(notifEvents.SEND_NOTIF, {
      currentUser: req.user,
      doc: noteId,
      docModel: NOTIFICATIONS_DOCS.NOTE,
      concernedUsers,
    });
    emailEmitter.emit(emailEvent.SEND_EMAIL, {
      currentUser: req.user,
      doc: noteId,
      concernedUsers,
    });
  }

  if (!newNote) {
    throw new BadRequestError("Note Not Found!");
  }

  return new SuccessResponse("Note updated successfully", newNote).send(res);
});

export const deleteNotes = asyncHandler(async (req: Request, res: Response) => {
  const { noteIds } = req.body;
  await NoteRepo.deleteMany({ _id: { $in: noteIds } });
  return new SuccessMsgResponse("Notes successfully deleted").send(res);
});

export const deleteNote = asyncHandler(async (req: Request, res: Response) => {
  const { noteId } = req.params;
  const note = await NoteRepo.deleteOne({ _id: noteId });

  if (!note) throw new NotFoundError("Note Not Found !");
  return new SuccessMsgResponse("Note deleted successfully").send(res);
});
