import { Request, Response } from "express";
import { PaymentNote } from "../db/models/PaymentNote";
import PaymentNoteRepo from "../db/repositories/PaymentNoteRepo";
import { BadRequestError, NotFoundError } from "../helpers/ApiError";
import { SuccessMsgResponse, SuccessResponse } from "../helpers/ApiResponse";
import asyncHandler from "../middlewares/asyncHandler";

// create
export const create = asyncHandler(async (req: Request, res: Response) => {
  const paymentNote = await PaymentNoteRepo.create(req.body);
  return new SuccessResponse("Payment created successfully", paymentNote).send(
    res
  );
});

//Get all payment Notes
export const getAll = asyncHandler(async (req: Request, res: Response) => {
  const paymentNotes = (await PaymentNoteRepo.findWithPagination(
    {},
    req.query
  )) as unknown as PaginationModel;

  const { docs, ...meta } = paymentNotes;

  // Send a success response
  new SuccessResponse("Payment Notes returned successfully", {
    docs,
    meta,
  }).send(res);
});

// Get one
export const getOne = asyncHandler(async (req: Request, res: Response) => {
  const { paymentNoteId } = req.params;
  const paymentNote = await PaymentNoteRepo.findOne({ _id: paymentNoteId });

  if (!paymentNote) {
    throw new NotFoundError("Payment Note not found !");
  }

  return new SuccessResponse(
    "Payment Note returned successfully",
    paymentNote
  ).send(res);
});

// update one
export const updateOne = asyncHandler(async (req: Request, res: Response) => {
  const { paymentNoteId } = req.params;

  const updatedPaymentNote = await PaymentNoteRepo.updateOne(
    { _id: paymentNoteId },
    req.body
  );

  if (!updatedPaymentNote) {
    throw new NotFoundError("Payment Note not found !");
  }
  return new SuccessResponse(" Payment Note updated successfully", {
    updatedPaymentNote,
  }).send(res);
});

// delete one
export const deleteOne = asyncHandler(async (req: Request, res: Response) => {
  const { paymentNoteId } = req.params;

  const updatedPaymentNote = await PaymentNoteRepo.softDelete({
    _id: paymentNoteId,
  });

  if (!updatedPaymentNote) {
    throw new NotFoundError("Payment Note not found !");
  }

  return new SuccessMsgResponse("Payment Note deleted successfully").send(res);
});

export const deleteMany = asyncHandler(async (req: Request, res: Response) => {
  const { paymentNoteIds } = req.body; // Expecting an array of IDs

  if (!Array.isArray(paymentNoteIds) || paymentNoteIds.length === 0) {
    throw new BadRequestError("No payment notes specified for deletion!");
  }

  const updatedPaymentNotes = await PaymentNoteRepo.softDeleteMany({
    _id: { $in: paymentNoteIds }, // Match any of the IDs in the array
  });

  if (!updatedPaymentNotes || updatedPaymentNotes.modifiedCount === 0) {
    throw new NotFoundError("No payment notes found to delete!");
  }

  return new SuccessMsgResponse("Payment Notes deleted successfully").send(res);
});