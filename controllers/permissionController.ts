import { Request, Response } from "express";
import PermissionRepo from "../db/repositories/PermissionRepo";
import { BadRequestError } from "../helpers/ApiError";
import { SuccessResponse } from "../helpers/ApiResponse";
import asyncHandler from "../middlewares/asyncHandler";

//Create permission
export const create = asyncHandler(async (req: Request, res: Response) => {
  const { model, method } = req.body;

  const permissionExist = await PermissionRepo.findOne({ model, method });
  if (permissionExist) {
    throw new BadRequestError("Permission already exists !");
  }

  const newPermission = await PermissionRepo.create(req.body);

  return new SuccessResponse(
    "Permission created successfully",
    newPermission
  ).send(res);
});

//Get all permissions
export const getAll = asyncHandler(async (req: Request, res: Response) => {
  const permissions = (await PermissionRepo.find(
    {},
    req.query
  )) as unknown as PaginationModel;

  const { docs, ...meta } = permissions;
  new SuccessResponse("Permissions returned successfully", {
    docs,
    meta,
  }).send(res);
});
