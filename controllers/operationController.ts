import { endOfDay, startOfDay } from "date-fns";
import { Request, Response } from "express";
import { Types } from "mongoose";
import { OperationType } from "../db/models/Operation";
import { OperationHistoryAction } from "../db/models/OperationHistory";
import { RoleCode } from "../db/models/User";
import OperatioHistoryRepo from "../db/repositories/OperationHistoryRepo";
import OperationRepo from "../db/repositories/OperationRepo";
import { BadRequestError, NotFoundError } from "../helpers/ApiError";
import { SuccessMsgResponse, SuccessResponse } from "../helpers/ApiResponse";
import asyncHandler from "../middlewares/asyncHandler";
import calculationService from "../services/calculationService";

//Create operation
export const create = asyncHandler(async (req: Request, res: Response) => {
  const { amount, type } = req.body;
  let office = req.user?.office || req.body.office;

  if (!office) {
    throw new BadRequestError("Please tell us office");
  }

  if (type === OperationType.CREDIT) {
    // check amount & balance
    const balance = await calculationService.totalBalance(
      new Types.ObjectId(office)
    );

    if (balance - amount < 0) {
      throw new BadRequestError("Insufficient Balance !");
    }
  }

  // Create Operation
  const newOperation = await OperationRepo.create({
    ...req.body,
    admin: req.body?.admin || req?.user?._id,
    office: req?.user?.office?._id || req.body.office,
  });

  // Operation History
  OperatioHistoryRepo.create({
    ...req.body,
    office: req?.user?.office?._id || req.body.office,
    admin: newOperation?.admin,
    operation: newOperation._id,
    action: OperationHistoryAction.CREATE,
    createdBy: req.user?._id,
  });

  return new SuccessResponse(
    "Operation created successfully",
    newOperation
  ).send(res);
});

//Get all operations
export const getAll = asyncHandler(async (req: Request, res: Response) => {
  let filter: any = {};
  const { role, office } = req.user;
  const { startDate, endDate } = req.query;

  // Admins filter
  if (role !== RoleCode.SUPER_ADMIN && office) {
    filter = {
      office,
    };
  }

  // Filter By Date
  if (startDate && endDate) {
    filter.createdAt = {
      $gte: startOfDay(startDate as string),
      $lte: endOfDay(endDate as string),
    };
  }

  // Fetch operations based on the filter
  const operations = (await OperationRepo.findWithPagination(
    filter,
    req.query
  )) as unknown as PaginationModel;

  const { docs, ...meta } = operations;

  // Send a success response
  new SuccessResponse("Operations returned successfully", {
    docs,
    meta,
  }).send(res);
});

//Get one operation
export const getOne = asyncHandler(async (req: Request, res: Response) => {
  const { operationId } = req.params;
  const { role, office } = req.user;
  let filter: Object = { _id: operationId };

  /* Super admins and admins without an office can view all operations,
   while admins with an office can only see codes within their respective offices. */
  if (role !== RoleCode.SUPER_ADMIN && office) {
    filter = { ...filter, office: office._id };
  }

  const operation = await OperationRepo.findOne(filter);

  //Check if stat client response exist
  if (!operation) {
    throw new NotFoundError("Code Not Found !");
  }

  return new SuccessResponse("Code returned successfully", operation).send(res);
});

// Update operation
export const updateOne = asyncHandler(async (req: Request, res: Response) => {
  const { operationId } = req.params;
  const { role, office } = req.user;
  const { type, amount } = req.body;
  let filter: Object = { _id: operationId };

  // Super admins and admins without an office can update all operations, while admins with an office can only update codes within their respective offices.
  if (role !== RoleCode.SUPER_ADMIN && office) {
    filter = { ...filter, office: office?._id };
  }

  const existingOperation = await OperationRepo.findOne({ _id: operationId });

  if (!existingOperation) {
    throw new BadRequestError("Operation Not Found!");
  }

  if (existingOperation.type !== type) {
    throw new BadRequestError(
      "Operation type cannot be changed. Please create a new operation instead."
    );
  }

  if (existingOperation.type === OperationType.CREDIT && amount) {
    // check amount & balance
    const balance = await calculationService.totalBalance(
      new Types.ObjectId(existingOperation?.office?._id)
    );

    if (balance + existingOperation?.amount - amount < 0) {
      throw new BadRequestError("Insufficient Balance !");
    }
  }

  const operation = await OperationRepo.findOneAndUpdate(filter, req.body);

  // Operation History
  const operationHistory = OperatioHistoryRepo.create({
    type: operation?.type,
    amount: operation?.amount,
    notes: operation?.notes,
    office: operation?.office,
    admin: operation?.admin,
    operation: operation._id,
    action: OperationHistoryAction.UPDATE,
    createdBy: req.user?._id,
  });

  return new SuccessResponse("Operation updated successfully", operation).send(
    res
  );
});

// Delete operation
export const deleteOne = asyncHandler(async (req: Request, res: Response) => {
  const { operationId } = req.params;
  const { role, office } = req.user;
  let filter: Object = { _id: operationId };

  // Super admins and admins without an office can update all operations, while admins with an office can only update codes within their respective offices.
  if (role !== RoleCode.SUPER_ADMIN && office) {
    filter = { ...filter, office: office._id };
  }

  const operation = await OperationRepo.findOne(filter);

  if (!operation) {
    throw new BadRequestError("Operation Not Found!");
  }

  await operation.delete();

  // Operation History
  const operationHistory = OperatioHistoryRepo.create({
    type: operation?.type,
    amount: operation?.amount,
    notes: operation?.notes,
    office: operation?.office,
    admin: operation?.admin,
    operation: operation._id,
    action: OperationHistoryAction.DELETE,
    createdBy: req.user?._id,
  });

  return new SuccessMsgResponse("Operation deleted successfully").send(res);
});
