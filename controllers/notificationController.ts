import { Request, Response } from 'express'
import NotificationRepo from '../db/repositories/NotificationRepo'
import { SuccessMsgResponse, SuccessResponse } from '../helpers/ApiResponse'
import asyncHandler from '../middlewares/asyncHandler'
import { ProtectedRequest } from '../types/index'

// get all
export const getAll = asyncHandler(async (req: Request, res: Response) => {
  const notifications = await NotificationRepo.findWithPagination(
    { to: req.user._id },
    req.query
  )

  const { docs, ...meta } = notifications

  return new SuccessResponse('All notifications are returned successfully', {
    docs,
    meta,
  }).send(res)
})

// update many
export const markAllMyNotificationsAsRead = asyncHandler(
  async (req: Request, res: Response) => {
    const now = Date.now()
    await NotificationRepo.updateMany(
      { to: req.user._id, seen: false },
      { seen: true, seenAt: now }
    )

    return new SuccessMsgResponse(
      'All the notifications are marked as read.'
    ).send(res)
  }
)

// update one
export const markOneNotificationAsRead = asyncHandler(
  async (req: Request, res: Response) => {
    const { notificationId } = req.params
    const now = Date.now()
    await NotificationRepo.update(
      { _id: notificationId, to: req.user._id, seen: false },
      { seen: true, seenAt: now }
    )

    return new SuccessMsgResponse('This notification is marked as read.').send(
      res
    )
  }
)

// get the number of unread notifications
export const getUnreadNotifications = asyncHandler(
  async (req: Request, res: Response) => {
    const unread = await NotificationRepo.count({
      to: req.user._id,
      seen: false,
    })

    return new SuccessResponse('The number of the unread notifications.', {
      unread,
    }).send(res)
  }
)
