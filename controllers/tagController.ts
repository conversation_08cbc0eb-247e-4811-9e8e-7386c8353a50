import { Request, Response } from "express";
import TagRepo from "../db/repositories/TagRepo";
import asyncHandler from "../middlewares/asyncHandler";
import { BadRequestError, NotFoundError } from "../helpers/ApiError";
import { SuccessResponse } from "../helpers/ApiResponse";

export const createTags = asyncHandler(async (req: Request, res: Response) => {
  const { title, color } = req.body;
  const tagExist = await TagRepo.findOne({ title });
  if (tagExist) {
    throw new BadRequestError("Already exist tag with this title ");
  }
  const newTag = await TagRepo.create({ title, color });
  return new SuccessResponse("Tag created successfully ", newTag).send(res);
});

export const getOne = asyncHandler(async (req: Request, res: Response) => {
  const { tagId } = req.params;
  const tag = await TagRepo.findOne({
    _id: tagId,
  });
  //Check if tag exist
  if (!tag) {
    throw new NotFoundError("Tag Not Found !");
  }

  return new SuccessResponse("Tag returned successfully", tag).send(res);
});

export const getAllTags = asyncHandler(async (req: Request, res: Response) => {
  const tags = await TagRepo.find({}, req.query);

  const { docs, ...meta } = tags;

  return new SuccessResponse("All tags are returned successfully", {
    docs,
    meta,
  }).send(res);
});

export const updateTag = asyncHandler(async (req: Request, res: Response) => {
  const { tagId } = req.params;
  const newTag = await TagRepo.findAndUpdate({ _id: tagId }, req.body);
  if (!newTag) {
    throw new BadRequestError("Tag Not Found !");
  }

  return new SuccessResponse("Tag updated successfully", newTag).send(res);
});

export const deleteTag = asyncHandler(async (req: Request, res: Response) => {
  const { tagId } = req.params;
  const deletedTag = await TagRepo.deleteById(tagId);
  if (!deletedTag) throw new NotFoundError("Tag Not Found !");
  return new SuccessResponse("Tag successfully deleted", {}).send(res);
});
