import { endOfDay, startOfDay } from "date-fns";
import { Request, Response } from "express";
import { FilterQuery, Types } from "mongoose";
import { BookCashDeskPaymentDocument } from "../db/models/BookCashDeskPayment";
import { RoleCode } from "../db/models/User";
import BookCashDeskPaymentRepo from "../db/repositories/BookCashDeskPaymentRepo";
import { BadRequestError, NotFoundError } from "../helpers/ApiError";
import { SuccessMsgResponse, SuccessResponse } from "../helpers/ApiResponse";
import asyncHandler from "../middlewares/asyncHandler";
import BookCashDeskPaymentService from "../services/bookCashDeskPaymentService";
import booksCalculationService from "../services/booksCalculationService";

// Create cash desk payment
export const create = asyncHandler(async (req: Request, res: Response) => {
  const { amount, office } = req.body;
  let convertedOffice;

  // Super Admin check
  if (req.user?.role === RoleCode.SUPER_ADMIN) {
    if (office) {
      convertedOffice = office;
    } else {
      throw new BadRequestError("please tell us office !");
    }
  }

  // !Super Admin check
  if (req.user?.role !== RoleCode.SUPER_ADMIN) {
    if (office) {
      throw new BadRequestError("you are not allowed to select an office");
    } else if (req.user?.office?._id) {
      convertedOffice = req.user?.office?._id;
    } else {
      throw new BadRequestError("you are not allowed !");
    }
  }

  // Validate Sufficient Funds
  const isSufficientFunds =
    await booksCalculationService.validateSufficientFunds(
      new Types.ObjectId(convertedOffice),
      amount
    );

  if (!isSufficientFunds) {
    throw new BadRequestError("insufficient balance");
  }

  const bookCashDeskPayment = await BookCashDeskPaymentRepo.create({
    ...req.body,
    office: convertedOffice,
    cashedBy: req.user?._id,
  });

  // // Cash Desk Payment History
  // CashDeskPaymentHistoryRepo.create({
  //   ...req.body,
  //   cashDeskPayment: cashDeskPayment._id,
  //   office: cashDeskPayment?.office,
  //   cashedBy: cashDeskPayment?.cashedBy,
  //   action: CashDeskPaymentHistoryAction.CREATE,
  //   createdBy: req.user?._id,
  // });

  return new SuccessResponse(
    "Book cash desk payment created successfully",
    bookCashDeskPayment
  ).send(res);
});

export const getAll = asyncHandler(async (req: Request, res: Response) => {
  const { role, office } = req.user;
  const { startDate, endDate } = req.query;
  let filter: any = req.query;

  /* Super admins and admins without an office can view all book cash desk payments,
  while admins with an office can only see cash desk payments within their respective offices. */
  if (role !== RoleCode.SUPER_ADMIN && office) {
    if (req.query.office) {
      throw new BadRequestError("You are not allowed to select an office");
    }
    filter.office = req.user?.office?._id;
  }

  // Filter By Date
  if (startDate && endDate) {
    filter.createdAt = {
      $gte: startOfDay(startDate as string),
      $lte: endOfDay(endDate as string),
    };
  }

  const bookCashDeskPayments = await BookCashDeskPaymentService.findAll(
    req.query
  );

  return new SuccessResponse(
    "Book cash desk payments fetched successfully",
    bookCashDeskPayments
  ).send(res);
});

//Get one cash desk payment
export const getOne = asyncHandler(async (req: Request, res: Response) => {
  const { booksCashDeskPaymentId } = req.params;
  const { role, office } = req.user;
  let filter: FilterQuery<BookCashDeskPaymentDocument> = {
    _id: new Types.ObjectId(booksCashDeskPaymentId),
  };

  if (role !== RoleCode.SUPER_ADMIN && office) {
    filter.office = office?._id;
  }

  const bookCashDeskPayment = await BookCashDeskPaymentService.findOne(filter);
  //Check if cash desk payment exist
  if (!bookCashDeskPayment) {
    throw new NotFoundError("Book Cash Desk Payment Not Found !");
  }

  return new SuccessResponse(
    "Book Cash Desk Payment returned successfully",
    bookCashDeskPayment
  ).send(res);
});

// Update cash desk payment
export const update = asyncHandler(async (req: Request, res: Response) => {
  const { booksCashDeskPaymentId } = req.params;

  // check amount & balance
  // const balance = await calculationService.totalBalance(
  //   new Types.ObjectId(cashDeskPayment.office?._id),
  //   startDate,
  //   cashDeskPayment?.createdAt
  // );

  // const oldAmount = cashDeskPayment.amount;

  // if (amount > oldAmount && balance - (amount - oldAmount) < 0) {
  //   throw new BadRequestError("Insufficient balance");
  // }

  // update
  const updatedCashDeskPayment = await BookCashDeskPaymentRepo.findOneAndUpdate(
    { _id: new Types.ObjectId(booksCashDeskPaymentId) },
    req.body
  );

  // // Cash Desk Payment History
  // CashDeskPaymentHistoryRepo.create({
  //   cashDeskPayment: updatedCashDeskPayment,
  //   action: CashDeskPaymentHistoryAction.UPDATE,
  //   office: updatedCashDeskPayment?.office,
  //   cashedBy: updatedCashDeskPayment?.cashedBy,
  //   cashedTo: updatedCashDeskPayment?.cashedTo,
  //   amount: updatedCashDeskPayment?.amount,
  //   notes: updatedCashDeskPayment?.notes,
  //   createdBy: req.user?._id,
  // });

  return new SuccessResponse(
    "Cash desk payment updated successfully",
    updatedCashDeskPayment
  ).send(res);
});

// Delete one
export const deleteOne = asyncHandler(async (req: Request, res: Response) => {
  const { booksCashDeskPaymentId } = req.params;

  await BookCashDeskPaymentService.deleteOne({ _id: booksCashDeskPaymentId });

  return new SuccessMsgResponse(
    "Book Cash Desk Payment deleted successfully"
  ).send(res);
});
