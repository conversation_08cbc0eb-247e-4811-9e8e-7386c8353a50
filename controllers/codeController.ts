import { endOfDay, startOfDay } from "date-fns";
import { Request, Response } from "express";
import { RoleCode } from "../db/models/User";
import CodeRepo from "../db/repositories/CodeRepo";
import OfficeRepo from "../db/repositories/OfficeRepo";
import UserRepo from "../db/repositories/UserRepo";
import {
  BadRequestError,
  ForbiddenError,
  NotFoundError,
} from "../helpers/ApiError";
import { SuccessMsgResponse, SuccessResponse } from "../helpers/ApiResponse";
import asyncHandler from "../middlewares/asyncHandler";
import codeService from "../services/codeService";

// Create code
export const create = asyncHandler(async (req: Request, res: Response) => {
  const { admin, office, officeAddress } = req.body;
  let searchedAdmin = null;
  let searchedOffice = null;

  // Get admin
  if (admin) {
    searchedAdmin = await UserRepo.findOne({ username: admin });
  }

  // Get office
  if (office) {
    searchedOffice = await OfficeRepo.findOne({ name: office });

    //Create new Office if doesn't exist
    if (!office && officeAddress) {
      searchedOffice = await OfficeRepo.create({
        name: office,
        address: officeAddress,
      });
    }
  }

  const code = await CodeRepo.create({
    ...req.body,
    admin: searchedAdmin,
    office: searchedOffice,
  });

  return new SuccessResponse("Code created successfully", code).send(res);
});

//Get all codes
export const getAll = asyncHandler(async (req: Request, res: Response) => {
  const { role, office, showAllCodes } = req.user;
  const { startDate, endDate } = req.query;

  let filter: Object = {};

  if (startDate && endDate) {
    filter = {
      createdAt: {
        $gte: startOfDay(startDate as string),
        $lte: endOfDay(endDate as string),
      },
    };
  }

  // Super admins and admins without an office can view all codes, while admins with an office can only see codes within their respective offices.
  if (role !== RoleCode.SUPER_ADMIN && office && !showAllCodes) {
    filter = { ...filter, office };
  }

  // Fetch codes based on the filter
  const codes = (await CodeRepo.findWithPagination(
    filter,
    req.query
  )) as unknown as PaginationModel;

  const { docs, ...meta } = codes;

  // Send a success response
  new SuccessResponse("Codes returned successfully", {
    docs,
    meta,
  }).send(res);
});

//Get one code
export const getOne = asyncHandler(async (req: Request, res: Response) => {
  const { codeId } = req.params;
  const { role, office, showAllCodes } = req.user;
  let filter: Object = { _id: codeId };

  // Super admins and admins without an office can view all codes, while admins with an office can only see codes within their respective offices.
  if (role !== RoleCode.SUPER_ADMIN && office && !showAllCodes) {
    filter = { ...filter, office: office._id };
  }

  const code = await CodeRepo.findOne(filter);

  //Check if stat client response exist
  if (!code) {
    throw new NotFoundError("Code Not Found !");
  }

  return new SuccessResponse("Code returned successfully", code).send(res);
});

// Update Code
export const updateOne = asyncHandler(async (req: Request, res: Response) => {
  const { codeId } = req.params;
  const { isPayed, paymentNote } = req.body;

  //Check payedAt
  const oldCode = await CodeRepo.findOne({ _id: codeId });
  // If !code
  if (!oldCode) {
    throw new BadRequestError("Code Not Found!");
  }

  //Check status
  if (oldCode.isPayed === false && isPayed === true) {
    if (!paymentNote) {
      throw new BadRequestError(
        "Payment Note is not allowed when the payment status is set to 'paid'"
      );
    }
    req.body.payedAt = Date.now();
  }

  if (oldCode.isPayed === true && isPayed === false) {
    if (!paymentNote) {
      throw new BadRequestError(
        "A Payment Note is required when the payment status is set to 'unpaid'."
      );
    }
    req.body.payedAt = null;
  }

  const code = await CodeRepo.findOneAndUpdate({ _id: codeId }, req.body);

  if (!code) {
    throw new BadRequestError("Code Not Found!");
  }

  return new SuccessResponse("Code updated successfully", code).send(res);
});

// Delete one
export const deleteOne = asyncHandler(async (req: Request, res: Response) => {
  const { codeId } = req.params;
  const code = await CodeRepo.deleteOne({ _id: codeId });
  if (!code) throw new NotFoundError("Code Not Found !");
  return new SuccessMsgResponse("Code deleted successfully").send(res);
});

// Reload data
export const reloadCodes = asyncHandler(async (req: Request, res: Response) => {
  const { date } = req.query;

  if (date && req.user?.role !== RoleCode.SUPER_ADMIN) {
    throw new ForbiddenError(
      "You do not have the required role to specify a date for this operation"
    );
  }

  const reloadedCodes = await codeService.reloadCodes(date && (date as string));

  if (!reloadedCodes) {
    throw new BadRequestError();
  }

  // Send a success response
  new SuccessMsgResponse("Codes reloaded successfully").send(res);
});

// Update Code
export const updateCodePaymentMethod = asyncHandler(
  async (req: Request, res: Response) => {
    const { code } = req.body;

    const oldCode = await CodeRepo.findOne({ code });

    if (!oldCode) {
      throw new NotFoundError("Code not found !");
    }

    const deletedCode = await CodeRepo.deleteOne({ _id: oldCode._id });

    if (!deletedCode) {
      throw new BadRequestError();
    }

    return new SuccessResponse("Code updated successfully", oldCode).send(res);
  }
);
