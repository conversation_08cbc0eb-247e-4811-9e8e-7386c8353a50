import { Request, Response } from "express";
import { json2csv } from "json-2-csv";
import { Types } from "mongoose";
import { startMontAgentPerformance } from "../config";
import { RoleCode, UserDocument } from "../db/models/User";
import { WorktimeType } from "../db/models/Worktime";
import AgentPerformanceRepo from "../db/repositories/AgentPerformanceRepo";
import BonusAmountRepo from "../db/repositories/BonusAmountRepo";
import UserRepo from "../db/repositories/UserRepo";
import {
  BadRequestError,
  NoEntryError,
  NotFoundError,
} from "../helpers/ApiError";
import {
  NotFoundResponse,
  SuccessMsgResponse,
  SuccessResponse,
} from "../helpers/ApiResponse";
import transformer from "../helpers/export/transformer";
import asyncHandler from "../middlewares/asyncHandler";
import agentPerformanceService from "../services/agentPerformanceService";
import callsService from "../services/callsService";
import statClientResponseService from "../services/statClientResponseService";
import todoService from "../services/todoService";
import userService from "../services/userService";
import workTimeService from "../services/worktimeService";
import { client } from "../utils/redis";
import { Readable } from "stream";
import { convertMilliSecondsToYears } from "../utils/convertTime";
import dailyResponseService from "../services/dailyResponseService";

export const getAllAgentPerfomancePerMonth = asyncHandler(
  async (req: Request, res: Response) => {
    let { month } = req.query;
    let year = parseInt(req.query.year as string);

    const performanceData =
      await agentPerformanceService.getAllAgentPerfomancePerMonth(
        parseInt(month as string),
        year
      );

    new SuccessResponse(
      "Agent performance per month returned successfully",
      performanceData
    ).send(res);
  }
);

export const getAgentPerfomanceByUser = asyncHandler(
  async (req: Request, res: Response) => {
    let user: UserDocument = req.user;
    let year = parseInt(req.query.year as string);

    const agentPerformances =
      await agentPerformanceService.getPerformanceByUser(
        user,
        year,
        req.user?.role,
        req.query.user as string
      );

    new SuccessResponse(
      "Agent performance per month returned successfully",
      agentPerformances
    ).send(res);
  }
);

export const createAgentPerformance = asyncHandler(
  async (req: Request, res: Response) => {
    const { user, date, bonusAmountPct } = req.body;
    let endDate;

    const foundUser = await UserRepo.findOne({ _id: user });

    if (!foundUser) {
      throw new NotFoundError("User not found !");
    }

    const foundAgentPerformanceWithThisDate =
      await AgentPerformanceRepo.findOne({ user, date });
    if (foundAgentPerformanceWithThisDate) {
      throw new BadRequestError(
        "Agent performance already exists with this date"
      );
    }

    // Get user seniority
    let userSeniority = userService.calculateSeniority(foundUser, date);
    userSeniority = convertMilliSecondsToYears(userSeniority);

    const foundBonusAmount = await BonusAmountRepo.findOne({
      startDate: { $lte: date },
      endDate: { $gte: date },
    });

    if (!foundBonusAmount) {
      throw new NoEntryError("Bonus amount not found in this period");
    }

    // Find the range that matches the seniority
    const matchingRange = foundBonusAmount.ranges.find((range) => {
      return (
        userSeniority >= range.minSeniority &&
        userSeniority <= range.maxSeniority
      );
    });

    if (!matchingRange) {
      throw new Error("No bonus amount found for the given seniority.");
    }

    // calculate the bonus amount
    const bonusAmountDT = agentPerformanceService.calculateAgentBonusAmount(
      matchingRange.amount,
      bonusAmountPct
    );

    if (typeof date === "string") {
      const dateObj = new Date(date);
      endDate = new Date(
        Date.UTC(
          dateObj.getFullYear(),
          dateObj.getMonth() + 1,
          0,
          23,
          59,
          59,
          999
        )
      );
    }

    const [
      seniority,
      lateness,
      leave,
      authorization,
      calls,
      todos,
      statClientResponses,
      dailyResponses,
    ] = await Promise.all([
      userService.calculateSeniority(foundUser, date),
      userService.getUserLateness(foundUser._id, date, endDate),
      workTimeService.calculateAgentDaysOfLeave(foundUser._id, date, endDate),
      workTimeService.caculatePeriodByWorktimeType(
        WorktimeType.AUTHORIZATION,
        foundUser._id,
        date,
        endDate
      ),
      callsService.getTotalCallsForUser(foundUser._id, new Date(date), endDate),
      todoService.getNumberOFDoneToDo(date, endDate, user),
      statClientResponseService.getNumberStatClientResponseByUser(
        date,
        endDate,
        user._id
      ),
      dailyResponseService.getNumberDailyResponseByUser(
        date,
        endDate,
        user._id
      ),
    ]);

    const newAgentPerformance = await AgentPerformanceRepo.create({
      ...req.body,
      bonusAmountDT,
      seniority,
      lateness,
      leave,
      authorization,
      calls,
      todos,
      statClientResponses,
      dailyResponses,
    });

    return new SuccessResponse(
      "Agent Metrics created Successfully",
      newAgentPerformance
    ).send(res);
  }
);

export const updateAgentPerformance = asyncHandler(
  async (req: Request, res: Response) => {
    const { agentPerformanceId } = req.params;
    const { bonusAmountPct } = req.body;
    let update = req.body;

    const foundAgentPerformance = await AgentPerformanceRepo.findOne({
      _id: agentPerformanceId,
    });

    if (!foundAgentPerformance) {
      throw new NotFoundResponse("Agent Performance not found !");
    }

    const userSeniority = convertMilliSecondsToYears(
      foundAgentPerformance.seniority
    );

    if (bonusAmountPct) {
      const foundBonusAmout = await BonusAmountRepo.findOne({
        startDate: { $lte: foundAgentPerformance.date },
        endDate: { $gte: foundAgentPerformance.date },
      });

      if (!foundBonusAmout) {
        throw new NoEntryError("Bonus amount not found in this period");
      }
      // Find the range that matches the seniority
      const matchingRange = foundBonusAmout.ranges.find((range) => {
        return (
          userSeniority >= range.minSeniority &&
          userSeniority <= range.maxSeniority
        );
      });

      if (!matchingRange) {
        throw new Error("No bonus amount found for the given seniority.");
      }

      // calculate the bonus amount
      const bonusAmountDT = agentPerformanceService.calculateAgentBonusAmount(
        matchingRange.amount,
        bonusAmountPct
      );

      update = { ...update, bonusAmountDT };
    }

    const updatedAgentPerformance = await AgentPerformanceRepo.updateOne(
      {
        _id: agentPerformanceId,
      },
      update
    );

    return new SuccessResponse(
      "Agent performance updated Successfully",
      updatedAgentPerformance
    ).send(res);
  }
);

export const deleteAgentPerformance = asyncHandler(
  async (req: Request, res: Response) => {
    const { agentPerformanceId } = req.params;

    const agentPerformance = await AgentPerformanceRepo.findOne({
      _id: agentPerformanceId,
    });
    if (!agentPerformance) {
      throw new NotFoundError("Agent performance not found");
    }
    await agentPerformance.delete();
    return new SuccessMsgResponse(
      "Agent performance deleted successfully"
    ).send(res);
  }
);

export const reloadAllAgentPerformances = asyncHandler(
  async (req: Request, res: Response) => {
    let { month } = req.query;
    let year = parseInt(req.query.year as string);
    const currentYear = new Date().getFullYear();

    let startMonth;

    if (year > currentYear) {
      throw new BadRequestError(
        "Invalid year, The year cannot be greater than the current year"
      );
    }

    if (!month) {
      startMonth = year === 2024 ? startMontAgentPerformance : 0; // Start from April for 2024, else January
    } else {
      startMonth = parseInt(month as string);
    }

    await agentPerformanceService.refreshAgentPerformances(year, startMonth);

    return new SuccessMsgResponse(
      "All agent performances are updated successfully"
    ).send(res);
  }
);

export const exportAgentPerfomances = asyncHandler(
  async (req: Request, res: Response) => {
    const fileName = "Agent Performance";
    let user: UserDocument = req.user;
    let requestedUserId = req.query.user;
    let { month } = req.query;
    let year = parseInt(req.query.year as string);

    let agentPerformances;
    let customData;

    if (requestedUserId) {
      agentPerformances = await agentPerformanceService.getPerformanceByUser(
        user,
        year,
        req.user?.role,
        requestedUserId as string
      );

      customData = agentPerformances.map(transformer.agentPerformancePerUser);
    } else {
      const { allPerformances } =
        await agentPerformanceService.getAllAgentPerfomancePerMonth(
          parseInt(month as string),
          year
        );

      agentPerformances = allPerformances;

      customData = agentPerformances.map(transformer.agentPerformance);
    }

    const data =
      customData.length !== 0 ? json2csv(customData) : "No data to export";

    const readable = Readable.from([data], {
      encoding: "utf-8",
    });
    res.writeHead(200, {
      "Content-Type": "text/csv",
      "content-Disposition": `attachment; filename=${fileName}.csv`,
    });

    return readable.pipe(res);
  }
);
