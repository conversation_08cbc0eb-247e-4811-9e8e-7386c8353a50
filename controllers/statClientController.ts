import { Request, Response } from 'express'
import KpiRepo from '../db/repositories/KpiRepo'
import StatClientRepo from '../db/repositories/StatClientRepo'
import { BadRequestError, NotFoundError } from '../helpers/ApiError'
import { SuccessMsgResponse, SuccessResponse } from '../helpers/ApiResponse'
import asyncHandler from '../middlewares/asyncHandler'

//Create statClient
export const create = asyncHandler(async (req: Request, res: Response) => {
  const { name, kpis } = req.body
  //Check duplicated kpis
  if (kpis) {
    const hasDuplicates = new Set(kpis).size !== kpis.length
    if (hasDuplicates) {
      throw new BadRequestError('Duplicate kpis are not allowed.')
    }
    //Check valid kpis
    const validKpis = await KpiRepo.count({
      _id: { $in: kpis },
    })

    if (validKpis !== kpis.length) {
      throw new BadRequestError('Please verify kpis !')
    }
  }
  const newStatClient = await StatClientRepo.create(req.body)

  return new SuccessResponse(
    'Stat Client created successfully',
    newStatClient
  ).send(res)
})

//Get all stat clients
export const getAll = asyncHandler(async (req: Request, res: Response) => {
  const statClients = (await StatClientRepo.find(
    {},
    req.query
  )) as unknown as PaginationModel

  const { docs, ...meta } = statClients
  new SuccessResponse('Stat Clients returned successfully', {
    docs,
    meta,
  }).send(res)
})

//Get one stat client
export const getOne = asyncHandler(async (req: Request, res: Response) => {
  const { statClientId } = req.params
  const statClient = await StatClientRepo.findOne({
    _id: statClientId,
  })
  //Check if stat client exist
  if (!statClient) {
    throw new NotFoundError('Stat Client not found !')
  }

  return new SuccessResponse(
    'Stat Client returned successfully',
    statClient
  ).send(res)
})

//Update one
export const update = asyncHandler(async (req: Request, res: Response) => {
  const { statClientId } = req.params

  //Update
  const updatedStatClient = await StatClientRepo.findOneAndUpdate(
    {
      _id: statClientId,
    },
    { ...req.body }
  )

  //Check if stat client exist
  if (!updatedStatClient) {
    throw new NotFoundError('Stat Client not found !')
  }

  return new SuccessResponse(
    'Stat Client response successfully updated',
    updatedStatClient
  ).send(res)
})

//Delete one
export const deleteOne = asyncHandler(async (req: Request, res: Response) => {
  const { statClientId } = req.params

  //Check stat client
  const statClient = await StatClientRepo.findOne({
    _id: statClientId,
  })
  if (!statClient) {
    throw new NotFoundError('Stat Client Not Found !')
  }

  await statClient.delete()
  return new SuccessMsgResponse(
    'Stat Client Response successfully deleted'
  ).send(res)
})

// Delete many
export const deleteMany = asyncHandler(async (req: Request, res: Response) => {
  const { statClientIds } = req.body

  // Check stat clients existence
  const statClientsLength = await StatClientRepo.count({
    _id: { $in: statClientIds },
  })

  if (statClientsLength !== statClientIds.length) {
    throw new NotFoundError('One or more Stat Clients not found')
  }

  // Delete stat clients
  await StatClientRepo.updateMany(
    {
      _id: { $in: statClientIds },
    },
    {
      deleted: true,
      deletedAt: Date.now(),
    }
  )

  return new SuccessMsgResponse('Stat Clients successfully deleted').send(res)
})