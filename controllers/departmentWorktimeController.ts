import { Request, Response } from "express";
import { BadRequestError } from "../helpers/ApiError";
import { SuccessMsgResponse, SuccessResponse } from "../helpers/ApiResponse";
import asyncHandler from "../middlewares/asyncHandler";
import DepartmentWorktimeService from "../services/departmentWorktimeService";

// create
export const create = asyncHandler(async (req: Request, res: Response) => {
  const departmentWorktime = await DepartmentWorktimeService.create(req.body);
  return new SuccessResponse(
    "Department Worktime created successfully",
    departmentWorktime
  ).send(res);
});

// get one
export const getOne = asyncHandler(async (req: Request, res: Response) => {
  const { departmentWorktimeId } = req.params;

  const foundDepartmentWorktime = await DepartmentWorktimeService.findOne({
    _id: departmentWorktimeId,
  });

  return new SuccessResponse(
    "Department Worktime returned successfully",
    foundDepartmentWorktime
  ).send(res);
});

// get one by filter
export const getOneByFilter = asyncHandler(
  async (req: Request, res: Response) => {
    const { departmentId, year, month } = req.query;

    // Validate query parameters
    if (!departmentId || !year || !month) {
      throw new BadRequestError(
        "department, year, and month are required query parameters."
      );
    }

    const foundDepartmentWorktime = await DepartmentWorktimeService.findOne({
      department: departmentId,
      year: parseInt(year as string),
      month: parseInt(month as string),
    });

    return new SuccessResponse(
      "Department Worktime returned successfully",
      foundDepartmentWorktime
    ).send(res);
  }
);

//Get all
export const getAll = asyncHandler(async (req: Request, res: Response) => {
  const departmentWorktimes = await DepartmentWorktimeService.find(req.query);
  new SuccessResponse(
    "Department Worktimes returned successfully",
    departmentWorktimes
  ).send(res);
});

// Update one
export const updateOne = asyncHandler(async (req: Request, res: Response) => {
  const { departmentWorktimeId } = req.params;

  const updatedDepartmentWorktime =
    await DepartmentWorktimeService.findOneAndUpdate(
      { _id: departmentWorktimeId },
      req.body
    );

  return new SuccessResponse("Department Worktime updated successfully", {
    updatedDepartmentWorktime,
  }).send(res);
});

// Delete one
export const deleteOne = asyncHandler(async (req: Request, res: Response) => {
  const { departmentWorktimeId } = req.params;

  await DepartmentWorktimeService.deleteOne({ _id: departmentWorktimeId });

  return new SuccessMsgResponse(
    "Department Worktime deleted successfully"
  ).send(res);
});
