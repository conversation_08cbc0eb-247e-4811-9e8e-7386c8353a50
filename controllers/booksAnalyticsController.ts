import { Request, Response } from "express";
import { SuccessResponse } from "../helpers/ApiResponse";
import asyncHandler from "../middlewares/asyncHandler";
import BooksAnalyticsService from "../services/booksAnalyticsService";
import { endOfDay, startOfDay } from "date-fns";

//Get all Books Stock Number
export const getAllBooksStockNumber = asyncHandler(
  async (req: Request, res: Response) => {
    const allBooksStockNumber = await BooksAnalyticsService.allBooksNumber(
      req?.user,
      req.query
    );
    new SuccessResponse("All Books Stock Number returned successfully", {
      allBooksStockNumber,
    }).send(res);
  }
);

//Get all Books Stock Number
export const getBooksStockPerBook = asyncHandler(
  async (req: Request, res: Response) => {
    const booksStockPerBook =
      await BooksAnalyticsService.booksStockNumberPerBook(req?.user, req.query);
    new SuccessResponse(
      "Books Stock Per Book returned successfully",
      booksStockPerBook
    ).send(res);
  }
);

//Get revenue per office
export const getRevenuePerOffice = asyncHandler(
  async (req: Request, res: Response) => {
    const revenue = await BooksAnalyticsService.calculateRevenuePerOffice(
      req?.user,
      req.query
    );
    new SuccessResponse(
      "Revenue per Office returned successfully",
      revenue
    ).send(res);
  }
);

//Get all Books Stock Number
export const getGeneralBooksAnalytics = asyncHandler(
  async (req: Request, res: Response) => {
    const generalAnalytics =
      await BooksAnalyticsService.generalAnalyticsPerOffice(
        req?.user,
        req.query
      );

    new SuccessResponse("General Books Analytics returned successfully", {
      generalAnalytics,
    }).send(res);
  }
);

//Get books cash desk payments
export const getBooksCashDeskPayments = asyncHandler(
  async (req: Request, res: Response) => {
    const totalPayments =
      await BooksAnalyticsService.totalBooksCashDeskPayments(
        req?.user,
        req.query
      );

    new SuccessResponse("Books Cash Desk Payments returned successfully", {
      totalPayments,
    }).send(res);
  }
);

// Get total revenue
export const getTotalRevenue = asyncHandler(
  async (req: Request, res: Response) => {
    const totalRevenue = await BooksAnalyticsService.totalRevenue(
      req?.user,
      req.query
    );
    new SuccessResponse("Total Revenue returned successfully", {
      totalRevenue,
    }).send(res);
  }
);

// Get books stock number per office and book
export const getBooksStockNumberPerOfficeAndBook = asyncHandler(
  async (req: Request, res: Response) => {
    const booksStockNumberPerOfficeAndBook =
      await BooksAnalyticsService.booksStockNumberPerOfficeAndBook(
        req?.user,
        req.query
      );
    new SuccessResponse(
      "Books Stock Number Per Office And Book returned successfully",
      booksStockNumberPerOfficeAndBook
    ).send(res);
  }
);
