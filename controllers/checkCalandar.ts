import { addDays, endOfDay, startOfToday } from "date-fns";
import { NextFunction, Request, Response } from "express";
import OfficeRepo from "../db/repositories/OfficeRepo";
import UserRepo from "../db/repositories/UserRepo";
import WorktimeRepo from "../db/repositories/WortimeRepo";
import { BadRequestError, NotFoundError } from "../helpers/ApiError";
import { SuccessMsgResponse, SuccessResponse } from "../helpers/ApiResponse";
import asyncHandler from "../middlewares/asyncHandler";

// Check Calandar
export const checkCalandar = asyncHandler(
  async (req: Request, res: Response, next: NextFunction) => {
    const userId = req.user?._id;
    const currentDate = new Date();
    const dateRange = [];
    let worktimesNumber = 0;

    if (!req.user?.isAuthorized) {
      // Generate dates from current date to current date + 14 days
      for (let i = 0; i < 14; i++) {
        const date = new Date();
        date.setDate(currentDate.getDate() + i);
        date.setHours(0, 0, 0, 0); // Ensure the time part is set to 00:00:00
        dateRange.push(date);
      }

      for (let date of dateRange) {
        const worktime = await WorktimeRepo.findOne({
          userId,
          startDate: { $gte: date, $lte: endOfDay(date) },
        });

        if (date === dateRange[0] && !worktime) {
          throw new BadRequestError("Today is required !");
        }

        if (worktime) {
          worktimesNumber++;
        }
      }

      if (worktimesNumber >= 12) {
        await UserRepo.update(userId, {
          isAuthorized: true,
          authorizedUntil: addDays(startOfToday(), 14),
        });
      } else {
        throw new BadRequestError(
          "Authorization denied: You must complete your calendar with at least 12 worktimes in the next 14 days !"
        );
      }
    }

    new SuccessMsgResponse("Success").send(res);
  }
);
