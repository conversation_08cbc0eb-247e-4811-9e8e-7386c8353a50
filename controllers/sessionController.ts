import { endOfDay, format, startOfDay } from "date-fns";
import { Request, Response } from "express";
import { json2csv } from "json-2-csv";
import { PipelineStage } from "mongoose";
import { Readable } from "stream";
import SessionRepo from "../db/repositories/SessionRepo";
import { SuccessResponse } from "../helpers/ApiResponse";
import transformer from "../helpers/export/transformer";
import asyncHandler from "../middlewares/asyncHandler";
import sessionService from "../services/sessionService";

// Get All
export const getAll = asyncHandler(async (req: Request, res: Response) => {
  const { startDate, endDate, withPagination = "true" } = req.query;
  let filter = {};

  if (startDate && endDate) {
    filter = {
      createdAt: {
        $gte: startOfDay(startDate as string),
        $lte: endOfDay(endDate as string),
      },
    };
  }
  delete req.query.withPagination;
  const sessions = await SessionRepo.find(
    filter,
    req.query,
    withPagination === "true"
  );
  const { docs, ...meta } = sessions;
  return new SuccessResponse("All sessions are returned successfully", {
    docs,
    meta,
  }).send(res);
});

// filter sessions
export const filterSessions = asyncHandler(
  async (req: Request, res: Response) => {
    const { page, limit } = req.query;

    const options = {
      page: parseInt(page as string, 10) || 1,
      limit: parseInt(limit as string, 10) || 10,
    };

    const pipeline =
      sessionService.generatePipelineToGetUserFirstLoginLastLogout(req);

    const data = await SessionRepo.aggregatePaginate(
      pipeline as PipelineStage[],
      options
    );

    return new SuccessResponse(
      "All sessions are returned successfully",
      data
    ).send(res);
  }
);

export const exportSessions = asyncHandler(
  async (req: Request, res: Response) => {
    const pipeline =
      sessionService.generatePipelineToGetUserFirstLoginLastLogout(req);

    const data = await SessionRepo.aggregate(pipeline as PipelineStage[]);
    const customData = sessionService.getCustomData(data);

    const formattedData =
      customData.length !== 0 ? json2csv(customData) : "No data to export";

    const readable = Readable.from([formattedData], {
      encoding: "utf-8",
    });
    res.writeHead(200, {
      "Content-Type": "text/csv",
      "content-Disposition": `attachment; filename=$sessions.csv`,
    });

    readable.pipe(res);
  }
);
