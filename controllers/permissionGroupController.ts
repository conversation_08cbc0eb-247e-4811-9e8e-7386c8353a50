import { Request, Response } from 'express'
import PermissionRepo from '../db/repositories/PermissionRepo'
import PermissionGroupRepo from '../db/repositories/PermissionGroupRepo'
import { BadRequestError, NotFoundError } from '../helpers/ApiError'
import { SuccessMsgResponse, SuccessResponse } from '../helpers/ApiResponse'
import asyncHandler from '../middlewares/asyncHandler'

//Create permission group
export const create = asyncHandler(async (req: Request, res: Response) => {
  const { permissions } = req.body
  // Check for duplicates in the permissions array
  if (permissions) {
    const hasDuplicates = new Set(permissions).size !== permissions.length
    if (hasDuplicates) {
      throw new BadRequestError('Duplicate permissions are not allowed.')
    }
    //Check valid permissions
    const validPermissions = await PermissionRepo.count({
      _id: { $in: permissions },
    })

    if (validPermissions !== permissions.length) {
      throw new BadRequestError('Please verify permissions !')
    }
  }

  //Create permission group
  const permissionGroup = await PermissionGroupRepo.create(req.body)

  return new SuccessResponse(
    'Permission group created successfully',
    permissionGroup
  ).send(res)
})

//Get all permission groups
export const getAll = asyncHandler(async (req: Request, res: Response) => {
  const permissionGroups = await PermissionGroupRepo.find({}, req.query)
  const { docs, ...meta } = permissionGroups
  new SuccessResponse('Permission groups returned successfully', {
    docs,
    meta,
  }).send(res)
})

//Get one permission group
export const getOne = asyncHandler(async (req: Request, res: Response) => {
  const { permissionGroupId } = req.params
  const permissionGroup = await PermissionGroupRepo.findOne({
    _id: permissionGroupId,
  })
  //Check if tag exist
  if (!permissionGroup) {
    throw new NotFoundError('Permission Group not found !')
  }

  return new SuccessResponse(
    'Permission Group returned successfully',
    permissionGroup
  ).send(res)
})

//Update permission group
export const update = asyncHandler(async (req: Request, res: Response) => {
  const { permissionGroupId } = req.params
  const { permissions } = req.body
  // CHECK Duplicated Permissions
  if (permissions) {
    const hasDuplicates = new Set(permissions).size !== permissions.length
    if (hasDuplicates) {
      throw new BadRequestError('Duplicated permissions are not allowed.')
    }
    //Verify Permissions
    const validPermissions = await PermissionRepo.count({
      _id: { $in: permissions },
    })

    if (validPermissions !== permissions.length) {
      throw new BadRequestError('Please verify permissions !')
    }
  }

  // UPDATE
  const permissionGroup = await PermissionGroupRepo.findOneAndUpdate(
    { _id: permissionGroupId },
    { ...req.body }
  )
  // Check if permission group exist
  if (!permissionGroup) {
    throw new NotFoundError('Permission group not found !')
  }
  return new SuccessResponse(
    'Permission group successfully updated',
    permissionGroup
  ).send(res)
})

// DELETE permission group
export const deleteOne = asyncHandler(async (req: Request, res: Response) => {
  const { permissionGroupId } = req.params

  // Check permission group
  const permissionGroup = await PermissionGroupRepo.findOne({
    _id: permissionGroupId,
  })
  if (!permissionGroup) {
    throw new NotFoundError('Permission Group Not Found !')
  }
  await PermissionGroupRepo.deleteOne(permissionGroup)
  return new SuccessMsgResponse('Permission Group successfully deleted').send(
    res
  )
})
