import { Request, Response } from "express";
import { SuccessMsgResponse, SuccessResponse } from "../helpers/ApiResponse";
import asyncHandler from "../middlewares/asyncHandler";
import BookService from "../services/bookService";

// create
export const create = asyncHandler(async (req: Request, res: Response) => {
  if (req.file) {
    req.body = { ...req.body, image: req?.file?.filename };
  }

  const book = await BookService.create(req.body);
  return new SuccessResponse("Book created successfully", book).send(res);
});

// get one
export const getOne = asyncHandler(async (req: Request, res: Response) => {
  const { bookId } = req.params;

  const foundBook = await BookService.findOne({
    _id: bookId,
  });

  return new SuccessResponse("Book returned successfully", foundBook).send(res);
});

//Get all
export const getAll = asyncHandler(async (req: Request, res: Response) => {
  const books = await BookService.find(req.query);
  new SuccessResponse("Books returned successfully", books).send(res);
});

// Update one
export const updateOne = asyncHandler(async (req: Request, res: Response) => {
  const { bookId } = req.params;

  const updatedBook = await BookService.findOneAndUpdate(
    { _id: bookId },
    req.body
  );

  return new SuccessResponse("Book updated successfully", {
    updatedBook,
  }).send(res);
});

// Delete one
export const deleteOne = asyncHandler(async (req: Request, res: Response) => {
  const { bookId } = req.params;

  await BookService.deleteOne({ _id: bookId });

  return new SuccessMsgResponse("Book deleted successfully").send(res);
});
