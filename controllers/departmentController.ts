import { Request, Response } from "express";
import { SuccessMsgResponse, SuccessResponse } from "../helpers/ApiResponse";
import asyncHandler from "../middlewares/asyncHandler";
import DepartmentService from "../services/departmentService";

// create
export const create = asyncHandler(async (req: Request, res: Response) => {
  const department = await DepartmentService.create(req.body);
  return new SuccessResponse(
    "Department created successfully",
    department
  ).send(res);
});

// get one
export const getOne = asyncHandler(async (req: Request, res: Response) => {
  const { departmentId } = req.params;

  const foundDepartment = await DepartmentService.findOne({
    _id: departmentId,
  });

  return new SuccessResponse(
    "Department returned successfully",
    foundDepartment
  ).send(res);
});

//Get all
export const getAll = asyncHandler(async (req: Request, res: Response) => {
  const departments = await DepartmentService.find(req.query);
  new SuccessResponse("Departments returned successfully", departments).send(
    res
  );
});

// Update one
export const updateOne = asyncHandler(async (req: Request, res: Response) => {
  const { departmentId } = req.params;

  const updatedDepartment = await DepartmentService.findOneAndUpdate(
    { _id: departmentId },
    req.body
  );

  return new SuccessResponse("Department updated successfully", {
    updatedDepartment,
  }).send(res);
});

// Delete one
export const deleteOne = asyncHandler(async (req: Request, res: Response) => {
  const { departmentId } = req.params;

  await DepartmentService.deleteOne({ _id: departmentId });

  return new SuccessMsgResponse("Department deleted successfully").send(res);
});
