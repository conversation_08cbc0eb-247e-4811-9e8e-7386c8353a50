import axios from "axios";
import { Request, Response } from "express";
import jwt from "jsonwebtoken";
import { ObjectId } from "mongoose";
import {
  adminGroupPermissionName,
  oauth_takiacademy_url,
  superAdminCredentials,
  tokenInfo,
} from "../config";
import { OfficeDocument } from "../db/models/Office";
import { EventType, SessionNotes } from "../db/models/Session";
import { RoleCode, UserDocument } from "../db/models/User";
import AuthRepo from "../db/repositories/AuthRepo";
import OfficeRepo from "../db/repositories/OfficeRepo";
import PermissionGroupRepo from "../db/repositories/PermissionGroupRepo";
import UserRepo from "../db/repositories/UserRepo";
import { AuthFailureError, NotFoundError } from "../helpers/ApiError";
import { SuccessMsgResponse, SuccessResponse } from "../helpers/ApiResponse";
import asyncHandler from "../middlewares/asyncHandler";
import authService from "../services/authService";
import sessionService from "../services/sessionService";
import userService from "../services/userService";
import { removeToken } from "../utils/authUtils";
import { client } from "../utils/redis";

//Generate Token
const signToken = (id: string) => {
  return jwt.sign({ id }, tokenInfo.jwt_secret, {
    expiresIn: tokenInfo.jwt_expires_in,
  });
};

//Send Token
const createSendToken = async (user: UserDocument, res: Response) => {
  const token = signToken(user._id);
  const cookieOptions = {
    expires: new Date(
      Date.now() + tokenInfo.jwt_cookie_expires_in * 24 * 60 * 60 * 1000
    ),
    // secure: true,
    httpOnly: true,
  };

  res.cookie("jwt", token, cookieOptions);

  // SET REDIS TOKEN
  await client.set(`token-${user._id}`, token);

  //user
  return new SuccessResponse("success", { token, user }).send(res);
};

//Get me
export const getMe = asyncHandler(async (req: Request, res: Response) => {
  const id = req.user?._id;
  const user = await AuthRepo.findById(id);
  if (!user) {
    throw new NotFoundError("User not found !");
  }
  new SuccessResponse("Success", user).send(res);
});

//LOGIN
export const OAuthTakiacademy = asyncHandler(
  async (req: Request, res: Response) => {
    const { credential, password, ipAddress } = req.body;
    let user: UserDocument;
    let office: OfficeDocument;
    let userData: Partial<UserDocument>;

    //Check if user already registred
    let OAuthResponse = await axios({
      method: "post",
      url: oauth_takiacademy_url,
      data: {
        credential,
        password,
      },
    });

    //Check response
    if (OAuthResponse.data.code !== 200) {
      throw new AuthFailureError();
    }

    const externalUser = OAuthResponse?.data?.payload?.adminUser;

    //Check if user registred
    user = await AuthRepo.findOneByToken(OAuthResponse?.data?.payload?.token);

    //Check user info
    if (user) {
      await authService.updateUserInfo(externalUser, user);
    }

    //If user not registred
    if (!user) {
      const {
        username,
        name,
        email,
        avatar,
        offices,
        roles,
        first_day_work,
        phone,
      } = OAuthResponse?.data?.payload?.adminUser;
      const isSuperAdmin = roles?.includes("ROLE_SUPER_ADMIN");

      //Check if office exist
      if (offices.length !== 0) {
        office = await OfficeRepo.findOne({
          officeId: offices[0].id,
        });

        //Create new Office if doesn't exist
        if (!office) {
          office = await OfficeRepo.create({
            name: offices[0].name,
            address: offices[0].address,
            officeId: offices[0].id,
          });
        }
      }

      userData = {
        username,
        name,
        email,
        avatar,
        office: office?._id ? office?._id : null,
        OAuthToken: OAuthResponse?.data?.payload?.token,
        authorizedUntil: null,
        firstDayWork: first_day_work,
        phone,
      };

      if (!isSuperAdmin) {
        //Get group permission
        const permissionGroup = await PermissionGroupRepo.findOne({
          name: adminGroupPermissionName,
        });
        if (!permissionGroup) {
          throw new NotFoundError("Permission group not found !");
        }
        userData = {
          ...userData,
          role: RoleCode.ADMIN,
          permissionGroup: [permissionGroup?._id],
        };
      } else {
        //Get group permission
        const permissionGroup = await PermissionGroupRepo.findOneSuperAdmin({
          name: superAdminCredentials.superAdminGroupName,
        });
        if (!permissionGroup) {
          throw new NotFoundError("Permission group not found !");
        }
        userData = {
          ...userData,
          role: RoleCode.SUPER_ADMIN,
          permissionGroup: [permissionGroup?._id],
        };
      }

      // set single device
      if (isSuperAdmin || userData.office === null) {
        userData = { ...userData, singleDevice: false, isAuthorized: true };
      }

      //Save user
      user = await UserRepo.create(userData);
    } else {
      office = user.office;
    }

    // single device check for admins with office
    if (user.singleDevice) {
      // check ip address
      if (office?.ipAddress) {
        if (ipAddress !== office?.ipAddress) {
          throw new AuthFailureError("You can't access with this device !");
        }
      }

      // Check device id cookies
      if (user.deviceId) {
        let userDeviceId = req.cookies[user?._id];
        if (userDeviceId !== user?.deviceId) {
          throw new AuthFailureError("You can't access with this device !");
        }
      } else {
        // machineId
        const { deviceId, cookieOptions } =
          await userService.createDeviceIdCookie();

        user.deviceId = deviceId;
        await user.save();

        res.cookie(user?._id, deviceId, cookieOptions);
      }
    }

    user.OAuthToken = undefined;

    // save session
    await sessionService.create(req, user?._id, EventType.LOGIN);

    await createSendToken(user, res);
  }
);

export const logoutAdmin = asyncHandler(async (req: Request, res: Response) => {
  const { userId } = req.params;

  // check if the user exists
  const user = await UserRepo.findOne({ _id: userId });
  if (!user) {
    throw new NotFoundError("User not found !");
  }

  // remove the token
  const tokenRemoved = await removeToken(userId);
  // verify if the token is removed
  if (!tokenRemoved) {
    throw new NotFoundError("Token not found !");
  }

  // save session
  await sessionService.create(
    req,
    userId as unknown as ObjectId,
    EventType.LOGOUT,
    SessionNotes.CONDUCTED_LOGOUT
  );

  new SuccessMsgResponse("Logged out successfully").send(res);
});

export const logout = asyncHandler(async (req: Request, res: Response) => {
  const userId = req.user._id;

  // remove the token
  const tokenRemoved = await removeToken(userId);

  // verify if the token is removed
  if (!tokenRemoved) {
    throw new NotFoundError("Token not found !");
  }

  // save session
  await sessionService.create(
    req,
    userId,
    EventType.LOGOUT,
    SessionNotes.SELF_LOGOUT
  );

  new SuccessMsgResponse("Logged out successfully").send(res);
});
