import { Request, Response } from "express";
import { Types } from "mongoose";
import FileRepo from "../db/repositories/FileRepo";
import FolderRepo, {
  populateInDepthFiveSubFolder,
} from "../db/repositories/FolderRepo";
import { BadRequestError, NotFoundError } from "../helpers/ApiError";
import { SuccessMsgResponse, SuccessResponse } from "../helpers/ApiResponse";
import asyncHandler from "../middlewares/asyncHandler";
import { RoleCode } from "../db/models/User";

export const getAllFolder = asyncHandler(
  async (req: Request, res: Response) => {
    const user = req?.user;
    const isSuperAdmin =
      user?.role === RoleCode.SUPER_ADMIN || !req?.user.office;
    const filter = {
      parentFolder: null,
      ...(isSuperAdmin
        ? {}
        : {
            $or: [{ show: { $in: [user.office] } }, { show: [] }],
          }),
    };
    const folders = await FolderRepo.find(filter, req.query);
    const { docs, ...meta } = folders;
    return new SuccessResponse("All Folders returned successfully", {
      docs,
      meta,
    }).send(res);
  }
);
export const getOneFolder = asyncHandler(
  async (req: Request, res: Response) => {
    const { folderId } = req.params;
    const folder = await FolderRepo.findOne({ _id: folderId });
    if (!folder) throw new NotFoundError("Folder not found !");
    return new SuccessResponse("Folder returned successfully", { folder }).send(
      res
    );
  }
);
export const createFolder = asyncHandler(
  async (req: Request, res: Response) => {
    const { name, parentFolder } = req.body;
    const isFolderExistWithSameName = await FolderRepo.findOne({
      name,
      parentFolder,
    });
    if (isFolderExistWithSameName)
      throw new BadRequestError("Already Folder  created with this name");

    const newFolder = await FolderRepo.create({
      ...req.body,
      user: req.user?._id,
    });
    if (parentFolder) {
      // check if folcer in sub folder of level 5
      const beyondFiveLevelChild = await FolderRepo.isAtMaxDepth(parentFolder);
      if (beyondFiveLevelChild)
        throw new BadRequestError("Cannot create folder beyond level 5");
      const parent = await FolderRepo.findOne({ _id: parentFolder });

      if (parent) {
        parent.subFolder.push(newFolder._id);
        await parent.save();
      }
    }
    return new SuccessResponse("Folder created successfully", newFolder).send(
      res
    );
  }
);
export const deleteFolder = asyncHandler(
  async (req: Request, res: Response) => {
    const { folderId } = req.params;
    const folderExist = await FolderRepo.findOne({
      _id: folderId,
    });
    if (!folderExist) throw new NotFoundError("Folder Not Found! ");
    const { parentFolder: parentFolderId } = folderExist;
    if (parentFolderId) {
      const parentFolder = await FolderRepo.findOne({ _id: parentFolderId });
      if (parentFolder) {
        parentFolder.subFolder = parentFolder?.subFolder?.filter(
          (elt) => String(elt) != folderId
        );
        await parentFolder.save();
      }
    }
    await FolderRepo.delete({ _id: folderId });
    return new SuccessResponse("Folder deleted successfully", {}).send(res);
  }
);
export const updateFolder = asyncHandler(
  async (req: Request, res: Response) => {
    const { folderId } = req.params;
    const newFolder = await FolderRepo.findByIdAndUpdate(folderId, req.body);
    if (!newFolder) throw new NotFoundError("Folder not Found!");
    const folderWithPopulate = await newFolder.populate(
      populateInDepthFiveSubFolder
    );
    return new SuccessResponse(
      "Folder updated successfully",
      folderWithPopulate
    ).send(res);
  }
);
export const uploadFileToFolder = asyncHandler(
  async (req: Request, res: Response) => {
    const { folderId } = req.params;
    const receivedFiles = req.files;
    const folder = await FolderRepo.findOne({
      _id: folderId,
    });

    let newFiles = [];

    if (Array.isArray(receivedFiles)) {
      const files = receivedFiles?.map((file: Express.Multer.File) => ({
        name: file.filename,
        originalName: file.originalname,
        type: file.mimetype,
        path: file.path,
        addedBy: req.user?._id,
        size: file.size,
        folder: folderId,
      }));
      newFiles = await FileRepo.insertMany(files);
    }
    folder.files.push(...newFiles?.map((file) => new Types.ObjectId(file._id)));
    await folder.save();
    const folderWithPopulate = await folder.populate(
      populateInDepthFiveSubFolder
    );
    return new SuccessResponse(
      "Files  uploaded successfully",
      folderWithPopulate
    ).send(res);
  }
);
export const removeFileFromFolder = asyncHandler(
  async (req: Request, res: Response) => {
    const { folderId, fileId } = req.params;
    const folder = await FolderRepo.findOne({
      _id: folderId ? folderId : null,
    });
    folder.files = folder.files.filter(
      (file) => file.toString() !== fileId.toString()
    );
    await folder.save();
    return new SuccessResponse("File removed successfully", folder).send(res);
  }
);
export const moveFileFromFolder = asyncHandler(
  async (req: Request, res: Response) => {
    const { from, to, fileId } = req.query;
    const [sourceFolder, destinationFolder, file] = await Promise.all([
      FolderRepo.findOne({ _id: from }),
      FolderRepo.findOne({ _id: to }),
      FileRepo.findOne({
        _id: fileId,
      }),
    ]);
    if (!sourceFolder || !destinationFolder) {
      throw new NotFoundError("Source or destination folder not found");
    }
    if (!file) throw new NotFoundError("File not Found!");
    const indexSource = sourceFolder.files.findIndex((file) =>
      file.equals(fileId.toString())
    );
    const indexDestination = destinationFolder.files.findIndex((file) =>
      file.equals(fileId.toString())
    );
    if (indexSource < 0) {
      throw new NotFoundError("File not found in from folder!");
    }
    if (indexDestination > -1)
      throw new BadRequestError("File already exist in to folder");
    sourceFolder.files.splice(indexSource, 1);
    destinationFolder.files.push(new Types.ObjectId(fileId.toString()));
    await Promise.all([sourceFolder.save(), destinationFolder.save()]);
    return new SuccessMsgResponse("File move successfully").send(res);
  }
);
