import { endOfDay, startOfDay } from "date-fns"; // You can use a library like date-fns to work with dates
import { Request, Response } from "express";
import { RoleCode } from "../db/models/User";
import StatClientResponseRepo from "../db/repositories/StatClientResponseRepo";
import { BadRequestError, NotFoundError } from "../helpers/ApiError";
import { SuccessMsgResponse, SuccessResponse } from "../helpers/ApiResponse";
import asyncHandler from "../middlewares/asyncHandler";
import statClientResponseService from "../services/statClientResponseService";

//Create statClientResponse
export const create = asyncHandler(async (req: Request, res: Response) => {
  const newStatClientResponse = await StatClientResponseRepo.create({
    ...req.body,
    admin: req.user?._id,
    office: req.user?.office,
  });

  return new SuccessResponse(
    "Stat Client Response created successfully",
    newStatClientResponse
  ).send(res);
});

//Get all statClientResponses
export const getAll = asyncHandler(async (req: Request, res: Response) => {
  let filter: Object = {};

  //Admins can see their today's statClients only
  if (req.user?.role !== RoleCode.SUPER_ADMIN) {
    // Define the date range for today
    const todayStart = startOfDay(new Date());
    const todayEnd = endOfDay(new Date());

    filter = {
      office: req.user?.office,
      createdAt: {
        $gte: todayStart,
        $lte: todayEnd,
      },
    };
  }

  // Fetch statClientResponses based on the filter
  const statClientsResponses = (await StatClientResponseRepo.findWithPagination(
    filter,
    req.query
  )) as unknown as PaginationModel;

  const { docs, ...meta } = statClientsResponses;

  // Send a success response
  new SuccessResponse("Stat Client Response returned successfully", {
    docs,
    meta,
  }).send(res);
});

//Get one stat client response
export const getOne = asyncHandler(async (req: Request, res: Response) => {
  const { statClientResponseId } = req.params;
  let filter: Object = { _id: statClientResponseId };

  //Admins can see their today's Stat Client Responses only
  if (req.user?.role !== RoleCode.SUPER_ADMIN) {
    // Define the date range for today
    const todayStart = startOfDay(new Date());
    const todayEnd = endOfDay(new Date());

    filter = {
      _id: statClientResponseId,
      office: req.user?.office,
      createdAt: {
        $gte: todayStart,
        $lte: todayEnd,
      },
    };
  }

  const statClientResponse = await StatClientResponseRepo.findOne(filter);

  //Check if stat client response exist
  if (!statClientResponse) {
    throw new NotFoundError("Stat Client Response not found !");
  }

  return new SuccessResponse(
    "Stat Client Response returned successfully",
    statClientResponse
  ).send(res);
});

//UPDATE ONE
export const update = asyncHandler(async (req: Request, res: Response) => {
  const { statClientResponseId } = req.params;
  let filter: Object = { _id: statClientResponseId };

  //Get statClientResponse
  const statClientResponse = await StatClientResponseRepo.findOne({
    _id: statClientResponseId,
  });
  if (
    req.user?.role !== RoleCode.SUPER_ADMIN &&
    statClientResponse.admin?._id?.toString() !== req.user?._id.toString()
  ) {
    throw new BadRequestError("You can't edit this stat client response");
  }

  //Update
  const updatedStatClientResponse =
    await StatClientResponseRepo.findOneAndUpdate(
      {
        _id: statClientResponseId,
      },
      { ...req.body }
    );

  return new SuccessResponse(
    "Stat Client response successfully updated",
    updatedStatClientResponse
  ).send(res);
});

// DELETE ONE
export const deleteOne = asyncHandler(async (req: Request, res: Response) => {
  const { statClientResponseId } = req.params;

  //Check stat client response
  const statClientResponse = await StatClientResponseRepo.findOne({
    _id: statClientResponseId,
  });
  if (!statClientResponse) {
    throw new NotFoundError("Stat Client Response Category Not Found !");
  }
  if (
    req.user?.role !== RoleCode.SUPER_ADMIN &&
    statClientResponse.admin?._id?.toString() !== req.user?._id.toString()
  ) {
    throw new BadRequestError("You can't delete this Stat-Client Response");
  }

  await statClientResponse.delete();
  return new SuccessMsgResponse(
    "Stat Client Response successfully deleted"
  ).send(res);
});

// DELETE MANY
export const deleteMany = asyncHandler(async (req: Request, res: Response) => {
  const { statClientResponseIds } = req.body;
  let queryCheck = {
    $and: [{ _id: { $in: statClientResponseIds } }, { admin: req.user?._id }],
  };
  if (req.user?.role === RoleCode.SUPER_ADMIN) {
    queryCheck = {
      $and: [{ _id: { $in: statClientResponseIds } }],
    };
  }

  // Check stat client responses existence
  const statClientResponseLength = await StatClientResponseRepo.count(
    queryCheck
  );

  // Check valid stat client responses idsƒ
  if (statClientResponseLength !== statClientResponseIds.length) {
    throw new NotFoundError(
      "You either don't have authorization to delete it or one or more Stat Client Responses were not found."
    );
  }

  // Delete stat clients
  await StatClientResponseRepo.updateMany(
    {
      _id: { $in: statClientResponseIds },
    },
    {
      deleted: true,
      deletedAt: Date.now(),
    }
  );

  return new SuccessMsgResponse(
    "Stat Client Responses successfully deleted"
  ).send(res);
});

//Get stat client response filtered
export const getByFilter = asyncHandler(async (req: Request, res: Response) => {
  const query = statClientResponseService.handleQuery(req.query);

  const statClientResponses = await StatClientResponseRepo.findPaginate(
    query,
    req.query
  );
  const { docs, ...meta } = statClientResponses;

  return new SuccessResponse("Stat Client Response returned successfully", {
    docs,
    meta,
  }).send(res);
});
