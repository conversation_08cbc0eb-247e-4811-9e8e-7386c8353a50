import { Request, Response } from 'express'
import { Types } from 'mongoose'
import OfficeRepo from '../db/repositories/OfficeRepo'
import UserRepo from '../db/repositories/UserRepo'
import { BadRequestError, NotFoundError } from '../helpers/ApiError'
import { SuccessMsgResponse, SuccessResponse } from '../helpers/ApiResponse'
import asyncHandler from '../middlewares/asyncHandler'
import PermissionGroupRepo from '../db/repositories/PermissionGroupRepo'
import PermissionRepo from '../db/repositories/PermissionRepo'

export const createUser = asyncHandler(async (req: Request, res: Response) => {
  const { email } = req.body
  const userExist = await UserRepo.findOne({ email })
  if (userExist) {
    throw new BadRequestError('A user with this email already exists !')
  }

  const newUser = await UserRepo.create(req.body)

  return new SuccessResponse('User created successfully', newUser).send(res)
})

//Get all users
export const getAll = asyncHandler(async (req: Request, res: Response) => {
  const users = (await UserRepo.findWithPagination(
    {},
    req.query
  )) as unknown as PaginationModel;
  const { docs, ...meta } = users
  new SuccessResponse('Users returned successfully', { docs, meta }).send(res)
})

//Get one user
export const getOne = asyncHandler(async (req: Request, res: Response) => {
  const { userId } = req.params

  const user = await UserRepo.findOne({ _id: userId })

  if (!user) {
    throw new NotFoundError('User not found !')
  }

  return new SuccessResponse('User returned successfully', user).send(res)
})

//Delete user
export const deleteOne = asyncHandler(async (req: Request, res: Response) => {
  const { userId } = req.params

  //Check user
  const user = await UserRepo.findOne(new Types.ObjectId(userId))
  if (!user) {
    throw new NotFoundError('User Not Found !')
  }
  await user.delete()
  return new SuccessMsgResponse('User successfully deleted').send(res)
})

// edit user
export const update = asyncHandler(async (req: Request, res: Response) => {
  const { userId } = req.params
  const { office, permissionGroup, extraPermissions } = req.body

  const user = await UserRepo.findOne({ _id: userId })

  if (!user) throw new NotFoundError('User not found')

  // check for duplicated group permissions
  if (permissionGroup) {
    const hasDuplicates =
      new Set(permissionGroup).size !== permissionGroup.length
    if (hasDuplicates) {
      throw new BadRequestError('Duplicated permission groups are not allowed.')
    }
    //Check valid group permissions

    const validPermissionGroups = await PermissionGroupRepo.count({
      _id: { $in: permissionGroup },
    })

    if (validPermissionGroups !== permissionGroup.length) {
      throw new BadRequestError('Please verify permission groups !')
    }
  }

  // check for duplicated extra permissions
  if (extraPermissions) {
    const hasDuplicates =
      new Set(extraPermissions).size !== extraPermissions.length
    if (hasDuplicates) {
      throw new BadRequestError('Duplicated extra permissions are not allowed.')
    }
    //Check valid group permissions
    const validPermissions = await PermissionRepo.count({
      _id: { $in: extraPermissions },
    })

    if (validPermissions !== extraPermissions.length) {
      throw new BadRequestError('Please verify extra permissions !')
    }
    // check if extra permissions exists in user group permissons
    let allGroupPermissions = user.permissionGroup.flatMap((group) =>
      group.permissions.map((permission) => permission._id)
    )
    const allGroupPermissionsSet = new Set(allGroupPermissions)

    const matchedElements = extraPermissions.filter((permission) =>
      allGroupPermissionsSet.has(permission)
    )

    if (matchedElements.length > 0) {
      throw new BadRequestError('Some permissions are already granted')
    }
  }

  if (office) {
    const foundOffice = await OfficeRepo.findOne({ _id: office })
    if (!foundOffice) {
      throw new NotFoundError('Office Not Found')
    }
  }

  const updatedUser = await UserRepo.update(userId, req.body)

  if (!updatedUser) {
    throw new NotFoundError('User Not Found')
  }

  return new SuccessResponse('User successfully updated', updatedUser).send(res)
})
