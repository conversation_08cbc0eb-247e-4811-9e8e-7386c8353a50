import { Request, Response } from "express";
import { SuccessMsgResponse, SuccessResponse } from "../helpers/ApiResponse";
import asyncHandler from "../middlewares/asyncHandler";
import LevelService from "../services/levelService";

// create
export const create = asyncHandler(async (req: Request, res: Response) => {
  const level = await LevelService.create(req.body);
  return new SuccessResponse("Level created successfully", level).send(res);
});

// get one
export const getOne = asyncHandler(async (req: Request, res: Response) => {
  const { levelId } = req.params;

  const foundLevel = await LevelService.findOne({
    _id: levelId,
  });

  return new SuccessResponse("Level returned successfully", foundLevel).send(
    res
  );
});

//Get all
export const getAll = asyncHandler(async (req: Request, res: Response) => {
  const levels = await LevelService.find(req.query);
  new SuccessResponse("Levels returned successfully", levels).send(res);
});

// Update one
export const updateOne = asyncHandler(async (req: Request, res: Response) => {
  const { levelId } = req.params;

  const updatedLevel = await LevelService.findOneAndUpdate(
    { _id: levelId },
    req.body
  );

  return new SuccessResponse("Level updated successfully", {
    updatedLevel,
  }).send(res);
});

// Delete one
export const deleteOne = asyncHandler(async (req: Request, res: Response) => {
  const { levelId } = req.params;

  await LevelService.deleteOne({ _id: levelId });

  return new SuccessMsgResponse("Level deleted successfully").send(res);
});
