import { Request, Response } from "express";
import SectionRepo from "../db/repositories/SectionRepo";
import { SuccessMsgResponse, SuccessResponse } from "../helpers/ApiResponse";
import asyncHandler from "../middlewares/asyncHandler";
import SectionService from "../services/sectionService";

// create
export const create = asyncHandler(async (req: Request, res: Response) => {
  const section = await SectionService.create(req.body);
  return new SuccessResponse("Section created successfully", section).send(res);
});

// get one
export const getOne = asyncHandler(async (req: Request, res: Response) => {
  const { sectionId } = req.params;

  const foundSection = await SectionService.findOne({
    _id: sectionId,
  });

  return new SuccessResponse(
    "Section returned successfully",
    foundSection
  ).send(res);
});

//Get all
export const getAll = asyncHandler(async (req: Request, res: Response) => {
  const sections = await SectionService.find(req.query);
  new SuccessResponse("Permissions returned successfully", sections).send(res);
});

// Update one
export const updateOne = asyncHandler(async (req: Request, res: Response) => {
  const { sectionId } = req.params;

  const updatedSection = await SectionService.findOneAndUpdate(
    { _id: sectionId },
    req.body
  );

  return new SuccessResponse("Section updated successfully", {
    updatedSection,
  }).send(res);
});

// Delete one
export const deleteOne = asyncHandler(async (req: Request, res: Response) => {
  const { sectionId } = req.params;

  await SectionService.deleteOne({ _id: sectionId });

  return new SuccessMsgResponse("Section deleted successfully").send(res);
});
