import { Request, Response } from 'express'
import DailyKpiRepo from '../db/repositories/DailyKpiRepo'
import DailyQuestionRepo from '../db/repositories/DailyQuestionRepo'
import { BadRequestError, NotFoundError } from '../helpers/ApiError'
import { SuccessMsgResponse, SuccessResponse } from '../helpers/ApiResponse'
import asyncHandler from '../middlewares/asyncHandler'

//Create DailyQuestion
export const create = asyncHandler(async (req: Request, res: Response) => {
  const { name, dailyKpis } = req.body
  //Check duplicated kpis
  if (dailyKpis) {
    const hasDuplicates = new Set(dailyKpis).size !== dailyKpis.length
    if (hasDuplicates) {
      throw new BadRequestError('Duplicate kpis are not allowed.')
    }
    //Check valid kpis
    const validKpis = await DailyKpiRepo.count({
      _id: { $in: dailyKpis },
    })

    if (validKpis !== dailyKpis.length) {
      throw new BadRequestError('Please verify kpis !')
    }
  }
  const newDailyQuestion = await DailyQuestionRepo.create(req.body)

  return new SuccessResponse(
    'Daily Question created successfully',
    newDailyQuestion
  ).send(res)
})

//Get all Daily Questions
export const getAll = asyncHandler(async (req: Request, res: Response) => {
  const dailyQuestions = (await DailyQuestionRepo.find(
    {},
    req.query
  )) as unknown as PaginationModel

  const { docs, ...meta } = dailyQuestions
  new SuccessResponse('Daily Questions returned successfully', {
    docs,
    meta,
  }).send(res)
})

//Get one Daily Question
export const getOne = asyncHandler(async (req: Request, res: Response) => {
  const { dailyQuestionId } = req.params
  const dailyQuestion = await DailyQuestionRepo.findOne({
    _id: dailyQuestionId,
  })
  //Check if Daily Question exist
  if (!dailyQuestion) {
    throw new NotFoundError('Daily Question not found !')
  }

  return new SuccessResponse(
    'Daily Question returned successfully',
    dailyQuestion
  ).send(res)
})

//Update one
export const update = asyncHandler(async (req: Request, res: Response) => {
  const { dailyQuestionId } = req.params

  //Update
  const updatedDailyQuestion = await DailyQuestionRepo.findOneAndUpdate(
    {
      _id: dailyQuestionId,
    },
    { ...req.body }
  )

  //Check if Daily Question exist
  if (!updatedDailyQuestion) {
    throw new NotFoundError('Daily Question not found !')
  }

  return new SuccessResponse(
    'Daily Question response successfully updated',
    updatedDailyQuestion
  ).send(res)
})

//Delete one
export const deleteOne = asyncHandler(async (req: Request, res: Response) => {
  const { dailyQuestionId } = req.params

  //Check Daily Question
  const dailyQuestion = await DailyQuestionRepo.findOne({
    _id: dailyQuestionId,
  })
  if (!dailyQuestion) {
    throw new NotFoundError('Daily Question Not Found !')
  }

  await dailyQuestion.delete()
  return new SuccessMsgResponse(
    'Daily Question Response successfully deleted'
  ).send(res)
})

// Delete many
export const deleteMany = asyncHandler(async (req: Request, res: Response) => {
  const { dailyQuestionIds } = req.body

  // Check Daily Questions existence
  const dailyQuestionsLength = await DailyQuestionRepo.count({
    _id: { $in: dailyQuestionIds },
  })

  if (dailyQuestionsLength !== dailyQuestionIds.length) {
    throw new NotFoundError('One or more Daily Questions not found')
  }

  // Delete Daily Questions
  await DailyQuestionRepo.updateMany(
    {
      _id: { $in: dailyQuestionIds },
    },
    {
      deleted: true,
      deletedAt: Date.now(),
    }
  )

  return new SuccessMsgResponse('Daily Questions successfully deleted').send(res)
})