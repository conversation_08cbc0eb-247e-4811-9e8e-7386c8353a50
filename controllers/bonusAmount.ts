import { Request, Response } from "express";
import BonusAmountRepo from "../db/repositories/BonusAmountRepo";
import { BadRequestError, NotFoundError } from "../helpers/ApiError";
import { SuccessMsgResponse, SuccessResponse } from "../helpers/ApiResponse";
import asyncHandler from "../middlewares/asyncHandler";

export const createBonusAmount = asyncHandler(
  async (req: Request, res: Response) => {
    const { startDate, endDate } = req.body;

    // Validate if there are any overlapping worktime periods
    const overlappedBonusAmount = await BonusAmountRepo.find({
      $and: [
        {
          startDate: { $lt: new Date(endDate) },
        },
        {
          endDate: { $gt: new Date(startDate) },
        },
      ],
    });

    if (overlappedBonusAmount.length > 0) {
      throw new BadRequestError(
        "Cannot create bonus amount. Overlapping bonus amount periods found."
      );
    }

    // create
    const bonusAmount = await BonusAmountRepo.create(req.body);

    return new SuccessResponse(
      "Bonus Amount created successfully",
      bonusAmount
    ).send(res);
  }
);

export const getAll = asyncHandler(async (req: Request, res: Response) => {
  const bonusAmounts = await BonusAmountRepo.findWithPagination({}, req.query);

  const { docs, ...meta } = bonusAmounts;

  return new SuccessResponse("All bonus amounts are returned successfully", {
    docs,
    meta,
  }).send(res);
});

export const getOne = asyncHandler(async (req: Request, res: Response) => {
  const { bonusAmountId } = req.params;

  const bonusAmount = await BonusAmountRepo.findOne({ _id: bonusAmountId });

  if (!bonusAmount) {
    throw new NotFoundError("Bonus amount not found");
  }

  return new SuccessResponse(
    "All bonus amounts are returned successfully",
    bonusAmount
  ).send(res);
});

export const updateOne = asyncHandler(async (req: Request, res: Response) => {
  const { bonusAmountId } = req.params;
  const { startDate, endDate } = req.body;

  const foundBonusAmount = await BonusAmountRepo.findOne({
    _id: bonusAmountId,
  });

  if (!foundBonusAmount) {
    throw new NotFoundError("Bonus amount not found");
  }

  // Validate if there are any overlapping worktime periods
  if (startDate && endDate) {
    const overlappedBonusAmount = await BonusAmountRepo.find({
      _id: { $ne: bonusAmountId },
      $and: [
        {
          startDate: { $lt: new Date(endDate) },
        },
        {
          endDate: { $gt: new Date(startDate) },
        },
      ],
    });

    if (overlappedBonusAmount.length > 0) {
      throw new BadRequestError(
        "Cannot update bonus amount. Overlapping bonus amount periods found."
      );
    }
  }

  // update
  const bonusAmount = await BonusAmountRepo.update(
    { _id: bonusAmountId },
    req.body
  );

  return new SuccessResponse(
    "Bonus Amount updated successfully",
    bonusAmount
  ).send(res);
});

export const deleteOne = asyncHandler(async (req: Request, res: Response) => {
  const { bonusAmountId } = req.params;

  const bonusAmount = await BonusAmountRepo.findOne({
    _id: bonusAmountId,
  });

  if (!bonusAmount) {
    throw new NotFoundError("Bonus Amount Not Found !");
  }

  await bonusAmount.delete();
  return new SuccessMsgResponse("Bonus Amount successfully deleted").send(res);
});

export const deleteMany = asyncHandler(async (req: Request, res: Response) => {
  const { bonusAmountIds } = req.body;

  const bonusAmountsLength = await BonusAmountRepo.count({
    _id: { $in: bonusAmountIds },
  });

  if (bonusAmountsLength !== bonusAmountIds.length) {
    throw new NotFoundError("One or more bonus Amount not found");
  }

  await BonusAmountRepo.updateMany(
    {
      _id: { $in: bonusAmountIds },
    },
    {
      deleted: true,
      deletedAt: Date.now(),
    }
  );

  return new SuccessMsgResponse("Bonus amounts successfully deleted").send(res);
});
