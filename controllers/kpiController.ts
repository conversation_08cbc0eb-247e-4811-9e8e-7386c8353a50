import { Request, Response } from 'express'
import KpiRepo from '../db/repositories/KpiRepo'
import { NotFoundError } from '../helpers/ApiError'
import { SuccessMsgResponse, SuccessResponse } from '../helpers/ApiResponse'
import asyncHandler from '../middlewares/asyncHandler'

// create
export const create = asyncHandler(async (req: Request, res: Response) => {
  const newKpi = await KpiRepo.create(req.body)
  return new SuccessResponse('Kpi created successfully', newKpi).send(res)
})
// get all
export const getAll = asyncHandler(async (req: Request, res: Response) => {
  const kpis = await KpiRepo.findWithPagination({}, req.query)
  const { docs, ...meta } = kpis
  return new SuccessResponse('All Kpis are returned successfully', {
    docs,
    meta,
  }).send(res)
})

// get one
export const getOne = asyncHandler(async (req: Request, res: Response) => {
  const { kpiId } = req.params
  const foundKpi = await KpiRepo.findOne({ _id: kpiId })
  if (!foundKpi) {
    throw new NotFoundError('Kpi not found !')
  }
  return new SuccessResponse('Kpi returned successfully', foundKpi).send(res)
})

// update one
export const updateKpi = asyncHandler(async (req: Request, res: Response) => {
  const { kpiId } = req.params
  const updatedKpi = await KpiRepo.update(kpiId, req.body)
  if (!updatedKpi) {
    throw new NotFoundError('Kpi not found !')
  }
  return new SuccessResponse('Kpi updated successfully', {
    updatedKpi,
  }).send(res)
})

//Delete ONE
export const deleteOne = asyncHandler(async (req: Request, res: Response) => {
  const { kpiId } = req.params

  //Check Kpi
  const kpi = await KpiRepo.findOne({
    _id: kpiId,
  })
  if (!kpi) {
    throw new NotFoundError('Kpi Not Found !')
  }
  const deletedKpis = await KpiRepo.count({
    deleted: true,
  })
  await KpiRepo.deleteOne(
    { _id: kpi._id },
    {
      $set: {
        name: `old${deletedKpis}${kpi.name}`,
        deletedAt: new Date(),
        deleted: true,
      },
    }
  )
  return new SuccessMsgResponse('Kpi deleted successfully').send(res)
})

//Delete MANY
export const deleteMany = asyncHandler(async (req: Request, res: Response) => {
  const { data } = req.body
  const now = Date.now()
  await KpiRepo.updateMany({ _id: { $in: data } }, [
    {
      $set: {
        deleted: true,
        deletedAt: now,
        name: {
          $concat: ['$name', now.toString()],
        },
      },
    },
  ])
  return new SuccessMsgResponse('Kpis successfully deleted').send(res)
})
