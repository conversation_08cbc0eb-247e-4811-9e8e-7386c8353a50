import { endOfDay, startOfDay } from "date-fns";
import { Request, Response } from "express";
import { Types } from "mongoose";
import { CashDeskPaymentHistoryAction } from "../db/models/CashDeskPaymentHistory";
import { RoleCode } from "../db/models/User";
import CashDeskPaymentHistoryRepo from "../db/repositories/CashDeskPaymentHistoryRepo";
import CashDeskPaymentRepo from "../db/repositories/CashDeskPaymentRepo";
import { BadRequestError, NotFoundError } from "../helpers/ApiError";
import { SuccessMsgResponse, SuccessResponse } from "../helpers/ApiResponse";
import asyncHandler from "../middlewares/asyncHandler";
import calculationService from "../services/calculationService";
import { pdfGenerator } from "../utils/PdfUtils/pdfGenerator";

// Create cash desk payment
export const create = asyncHandler(async (req: Request, res: Response) => {
  const { amount, office } = req.body;
  let convertedOffice;

  // Super Admin check
  if (req.user?.role === RoleCode.SUPER_ADMIN) {
    if (office) {
      convertedOffice = office;
    } else {
      throw new BadRequestError("please tell us office !");
    }
  }

  // !Super Admin check
  if (req.user?.role !== RoleCode.SUPER_ADMIN) {
    if (office) {
      throw new BadRequestError("you are not allowed to select an office");
    } else if (req.user?.office?._id) {
      convertedOffice = req.user?.office?._id;
    } else {
      throw new BadRequestError("you are not allowed !");
    }
  }

  // check amount & balance
  const balance = await calculationService.totalBalance(
    new Types.ObjectId(convertedOffice)
  );

  if (balance - amount < 0) {
    throw new BadRequestError("insufficient balance");
  }

  const cashDeskPayment = await CashDeskPaymentRepo.create({
    ...req.body,
    office: convertedOffice,
    cashedBy: req.user?._id,
  });

  // Cash Desk Payment History
  CashDeskPaymentHistoryRepo.create({
    ...req.body,
    cashDeskPayment: cashDeskPayment._id,
    office: cashDeskPayment?.office,
    cashedBy: cashDeskPayment?.cashedBy,
    action: CashDeskPaymentHistoryAction.CREATE,
    createdBy: req.user?._id,
  });

  return new SuccessResponse(
    "Cash desk payment created successfully",
    cashDeskPayment
  ).send(res);
});

//Get all cash desk payments
export const getAll = asyncHandler(async (req: Request, res: Response) => {
  const { role, office } = req.user;
  const { startDate, endDate } = req.query;
  let filter: any = {};

  /* Super admins and admins without an office can view all cash desk payments,
  while admins with an office can only see cash desk payments within their respective offices. */
  if (role !== RoleCode.SUPER_ADMIN && office) {
    if (req.query.office) {
      throw new BadRequestError("You are not allowed to select an office");
    }
    filter.office = req.user?.office?._id;
  }

  // Filter By Date
  if (startDate && endDate) {
    filter.createdAt = {
      $gte: startOfDay(startDate as string),
      $lte: endOfDay(endDate as string),
    };
  }

  // Fetch cash desk payments based on the filter
  const cashDeskPayments = (await CashDeskPaymentRepo.findWithPagination(
    filter,
    req.query
  )) as unknown as PaginationModel;

  const { docs, ...meta } = cashDeskPayments;

  // Send a success response
  new SuccessResponse("Cash desk payments returned successfully", {
    docs,
    meta,
  }).send(res);
});

//Get one cash desk payment
export const getOne = asyncHandler(async (req: Request, res: Response) => {
  const { cashDeskPaymentId } = req.params;
  const { role, office } = req.user;
  let filter: any = { _id: cashDeskPaymentId };

  /* Super admins and admins without an office can view all cash desk payments,
  while admins with an office can only see cash desk payments within their respective offices. */
  if (role !== RoleCode.SUPER_ADMIN && office) {
    filter.office = office?._id;
  }

  const cashDeskPayment = await CashDeskPaymentRepo.findOne(filter);

  //Check if cash desk payment exist
  if (!cashDeskPayment) {
    throw new NotFoundError("Cash Desk Payment Not Found !");
  }

  return new SuccessResponse(
    "Cash Desk Payment returned successfully",
    cashDeskPayment
  ).send(res);
});

// Update cash desk payment
export const update = asyncHandler(async (req: Request, res: Response) => {
  const { amount, office } = req.body;
  const { cashDeskPaymentId } = req.params;
  let startDate = new Date(null);

  // Get the cash desk payment
  const cashDeskPayment = await CashDeskPaymentRepo.findOne({
    _id: cashDeskPaymentId,
  });

  if (!cashDeskPayment) {
    throw new NotFoundError("Cash Desk Payment Not Found !");
  }

  // Dont belong to the office
  if (
    req.user?.role !== RoleCode.SUPER_ADMIN &&
    req.user?.office?._id.equals(cashDeskPayment.office?._id)
  ) {
    throw new BadRequestError(
      "you are not allowed to edit this cash desk payment !"
    );
  }

  // check fileds to update
  if (req.user?.role !== RoleCode.SUPER_ADMIN && office) {
    throw new BadRequestError("you are not allowed to select an office !");
  }

  // check amount & balance
  const balance = await calculationService.totalBalance(
    new Types.ObjectId(cashDeskPayment.office?._id),
    startDate,
    cashDeskPayment?.createdAt
  );

  const oldAmount = cashDeskPayment.amount;

  if (amount > oldAmount && balance - (amount - oldAmount) < 0) {
    throw new BadRequestError("Insufficient balance");
  }

  // update
  const updatedCashDeskPayment = await CashDeskPaymentRepo.findOneAndUpdate(
    { _id: cashDeskPayment },
    req.body
  );

  // Cash Desk Payment History
  CashDeskPaymentHistoryRepo.create({
    cashDeskPayment: updatedCashDeskPayment,
    action: CashDeskPaymentHistoryAction.UPDATE,
    office: updatedCashDeskPayment?.office,
    cashedBy: updatedCashDeskPayment?.cashedBy,
    cashedTo: updatedCashDeskPayment?.cashedTo,
    amount: updatedCashDeskPayment?.amount,
    notes: updatedCashDeskPayment?.notes,
    createdBy: req.user?._id,
  });

  return new SuccessResponse(
    "Cash desk payment updated successfully",
    updatedCashDeskPayment
  ).send(res);
});

// Delete cash desk payment
export const deleteOne = asyncHandler(async (req: Request, res: Response) => {
  const { cashDeskPaymentId } = req.params;
  const { role, office } = req.user;

  let filter: Object = { _id: cashDeskPaymentId };

  // Super admins can delete all operations, while admins can only delete codes within their respective offices.
  if (role !== RoleCode.SUPER_ADMIN) {
    filter = { ...filter, office: office._id };
  }

  const cashDeskPayment = await CashDeskPaymentRepo.findOne(filter);

  if (!cashDeskPayment) {
    throw new BadRequestError("Cash Desk Payment Not Found !");
  }

  await cashDeskPayment.delete();

  // Cash Desk Payment History
  CashDeskPaymentHistoryRepo.create({
    cashDeskPayment: cashDeskPayment,
    action: CashDeskPaymentHistoryAction.DELETE,
    createdBy: req.user?._id,
  });

  return new SuccessMsgResponse("Cash Desk Payment deleted successfully").send(
    res
  );
});

// fill cash desk payment pdf
export const generateCashDeskPaymentPDF = asyncHandler(
  async (req: Request, res: Response) => {
    const { content } = req.body;

    const outputPath = "financialReceipt.pdf";
    const pdfBuffer = await pdfGenerator(content, "financialReceipt.pdf");

    res.set({
      "Content-Type": "application/pdf",
      "Content-Disposition": `attachment; filename=${outputPath}"`,
      "Content-Length": pdfBuffer?.length,
    });

    res.send(pdfBuffer);
  }
);
