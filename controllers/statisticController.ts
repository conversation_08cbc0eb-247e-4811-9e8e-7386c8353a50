import {
  addDays,
  addMonths,
  endOfDay,
  endOfMonth,
  endOfYear,
  format,
  isSameMonth,
  isSameYear,
  parse,
  startOfDay,
  startOfMonth,
  startOfToday,
  startOfYear,
} from "date-fns";
import { Request, Response } from "express";
import { ObjectId, Types } from "mongoose";
import { PaymentMethod } from "../db/models/Code";
import { OperationType } from "../db/models/Operation";
import { RoleCode } from "../db/models/User";
import CodeRepo from "../db/repositories/CodeRepo";
import KpiRepo from "../db/repositories/KpiRepo";
import OfficeRepo from "../db/repositories/OfficeRepo";
import StatClientRepo from "../db/repositories/StatClientRepo";
import StatClientResponseRepo from "../db/repositories/StatClientResponseRepo";
import UserRepo from "../db/repositories/UserRepo";
import { BadRequestError } from "../helpers/ApiError";
import { SuccessResponse } from "../helpers/ApiResponse";
import asyncHandler from "../middlewares/asyncHandler";
import calculationService from "../services/calculationService";
import cashDeskPaymentService from "../services/cashDeskPaymentService";
import codeService from "../services/codeService";
import operationService from "../services/operationService";
import { client } from "../utils/redis";
//Get Statistics Numbers
export const getNumbers = asyncHandler(async (req: Request, res: Response) => {
  const { startDate, endDate } = req.query;
  //Filter by date
  const dateMatch = {
    $expr: {
      $and: [
        {
          $gte: [
            { $dateToString: { format: "%Y-%m-%d", date: "$createdAt" } },
            startDate || "1970-01-01", // Default to the beginning of time
          ],
        },
        {
          $lte: [
            { $dateToString: { format: "%Y-%m-%d", date: "$createdAt" } },
            endDate || new Date(), // Default to the current date
          ],
        },
      ],
    },
  };

  const nbStatClients = await StatClientRepo.count(dateMatch);
  const nbStatClientResponses = await StatClientResponseRepo.count(dateMatch);
  const nbEmployees = await UserRepo.count(dateMatch);
  const nbOffices = await OfficeRepo.count(dateMatch);
  const nbKpis = await KpiRepo.count(dateMatch);

  new SuccessResponse("Statistics numbers returned successfully", {
    nbStatClients,
    nbStatClientResponses,
    nbEmployees,
    nbOffices,
    nbKpis,
  }).send(res);
});

//Get Nb Stat Client Responses By Office
export const getNbStatClientResponsesByOffice = asyncHandler(
  async (req: Request, res: Response) => {
    const { startDate, endDate } = req.query;
    //Filter by date
    const matchStage = {
      $match: {
        $expr: {
          $and: [
            {
              $gte: [
                { $dateToString: { format: "%Y-%m-%d", date: "$createdAt" } },
                startDate || "1970-01-01", // Default to the beginning of time
              ],
            },
            {
              $lte: [
                { $dateToString: { format: "%Y-%m-%d", date: "$createdAt" } },
                endDate || new Date(), // Default to the current date
              ],
            },
          ],
        },
      },
    };

    const nbStatClientResponsesByOffice =
      await StatClientResponseRepo.aggregate([
        matchStage,
        { $sort: { createdAt: 1 } },
        {
          $lookup: {
            from: "users",
            localField: "admin",
            foreignField: "_id",
            as: "admin",
          },
        },
        {
          $unwind: {
            path: "$admin",
            preserveNullAndEmptyArrays: false,
          },
        },
        {
          $lookup: {
            from: "offices",
            localField: "admin.office",
            foreignField: "_id",
            as: "office",
          },
        },
        {
          $unwind: {
            path: "$office",
            preserveNullAndEmptyArrays: false,
          },
        },
        {
          $group: {
            _id: "$office",
            nbStatClientResponses: {
              $sum: 1,
            },
          },
        },
        {
          $project: {
            _id: 0,
            office: "$_id",
            nbStatClientResponses: 1,
          },
        },
      ]);

    new SuccessResponse(
      "Nb Stat Client Responses by Office returned successfully",
      {
        nbStatClientResponsesByOffice,
      }
    ).send(res);
  }
);

//Get Balance statistics
export const getBalanceAnalytics = asyncHandler(
  async (req: Request, res: Response) => {
    let { startDate, endDate, office } = req.query;
    let convertedStartedDate: Date = undefined;
    let convertedEndDate: Date = undefined;
    let convertedOffice = undefined;

    //Super admin can select any office, admin can select only her office
    if (req.user?.role !== RoleCode.SUPER_ADMIN && req.user?.office && office) {
      throw new BadRequestError("You can't select an office !");
    } else if (office) {
      convertedOffice = new Types.ObjectId(office as string);
    } else {
      convertedOffice = req.user?.office?._id;
    }

    if (startDate && endDate) {
      convertedStartedDate = startOfDay(startDate.toString());
      convertedEndDate = endOfDay(endDate.toString());
    }

    const [
      totalCashCodes,
      totalCheckCodes,
      checksNumber,
      totalCredit,
      totalDeposit,
      totalCashDeskCurrent,
      totalCashDeskAll,
      totalBalance,
      totalIncomes,
      totalNotPayedCodes,
      totalNotPayedCodesAll,
      totalNotPayedChecksCodes,
      notPayedChecksNumber,
    ] = await Promise.all([
      codeService.totalCodesBalance(
        convertedOffice,
        convertedStartedDate,
        convertedEndDate
      ),
      codeService.totalCodesBalance(
        convertedOffice,
        new Date(0),
        convertedEndDate,
        PaymentMethod.CHECK,
        false,
        true,
        false
      ),
      codeService.checksNumber(
        convertedOffice,
        new Date(0),
        convertedEndDate,
        true,
        false
      ),
      operationService.totalOperations(
        OperationType.CREDIT,
        convertedOffice,
        convertedStartedDate,
        convertedEndDate
      ),
      operationService.totalOperations(
        OperationType.DEPOSIT,
        convertedOffice,
        convertedStartedDate,
        convertedEndDate
      ),
      cashDeskPaymentService.totalCashDeskPayments(
        convertedOffice,
        convertedStartedDate,
        convertedEndDate
      ),
      cashDeskPaymentService.totalCashDeskPayments(
        convertedOffice,
        new Date(0),
        convertedEndDate
      ),
      calculationService.totalBalance(
        convertedOffice,
        new Date(0),
        convertedEndDate
      ),
      calculationService.totalIncomes(
        convertedOffice,
        convertedStartedDate,
        convertedEndDate
      ),
      codeService.totalCodesBalance(
        convertedOffice,
        convertedStartedDate,
        convertedEndDate,
        PaymentMethod.CASH,
        false
      ),
      codeService.totalCodesBalance(
        convertedOffice,
        new Date(0),
        convertedEndDate,
        PaymentMethod.CASH,
        false
      ),
      codeService.totalCodesBalance(
        convertedOffice,
        new Date(0),
        convertedEndDate,
        PaymentMethod.CHECK,
        true,
        false,
        false
      ),
      codeService.checksNumber(
        convertedOffice,
        new Date(0),
        convertedEndDate,
        false,
        false
      ),
    ]);

    new SuccessResponse("Balance analytics returned successfully", {
      totalCashCodes,
      totalCheckCodes,
      checksNumber,
      totalDeposit,
      totalCredit,
      totalCashDeskCurrent,
      totalCashDeskAll,
      totalBalance,
      totalIncomes,
      totalNotPayedCodes,
      totalNotPayedCodesAll,
      totalNotPayedChecksCodes,
      notPayedChecksNumber,
    }).send(res);
  }
);

//Get codes statistics
export const getCodesAnalytics = asyncHandler(
  async (req: Request, res: Response) => {
    let { startDate, endDate, office } = req.query;
    let convertedStartedDate: Date = undefined;
    let convertedEndDate: Date = undefined;
    let convertedOffice = undefined;

    //Super admin can select any office, admin can select only her office
    if (req.user?.role !== RoleCode.SUPER_ADMIN && req.user?.office && office) {
      throw new BadRequestError("You can't select an office !");
    } else if (office) {
      convertedOffice = new Types.ObjectId(office as string);
    } else {
      convertedOffice = req.user?.office?._id;
    }

    if (startDate && endDate) {
      convertedStartedDate = startOfDay(startDate.toString());
      convertedEndDate = endOfDay(endDate.toString());
    }

    //Fetch Data
    const [
      totalCashCodes,
      totalCheckCodes,
      checksNumber,
      totalBalance,
      totalIncomes,
      totalNotPayedCodes,
      totalNotPayedCodesAll,
      totalNotPayedChecksCodes,
      notPayedChecksNumber,
      billsOfExchangeNumber,
      billsOfExchangeIncomes,
    ] = await Promise.all([
      codeService.totalCodesBalance(
        convertedOffice,
        convertedStartedDate,
        convertedEndDate
      ),
      codeService.totalCodesBalance(
        convertedOffice,
        new Date(0),
        convertedEndDate,
        PaymentMethod.CHECK,
        false,
        true,
        false
      ),
      codeService.checksNumber(
        convertedOffice,
        new Date(0),
        convertedEndDate,
        true,
        false
      ),
      calculationService.totalBalance(
        convertedOffice,
        new Date(0),
        convertedEndDate
      ),
      calculationService.totalIncomes(
        convertedOffice,
        convertedStartedDate,
        convertedEndDate
      ),
      codeService.totalCodesBalance(
        convertedOffice,
        convertedStartedDate,
        convertedEndDate,
        PaymentMethod.CASH,
        false
      ),
      codeService.totalCodesBalance(
        convertedOffice,
        new Date(0),
        convertedEndDate,
        PaymentMethod.CASH,
        false
      ),
      codeService.totalCodesBalance(
        convertedOffice,
        new Date(0),
        convertedEndDate,
        PaymentMethod.CHECK,
        true,
        false,
        false
      ),
      codeService.checksNumber(
        convertedOffice,
        new Date(0),
        convertedEndDate,
        false,
        false
      ),
      codeService.billOfExchangeNumber(
        convertedOffice,
        convertedStartedDate,
        convertedEndDate
      ),
      codeService.totalCodesBalance(
        convertedOffice,
        convertedStartedDate,
        convertedEndDate,
        PaymentMethod.BILL_OF_EXCHANGE
      ),
    ]);

    new SuccessResponse("Codes analytics returned successfully", {
      totalCashCodes,
      totalCheckCodes,
      checksNumber,
      totalBalance,
      totalIncomes,
      totalNotPayedCodes,
      totalNotPayedCodesAll,
      totalNotPayedChecksCodes,
      notPayedChecksNumber,
      billsOfExchangeNumber,
      billsOfExchangeIncomes,
    }).send(res);
  }
);

//Get expenses statistics
export const getExpensesAnalytics = asyncHandler(
  async (req: Request, res: Response) => {
    let { startDate, endDate, office } = req.query;
    let convertedStartedDate: Date = undefined;
    let convertedEndDate: Date = undefined;
    let convertedOffice = undefined;

    //Super admin can select any office, admin can select only her office
    if (req.user?.role !== RoleCode.SUPER_ADMIN && req.user?.office && office) {
      throw new BadRequestError("You can't select an office !");
    } else if (office) {
      convertedOffice = new Types.ObjectId(office as string);
    } else {
      convertedOffice = req.user?.office?._id;
    }

    if (startDate && endDate) {
      convertedStartedDate = startOfDay(startDate.toString());
      convertedEndDate = endOfDay(endDate.toString());
    }

    const [totalDeposit, totalCredit] = await Promise.all([
      operationService.totalOperations(
        OperationType.DEPOSIT,
        convertedOffice,
        convertedStartedDate,
        convertedEndDate
      ),
      operationService.totalOperations(
        OperationType.CREDIT,
        convertedOffice,
        convertedStartedDate,
        convertedEndDate
      ),
    ]);

    new SuccessResponse("Expenses analytics returned successfully", {
      totalDeposit,
      totalCredit,
    }).send(res);
  }
);

//Get Balance statistics
export const getCashDeskAnalytics = asyncHandler(
  async (req: Request, res: Response) => {
    let { startDate, endDate, office } = req.query;
    let convertedStartedDate: Date = undefined;
    let convertedEndDate: Date = undefined;
    let convertedOffice = undefined;

    //Super admin can select any office, admin can select only her office
    if (req.user?.role !== RoleCode.SUPER_ADMIN && req.user?.office && office) {
      throw new BadRequestError("You can't select an office !");
    } else if (office) {
      convertedOffice = new Types.ObjectId(office as string);
    } else {
      convertedOffice = req.user?.office?._id;
    }

    if (startDate && endDate) {
      convertedStartedDate = startOfDay(startDate.toString());
      convertedEndDate = endOfDay(endDate.toString());
    }

    const [totalCashDeskCurrent, totalCashDeskAll] = await Promise.all([
      cashDeskPaymentService.totalCashDeskPayments(
        convertedOffice,
        convertedStartedDate,
        convertedEndDate
      ),
      cashDeskPaymentService.totalCashDeskPayments(
        convertedOffice,
        new Date(0),
        convertedEndDate
      ),
    ]);

    new SuccessResponse("Cash Desk analytics returned successfully", {
      totalCashDeskCurrent,
      totalCashDeskAll,
    }).send(res);
  }
);

export const getBalanceAnalyticsPerMonth = asyncHandler(
  async (req: Request, res: Response) => {
    let { month, sort } = req.query;
    let year = parseInt(req.query.year as string);

    const currentYear = new Date().getFullYear();
    const currentMonth = new Date().getMonth();

    if (year > currentYear) {
      throw new BadRequestError(
        "Invalid year, The year cannot be greater than the current year"
      );
    }

    let startMonth = !month
      ? year === 2024
        ? 3
        : 0
      : parseInt(month as string); // Start from April 2024 or January
    let endMonth = !month
      ? year === currentYear
        ? currentMonth + 1
        : 12
      : startMonth + 1; // End at the current month or December

    let yearBalanceAnalytics = [];

    for (let month = startMonth; month < endMonth; month++) {
      const startDate = new Date(Date.UTC(year, month, 1));
      const endDate = new Date(Date.UTC(year, month + 1, 0, 23, 59, 59, 999));

      const isCurrentMonth = currentMonth === month;

      // Attempt to fetch from Redis cache
      const cacheKey = `balance-per-month-${month}-${year}`;
      const cachedResponse = await client.get(cacheKey);

      if (cachedResponse && !isCurrentMonth) {
        yearBalanceAnalytics.push({
          month: startDate.toLocaleString("default", { month: "long" }),
          data: JSON.parse(cachedResponse),
        });
      } else {
        // Fetch offices and process balance data
        const officesQuery = {
          sort: sort === "officeId" || sort === "-officeId" ? sort : "officeId",
        };
        const officesResponse = await OfficeRepo.findWithPagination(
          {},
          officesQuery
        );
        const offices = officesResponse.docs;

        const monthBalanceAnalytics = await Promise.all(
          offices.map(async (office) => {
            const totalCodes = await codeService.totalCodesBalance(
              office._id,
              startDate,
              endDate
            );
            const totalCredit = await operationService.totalOperations(
              OperationType.CREDIT,
              office._id,
              startDate,
              endDate
            );
            const totalDeposit = await operationService.totalOperations(
              OperationType.DEPOSIT,
              office._id,
              startDate,
              endDate
            );
            const totalCashDesk =
              await cashDeskPaymentService.totalCashDeskPayments(
                office._id,
                startDate,
                endDate
              );
            const totalBalance = await calculationService.totalBalance(
              office._id,
              new Date(0),
              endDate
            );

            return {
              office: office.name,
              totalCodes,
              totalDeposit,
              totalCredit,
              totalCashDesk,
              totalBalance,
            };
          })
        );

        // Cache the result
        await client.set(cacheKey, JSON.stringify(monthBalanceAnalytics));

        yearBalanceAnalytics.push({
          month: startDate.toLocaleString("default", { month: "long" }),
          data: monthBalanceAnalytics,
        });
      }
    }

    new SuccessResponse(
      "Balance analytics per month returned successfully",
      yearBalanceAnalytics
    ).send(res);
  }
);

//Get Balance statistics Per Month
export const getBalanceAnalyticsPerMonthPerOffice = asyncHandler(
  async (req: Request, res: Response) => {
    let year = parseInt(req.query.year as string);

    const currentYear = new Date().getFullYear();
    const currentMonth = new Date().getMonth();

    if (year > currentYear) {
      throw new BadRequestError(
        "Invalid year, The year cannot be greater than the current year"
      );
    }

    let startMonth = year === 2024 ? 3 : 0; // Start from April for 2024, else January
    let endMonth = year === currentYear ? currentMonth + 1 : 12; // End with current month for current year, else December

    let yearBalanceAnalytics = [];

    let office = req.user?.office;

    if (!office) {
      throw new BadRequestError("Only for users with office !");
    }

    for (let month = startMonth; month < endMonth; month++) {
      const startDate = new Date(Date.UTC(year, month, 1));
      const endDate = new Date(Date.UTC(year, month + 1, 0, 23, 59, 59, 999));

      let monthBalanceAnalytics = [];

      const totalCodes = await codeService.totalCodesBalance(
        office._id,
        startDate,
        endDate
      );
      const totalCredit = await operationService.totalOperations(
        OperationType.CREDIT,
        office._id,
        startDate,
        endDate
      );
      const totalDeposit = await operationService.totalOperations(
        OperationType.DEPOSIT,
        office._id,
        startDate,
        endDate
      );
      const totalCashDesk = await cashDeskPaymentService.totalCashDeskPayments(
        office._id,
        startDate,
        endDate
      );

      const totalBalance = await calculationService.totalBalance(
        office._id,
        new Date(null),
        endDate
      );

      monthBalanceAnalytics.push({
        office: office.name,
        totalCodes,
        totalDeposit,
        totalCredit,
        totalCashDesk,
        totalBalance,
      });

      yearBalanceAnalytics.push({
        month: startDate.toLocaleString("default", { month: "long" }),
        data: monthBalanceAnalytics,
      });
    }

    new SuccessResponse(
      "Balance analytics per month returned successfully",
      yearBalanceAnalytics
    ).send(res);
  }
);

//Get Nb Stat Client ResponsesBy Stat Client  By Office
export const getNbStatClientResponsesByStatClientByOffice = asyncHandler(
  async (req: Request, res: Response) => {
    const { startDate, endDate } = req.query;
    let matchStage: any = {};
    // Filter By Date
    if (startDate && endDate) {
      matchStage.createdAt = {
        $gte: startOfDay(startDate as string),
        $lte: endOfDay(endDate as string),
      };
    }

    const nbStatClientResponsesByStatClientByOffice =
      await StatClientResponseRepo.aggregate([
        { $match: matchStage },
        { $sort: { createdAt: 1 } },
        {
          $group: {
            _id: {
              statClient: "$statClient",
              office: "$office",
            },
            nbStatClientResponses: { $sum: 1 },
          },
        },
        {
          $lookup: {
            from: "offices",
            localField: "_id.office",
            foreignField: "_id",
            as: "office",
          },
        },
        { $unwind: { path: "$office" } },
        {
          $group: {
            _id: "$_id.statClient",
            offices: {
              $push: {
                office: "$office",
                nbStatClientResponses: "$nbStatClientResponses",
              },
            },
          },
        },
        {
          $lookup: {
            from: "statclients",
            localField: "_id",
            foreignField: "_id",
            as: "statClients",
          },
        },
        {
          $addFields: {
            statClient: { $first: "$statClients" },
          },
        },
        { $project: { _id: 0, statClients: 0 } },
        { $sort: { "statClient.createdAt": 1 } },
      ]);
    new SuccessResponse(
      "Nb Stat Client Responses by Stat Client By Office returned successfully",
      {
        nbStatClientResponsesByStatClientByOffice,
      }
    ).send(res);
  }
);

//Get Nb Stat Client ResponsesBy Stat Client  By Stat
export const getNbStatClientResponsesByStatClient = asyncHandler(
  async (req: Request, res: Response) => {
    const { month, year } = req.query;
    let startDate: Date = null;
    let endDate: Date = null;
    let cachedData;

    // Create a unique key based on the request parameters
    const office = req?.user.office;
    const cacheKey = `nbStatClientResponses:${month || "yearly"}:${year}${
      office ? `__${office}` : ""
    }`;
    if (month) {
      const monthIndex = parse(month as string, "MMMM", new Date()).getMonth();
      startDate = startOfMonth(new Date(parseInt(year as string), monthIndex));
      endDate = endOfMonth(new Date(parseInt(year as string), monthIndex));
    } else {
      startDate = startOfYear(new Date(parseInt(year as string), 0, 1));
      endDate = endOfYear(new Date(parseInt(year as string), 0, 1));
    }
    // REDIS //
    if (month && year) {
      const today = startOfToday();
      const sameMonth = isSameMonth(startDate, today);
      const sameYear = isSameYear(startDate, today);

      if (!(sameMonth && sameYear)) {
        try {
          cachedData = await client.get(cacheKey);
        } catch (error) {
          console.error("Redis Error:", error);
        }

        if (cachedData) {
          // If data is found in the cache, send it as response
          const parsedData = JSON.parse(cachedData);
          return new SuccessResponse(
            "Cached data retrieved successfully",
            parsedData
          ).send(res);
        }
      }
    }
    // REDIS //

    const results: any[] = [];

    for (
      let date = startDate;
      date <= endDate;
      date = month ? addDays(date, 1) : addMonths(date, 1)
    ) {
      // Construct match stage for the current day
      const matchStage = {
        createdAt: {
          $gte: month ? startOfDay(date) : startOfMonth(date),
          $lte: month ? endOfDay(date) : endOfMonth(date),
        },
      };
      if (office) (matchStage as any).office = office;
      const nbStatClientResponsesByStatClientByOffice =
        await StatClientResponseRepo.aggregate([
          { $match: matchStage },
          {
            $group: {
              _id: "$statClient",
              totalNumber: {
                $count: {},
              },
            },
          },
          {
            $lookup: {
              from: "statclients",
              localField: "_id",
              foreignField: "_id",
              as: "statClient",
            },
          },
          {
            $unwind: {
              path: "$statClient",
            },
          },
          {
            $project: {
              _id: 0,
              statClient: "$statClient",
              totalNumber: 1,
            },
          },
          { $sort: { "statClient.createdAt": 1 } },
        ]);

      // Push the result for the current day to the results array
      results.push({
        date: month
          ? date.getDate()
          : format(new Date(2000, date.getMonth()), "MMMM"),
        stats: nbStatClientResponsesByStatClientByOffice,
      });
    }

    // Cache the data in Redis
    client.set(cacheKey, JSON.stringify(results));

    new SuccessResponse(
      "Nb Stat Client Responses by Stat Client By Office returned successfully",
      results
    ).send(res);
  }
);

//Get KPI response stat
export const getKpiResponseStat = asyncHandler(
  async (req: Request, res: Response) => {
    const { kpiId, startDate, endDate } = req.query;
    let matchStage: any = {};

    matchStage["kpis.kpi"] = new Types.ObjectId(kpiId as string);

    // Filter By Date
    if (startDate && endDate) {
      matchStage.createdAt = {
        $gte: startOfDay(startDate as string),
        $lte: endOfDay(endDate as string),
      };
    }

    const kpiResponseStat = await StatClientResponseRepo.aggregate([
      {
        $match: matchStage,
      },
      {
        $unwind: {
          path: "$kpis",
        },
      },
      {
        $match: {
          "kpis.kpi": new Types.ObjectId(kpiId as string),
        },
      },
      {
        $group: {
          _id: {
            response: "$kpis.response",
            office: "$office",
          },
          count: {
            $sum: 1,
          },
        },
      },
      {
        $lookup: {
          from: "offices",
          localField: "_id.office",
          foreignField: "_id",
          as: "office",
        },
      },
      {
        $unwind: {
          path: "$office",
        },
      },
      {
        $group: {
          _id: "$_id.response",
          offices: {
            $push: {
              office: "$office",
              count: "$count",
            },
          },
        },
      },
      {
        $addFields: {
          response: {
            $first: "$_id",
          },
        },
      },
      {
        $project: {
          _id: 0,
        },
      },
    ]);

    new SuccessResponse(
      "KPI response stat returned successfully",
      kpiResponseStat
    ).send(res);
  }
);

//Get Nb Stat Client ResponsesBy Stat Client  By Office
export const getKpiResponseStatByRangeDate = asyncHandler(
  async (req: Request, res: Response) => {
    const { kpiId, month, year } = req.query;
    let startDate: Date = null;
    let endDate: Date = null;
    let convertedKpiId = new Types.ObjectId(kpiId as string);
    let cachedData;
    const office = req?.user?.office;
    // Create a unique key based on the request parameters
    const cacheKey = `kpiResponseStatByRangeDate:${month || "yearly"}:${year}${
      office ? `__${office}` : ""
    }`;

    if (month) {
      const monthIndex = parse(month as string, "MMMM", new Date()).getMonth();
      startDate = startOfMonth(new Date(parseInt(year as string), monthIndex));
      endDate = endOfMonth(new Date(parseInt(year as string), monthIndex));
    } else {
      startDate = startOfYear(new Date(parseInt(year as string), 0, 1));
      endDate = endOfYear(new Date(parseInt(year as string), 0, 1));
    }

    // REDIS //
    if (month && year) {
      const today = startOfToday();
      const sameMonth = isSameMonth(startDate, today);
      const sameYear = isSameYear(startDate, today);

      if (!(sameMonth && sameYear)) {
        try {
          cachedData = await client.get(cacheKey);
        } catch (error) {
          console.error("Redis Error:", error);
        }

        if (cachedData) {
          // If data is found in the cache, send it as response
          const parsedData = JSON.parse(cachedData);
          return new SuccessResponse(
            "Cached data retrieved successfully",
            parsedData
          ).send(res);
        }
      }
    }
    // REDIS //

    const results: any[] = [];

    for (
      let date = startDate;
      date <= endDate;
      date = month ? addDays(date, 1) : addMonths(date, 1)
    ) {
      // Construct match stage for the current day
      const matchStage = {
        createdAt: {
          $gte: month ? startOfDay(date) : startOfMonth(date),
          $lte: month ? endOfDay(date) : endOfMonth(date),
        },
        "kpis.kpi": convertedKpiId,
      };
      if (office) (matchStage as any).office = office;
      const nbStatClientResponsesByStatClientByOffice =
        await StatClientResponseRepo.aggregate([
          {
            $match: matchStage,
          },
          {
            $unwind: {
              path: "$kpis",
            },
          },
          {
            $match: {
              "kpis.kpi": convertedKpiId,
            },
          },
          {
            $group: {
              _id: "$kpis.response",
              nbResponses: {
                $sum: 1,
              },
            },
          },
          {
            $project: {
              _id: 0,
              response: {
                $first: "$_id",
              },
              nbResponses: 1,
            },
          },
        ]);

      // Push the result for the current day to the results array
      results.push({
        date: month
          ? date.getDate()
          : format(new Date(2000, date.getMonth()), "MMMM"),
        stats: nbStatClientResponsesByStatClientByOffice,
      });
    }

    // Cache the data in Redis
    client.set(cacheKey, JSON.stringify(results));

    new SuccessResponse(
      "Nb Stat Client Responses by Stat Client By Office returned successfully",
      results
    ).send(res);
  }
);

//Get Nb Stat Client ResponsesBy Stat Client  By Office
export const totalNotPayedCodes = asyncHandler(
  async (req: Request, res: Response) => {
    let { startDate, endDate, office } = req.query;
    let convertedStartedDate: Date = undefined;
    let convertedEndDate: Date = undefined;
    let convertedOffice = undefined;

    //Super admin can select any office, admin can select only her office
    if (req.user?.role !== RoleCode.SUPER_ADMIN && req.user?.office && office) {
      throw new BadRequestError("You can't select an office !");
    } else if (office) {
      convertedOffice = new Types.ObjectId(office as string);
    } else {
      convertedOffice = req.user?.office?._id;
    }

    if (startDate && endDate) {
      convertedStartedDate = startOfDay(startDate.toString());
      convertedEndDate = endOfDay(endDate.toString());
    }

    const totalNotPayedCodes = await codeService.totalCodesBalance(
      convertedOffice,
      convertedStartedDate,
      convertedEndDate,
      undefined,
      false
    );

    new SuccessResponse(
      "Total not payed codes returned successfully",
      totalNotPayedCodes
    ).send(res);
  }
);

//Balance per office
export const getBalanceAnalyticsPerOffice = asyncHandler(
  async (req: Request, res: Response) => {
    let { startDate, endDate } = req.query;
    let convertedStartedDate: Date = undefined;
    let convertedEndDate: Date = undefined;
    const result = [];

    if (startDate && endDate) {
      convertedStartedDate = startOfDay(startDate.toString());
      convertedEndDate = endOfDay(endDate.toString());
    }

    const offices = await OfficeRepo.findAll({});

    const balanceDataPromises = offices.map(async (office) => {
      const [
        totalCodes,
        totalCredit,
        totalDeposit,
        totalCashDesk,
        totalBalance,
      ] = await Promise.all([
        codeService.totalCodesBalance(
          office._id,
          convertedStartedDate,
          convertedEndDate
        ),
        operationService.totalOperations(
          OperationType.CREDIT,
          office._id,
          convertedStartedDate,
          convertedEndDate
        ),
        operationService.totalOperations(
          OperationType.DEPOSIT,
          office._id,
          convertedStartedDate,
          convertedEndDate
        ),
        cashDeskPaymentService.totalCashDeskPayments(
          office._id,
          convertedStartedDate,
          convertedEndDate
        ),
        calculationService.totalBalance(
          office._id,
          new Date(null),
          convertedEndDate
        ),
      ]);

      return {
        office: office.name,
        totalCodes,
        totalDeposit,
        totalCredit,
        totalCashDesk,
        totalBalance,
      };
    });

    const balanceData = await Promise.all(balanceDataPromises);

    new SuccessResponse("Success", balanceData).send(res);
  }
);
