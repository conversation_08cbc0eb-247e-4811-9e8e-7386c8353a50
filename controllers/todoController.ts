import { endOfDay, startOfDay } from "date-fns";
import { Request, Response } from "express";
import { NOTIFICATIONS_DOCS } from "../db/models/Notification";
import { TodoStatus } from "../db/models/Todo";
import { RoleCode } from "../db/models/User";
import OfficeRepo from "../db/repositories/OfficeRepo";
import TodoRepo from "../db/repositories/TodoRepo";
import UserRepo from "../db/repositories/UserRepo";
import emailEmitter, { events as emailEvent } from "../events/emailEvent";
import notifEmitter, { events as notifEvents } from "../events/notifEvent";
import { NotFoundError } from "../helpers/ApiError";
import { SuccessMsgResponse, SuccessResponse } from "../helpers/ApiResponse";
import asyncHandler from "../middlewares/asyncHandler";
import { combineMetadata } from "../services/todoService";

export const createTodo = asyncHandler(async (req: Request, res: Response) => {
  const { mentions, description } = req.body;
  let mentionList = mentions;
  const everyoneMentioned = description.includes("@[everyone](everyone)");
  let concernedUsersPromise;

  if (everyoneMentioned) {
    mentionList = await UserRepo.findAndselect({ _id: { $ne: req.user._id } });
  }

  // Create the todo
  const todoData = {
    ...req.body,
    mentions: mentionList,
    author: req.user?._id,
  };

  const todoPromise = TodoRepo.create(todoData);

  // Find the concerned users
  if (everyoneMentioned) {
    concernedUsersPromise = mentionList;
  } else {
    concernedUsersPromise =
      mentionList && mentionList.length > 0
        ? UserRepo.findAndselect({
            $or: [
              { _id: { $in: mentionList } },
              { office: { $in: mentionList } },
            ],
            _id: { $ne: req.user._id },
          })
        : Promise.resolve([]);
  }

  // Await both the creation of the todo and finding the concerned users
  const [todo, concernedUsers] = await Promise.all([
    todoPromise,
    concernedUsersPromise,
  ]);

  if (concernedUsers.length > 0) {
    notifEmitter.emit(notifEvents.SEND_NOTIF, {
      currentUser: req.user,
      doc: todo._id,
      docModel: NOTIFICATIONS_DOCS.TODO,
      concernedUsers,
    });
    emailEmitter.emit(emailEvent.SEND_EMAIL, {
      currentUser: req.user,
      doc: todo._id,
      concernedUsers,
    });
  }

  return new SuccessResponse("Todo created successfully", todo).send(res);
});

// get My todos
export const getMyTodos = asyncHandler(async (req: Request, res: Response) => {
  const { startDate, endDate } = req.query;
  const query: any = {};

  if (startDate && endDate) {
    query["$expr"] = {
      $and: [
        {
          $gte: ["$createdAt", { $dateFromString: { dateString: startDate } }],
        },
        { $lte: ["$createdAt", { $dateFromString: { dateString: endDate } }] },
      ],
    };
  }

  const todos = await TodoRepo.findWithPagination(
    {
      author: req.user._id,
      ...query,
    },
    req.query
  );

  const { docs, ...meta } = todos;
  return new SuccessResponse("all todos are returned successfully", {
    docs,
    meta,
  }).send(res);
});

// get the filtered todos
export const getTheTodosFilteredTodos = asyncHandler(
  async (req: Request, res: Response) => {
    const { _id: userId, office } = req.user;
    const { startDate, endDate, assigned } = req.query;
    let filter: Object = {};

    if (startDate && endDate) {
      filter = {
        createdAt: {
          $gte: startOfDay(startDate as string),
          $lte: endOfDay(endDate as string),
        },
      };
    }

    let todos;
    if (req.user?.role !== RoleCode.SUPER_ADMIN || assigned === "true") {
      todos = await TodoRepo.findWithPagination(
        {
          ...filter,
          mentions: { $in: [userId, office] },
        },
        req.query
      );
    } else {
      todos = await TodoRepo.findWithPagination(filter, req.query);
    }

    const { docs, ...meta } = todos;

    const message =
      req.user?.role !== RoleCode.SUPER_ADMIN || assigned === "true"
        ? "All your assigned todos have been successfully retrieved."
        : "All the todos are returned successfully";

    return new SuccessResponse(message, {
      docs,
      meta,
    }).send(res);
  }
);

// get one
export const getOne = asyncHandler(async (req: Request, res: Response) => {
  const { todoId } = req.params;
  const { _id, office, role } = req.user;

  // check super admin
  const filter =
    role === RoleCode.SUPER_ADMIN
      ? {}
      : { $or: [{ author: _id }, { mentions: { $in: [office, _id] } }] };

  // find the todo
  const foundTodo = await TodoRepo.findOne({
    _id: todoId,
    ...filter,
  });

  if (!foundTodo) {
    throw new NotFoundError("Todo not found !");
  }
  // only the mentioned users added to seenBy
  if (
    foundTodo.mentions.some(
      (mention) =>
        mention?.toString() === _id?.toString() ||
        mention?.toString() === office?.toString()
    )
  ) {
    await foundTodo.update({ $addToSet: { seenBy: _id } }, { new: true });
  }

  return new SuccessResponse("Todo returned successfully", foundTodo).send(res);
});

//Delete ONE
export const deleteOne = asyncHandler(async (req: Request, res: Response) => {
  const { todoId } = req.params;
  const now = Date.now();

  // chek for super admin
  const filter =
    req.user.role === RoleCode.SUPER_ADMIN ? {} : { author: req.user._id };

  await TodoRepo.deleteOne(
    { _id: todoId, ...filter },
    {
      deletedAt: now,
      deleted: true,
    }
  );

  return new SuccessMsgResponse("Todo deleted successfully").send(res);
});

//Delete MANY
export const deleteMany = asyncHandler(async (req: Request, res: Response) => {
  const { todoIds } = req.body;
  const now = Date.now();

  const filter =
    req.user.role === RoleCode.SUPER_ADMIN ? {} : { author: req.user._id };

  await TodoRepo.updateMany(
    { _id: { $in: todoIds }, ...filter },
    {
      deleted: true,
      deletedAt: now,
    }
  );

  return new SuccessMsgResponse("todos successfully deleted").send(res);
});

// update
export const update = asyncHandler(async (req: Request, res: Response) => {
  const { todoId } = req.params;
  const { mentions, description } = req.body;
  let mentionList = mentions;
  const filter =
    req.user.role === RoleCode.SUPER_ADMIN ? {} : { author: req.user._id };

  // check if todo exists
  const existTodo = await TodoRepo.findOne({ _id: todoId, ...filter });

  if (!existTodo) {
    throw new NotFoundError("todo not found !");
  }

  if (description && description.includes("@[everyone](everyone)")) {
    const allUsers = await UserRepo.findAll({ _id: { $ne: req.user._id } });
    mentionList = allUsers.map((user) => user._id);
  }

  // update the todo
  const { status } = req.body;
  const updatedTodo = await TodoRepo.update(
    {
      _id: todoId,
      ...filter,
    },
    {
      ...req.body,
      ...(mentionList && { mentions: mentionList }),
    }
  );
  // Notify the concerned users
  const concernedUsers =
    mentions && mentions.length > 0
      ? await UserRepo.findAll({
          $or: [{ _id: { $in: mentions } }, { office: { $in: mentions } }],
          _id: { $ne: req.user._id },
        })
      : [];

  if (concernedUsers.length > 0) {
    notifEmitter.emit(notifEvents.SEND_NOTIF, {
      currentUser: req.user,
      doc: todoId,
      docModel: NOTIFICATIONS_DOCS.TODO,
      concernedUsers,
    });
    emailEmitter.emit(emailEvent.SEND_EMAIL, {
      currentUser: req.user,
      doc: todoId,
      concernedUsers,
    });
  }

  return new SuccessResponse("Todo updated successfully", updatedTodo).send(
    res
  );
});

// Update todo status
export const updateStatus = asyncHandler(
  async (req: Request, res: Response) => {
    const { todoId } = req.params;

    // check for super admin
    const filter =
      req.user.role === RoleCode.SUPER_ADMIN
        ? {}
        : {
            $or: [
              { author: req.user._id },
              { mentions: { $in: [req.user._id, req.user.office] } },
            ],
          };

    // update the todo
    const { status } = req.body;
    const updatedTodo = await TodoRepo.update(
      {
        _id: todoId,
        ...filter,
      },
      {
        ...req.body,
        ...(status === TodoStatus.COMPLETED
          ? { completedBy: req?.user?._id }
          : {}),
      }
    );

    if (!updatedTodo) {
      throw new NotFoundError("todo not found !");
    }

    return new SuccessResponse("Todo updated successfully", updatedTodo).send(
      res
    );
  }
);

// GET users and offices
export const getUsersAndOffices = asyncHandler(
  async (req: Request, res: Response) => {
    const offices = await OfficeRepo.findWithPagination({}, req.query);
    const users = await UserRepo.findWithPagination({}, req.query);

    const { docs: officeDocs, ...officeMeta } = offices;
    const { docs: userDocs, ...userMeta } = users;

    const meta = combineMetadata(userMeta, officeMeta);

    return new SuccessResponse("Users and offices are returned successfully", {
      offices: officeDocs,
      users: userDocs,
      meta,
    }).send(res);
  }
);
