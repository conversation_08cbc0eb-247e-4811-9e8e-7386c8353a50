import { Request, Response } from "express";
import { SuccessMsgResponse, SuccessResponse } from "../helpers/ApiResponse";
import asyncHandler from "../middlewares/asyncHandler";
import BookService from "../services/bookService";
import BookStockService from "../services/bookStockService";

// create
export const create = asyncHandler(async (req: Request, res: Response) => {
  const bookStock = await BookStockService.create(req.body);
  return new SuccessResponse("Book Stock created successfully", bookStock).send(
    res
  );
});

// get one
export const getOne = asyncHandler(async (req: Request, res: Response) => {
  const { bookStockId } = req.params;

  const foundBookStock = await BookStockService.findOne(req?.user, {
    _id: bookStockId,
  });

  return new SuccessResponse(
    "Book Stock returned successfully",
    foundBookStock
  ).send(res);
});

//Get all
export const getAll = asyncHandler(async (req: Request, res: Response) => {
  const bookStocks = await BookStockService.find(req.user, req.query);
  new SuccessResponse("Book Stocks returned successfully", bookStocks).send(
    res
  );
});

//Get availabke stock per office
export const getAvailableStockPerOffice = asyncHandler(
  async (req: Request, res: Response) => {
    const availableStock = await BookStockService.availableStockPerOffice(
      req.user,
      req.query
    );
    new SuccessResponse(
      "Available Books returned successfully",
      availableStock
    ).send(res);
  }
);

// Update one
export const updateOne = asyncHandler(async (req: Request, res: Response) => {
  const { bookStockId } = req.params;

  const updatedBookStock = await BookStockService.findOneAndUpdate(
    { _id: bookStockId },
    req.body
  );

  return new SuccessResponse("Book Stock updated successfully", {
    updatedBookStock,
  }).send(res);
});

// Delete one
export const deleteOne = asyncHandler(async (req: Request, res: Response) => {
  const { bookStockId } = req.params;

  await BookStockService.deleteOne({ _id: bookStockId });

  return new SuccessMsgResponse("Book Stock deleted successfully").send(res);
});
