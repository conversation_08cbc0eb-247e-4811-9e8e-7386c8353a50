import { Request, Response } from "express";
import DailyKpiRepo from "../db/repositories/DailyKpiRepo";
// import KpiRepo from '../db/repositories/KpiRepo'
import { NotFoundError } from "../helpers/ApiError";
import { SuccessMsgResponse, SuccessResponse } from "../helpers/ApiResponse";
import asyncHandler from "../middlewares/asyncHandler";

// create
export const create = asyncHandler(async (req: Request, res: Response) => {
  const newDailyKpi = await DailyKpiRepo.create(req.body);
  return new SuccessResponse(
    "Daily Kpi created successfully",
    newDailyKpi
  ).send(res);
});
// get all
export const getAll = asyncHandler(async (req: Request, res: Response) => {
  const dailyKpis = await DailyKpiRepo.findWithPagination({}, req.query);
  const { docs, ...meta } = dailyKpis;
  return new SuccessResponse("All Daily Kpis are returned successfully", {
    docs,
    meta,
  }).send(res);
});

// get one
export const getOne = asyncHandler(async (req: Request, res: Response) => {
  const { dailyKpiId } = req.params;
  const foundDailyKpi = await DailyKpiRepo.findOne({ _id: dailyKpiId });
  if (!foundDailyKpi) {
    throw new NotFoundError("Daily Kpi not found !");
  }
  return new SuccessResponse(
    "Daily Kpi returned successfully",
    foundDailyKpi
  ).send(res);
});

// update one
export const updateDailyKpi = asyncHandler(
  async (req: Request, res: Response) => {
    const { dailyKpiId } = req.params;
    const updatedDailyKpi = await DailyKpiRepo.update(dailyKpiId, req.body);
    if (!updatedDailyKpi) {
      throw new NotFoundError("Daily Kpi not found !");
    }
    return new SuccessResponse("Daily Kpi updated successfully", {
      updatedDailyKpi,
    }).send(res);
  }
);

//Delete ONE
export const deleteOne = asyncHandler(async (req: Request, res: Response) => {
  const { dailyKpiId } = req.params;

  //Check Kpi
  const dailyKpi = await DailyKpiRepo.findOne({
    _id: dailyKpiId,
  });
  if (!dailyKpi) {
    throw new NotFoundError("Daily Kpi Not Found !");
  }
  const deletedDailyKpis = await DailyKpiRepo.count({
    deleted: true,
  });
  await DailyKpiRepo.deleteOne(
    { _id: dailyKpi._id },
    {
      $set: {
        name: `old${deletedDailyKpis}${dailyKpi.name}`,
        deletedAt: new Date(),
        deleted: true,
      },
    }
  );
  return new SuccessMsgResponse("Daily Kpi deleted successfully").send(res);
});

//Delete MANY
export const deleteMany = asyncHandler(async (req: Request, res: Response) => {
  const { data } = req.body;
  const now = Date.now();
  await DailyKpiRepo.updateMany({ _id: { $in: data } }, [
    {
      $set: {
        deleted: true,
        deletedAt: now,
        name: {
          $concat: ["$name", now.toString()],
        },
      },
    },
  ]);
  return new SuccessMsgResponse("Daily Kpis successfully deleted").send(res);
});
