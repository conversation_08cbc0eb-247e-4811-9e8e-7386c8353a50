import { endOfDay, startOfDay } from "date-fns";
import { Request, Response } from "express";
import { RoleCode } from "../db/models/User";
import DailyResponseRepo from "../db/repositories/DailyResponseRepo";
import { BadRequestError, NotFoundError } from "../helpers/ApiError";
import { SuccessMsgResponse, SuccessResponse } from "../helpers/ApiResponse";
import asyncHandler from "../middlewares/asyncHandler";
import dailyResponseService from "../services/dailyResponseService";

//Create dailyResponse
export const create = asyncHandler(async (req: Request, res: Response) => {
  // Check if a DailyResponse already exists for today
  const existing = await DailyResponseRepo.findOne({
    admin: req.user?._id,
    createdAt: {
      $gte: startOfDay(new Date()),
      $lte: endOfDay(new Date()),
    },
  });

  if (existing) {
    throw new BadRequestError(
      "You have already submitted a daily response for today."
    );
  }

  const newDailyResponse = await DailyResponseRepo.create({
    ...req.body,
    admin: req.user?._id,
    office: req.user?.office,
  });

  return new SuccessResponse(
    "Daily Response created successfully",
    newDailyResponse
  ).send(res);
});

//Get all dailyResponses
export const getAll = asyncHandler(async (req: Request, res: Response) => {
  let filter: Object = {};

  //Admins can see their today's dailyResponses only
  if (req.user?.role !== RoleCode.SUPER_ADMIN) {
    // Define the date range for today
    const todayStart = startOfDay(new Date());
    const todayEnd = endOfDay(new Date());

    filter = {
      office: req.user?.office,
      createdAt: {
        $gte: todayStart,
        $lte: todayEnd,
      },
    };
  }

  // Fetch dailyResponses based on the filter
  const dailyResponses = (await DailyResponseRepo.findWithPagination(
    filter,
    req.query
  )) as unknown as PaginationModel;

  const { docs, ...meta } = dailyResponses;

  // Send a success response
  new SuccessResponse("Daily Response returned successfully", {
    docs,
    meta,
  }).send(res);
});

//Get one daily response
export const getOne = asyncHandler(async (req: Request, res: Response) => {
  const { dailyResponseId } = req.params;
  let filter: Object = { _id: dailyResponseId };

  //Admins can see their today's Daily Responses only
  if (req.user?.role !== RoleCode.SUPER_ADMIN) {
    // Define the date range for today
    const todayStart = startOfDay(new Date());
    const todayEnd = endOfDay(new Date());

    filter = {
      _id: dailyResponseId,
      office: req.user?.office,
      createdAt: {
        $gte: todayStart,
        $lte: todayEnd,
      },
    };
  }

  const dailyResponse = await DailyResponseRepo.findOne(filter);

  //Check if daily response exist
  if (!dailyResponse) {
    throw new NotFoundError("Daily Response not found !");
  }

  return new SuccessResponse(
    "Daily Response returned successfully",
    dailyResponse
  ).send(res);
});

//UPDATE ONE
export const update = asyncHandler(async (req: Request, res: Response) => {
  const { dailyResponseId } = req.params;

  //Get dailyResponse
  const dailyResponse = await DailyResponseRepo.findOne({
    _id: dailyResponseId,
  });

  if (!dailyResponse) {
    throw new NotFoundError("Daily Response not found!");
  }

  if (
    req.user?.role !== RoleCode.SUPER_ADMIN &&
    dailyResponse.admin?._id?.toString() !== req.user?._id.toString()
  ) {
    throw new BadRequestError("You can't edit this daily response");
  }

  //Update
  const updatedDailyResponse = await DailyResponseRepo.findOneAndUpdate(
    {
      _id: dailyResponseId,
    },
    { ...req.body }
  );

  return new SuccessResponse(
    "Daily response successfully updated",
    updatedDailyResponse
  ).send(res);
});

//DELETE ONE
export const deleteOne = asyncHandler(async (req: Request, res: Response) => {
  const { dailyResponseId } = req.params;

  //Get dailyResponse
  const dailyResponse = await DailyResponseRepo.findOne({
    _id: dailyResponseId,
  });

  if (!dailyResponse) {
    throw new NotFoundError("Daily Response not found!");
  }

  //Delete
  await DailyResponseRepo.updateMany(
    {
      _id: dailyResponseId,
    },
    {
      deleted: true,
      deletedAt: Date.now(),
    }
  );

  return new SuccessMsgResponse("Daily Response successfully deleted").send(
    res
  );
});

//DELETE MANY
export const deleteMany = asyncHandler(async (req: Request, res: Response) => {
  const { dailyResponseIds } = req.body;

  // Delete daily responses
  await DailyResponseRepo.updateMany(
    {
      _id: { $in: dailyResponseIds },
    },
    {
      deleted: true,
      deletedAt: Date.now(),
    }
  );

  return new SuccessMsgResponse("Daily Responses successfully deleted").send(
    res
  );
});

//Get daily response filtered
export const getByFilter = asyncHandler(async (req: Request, res: Response) => {
  const query = dailyResponseService.handleQuery(req.query);

  const dailyResponses = await DailyResponseRepo.findPaginate(query, req.query);
  const { docs, ...meta } = dailyResponses;

  return new SuccessResponse("Daily Response returned successfully", {
    docs,
    meta,
  }).send(res);
});
