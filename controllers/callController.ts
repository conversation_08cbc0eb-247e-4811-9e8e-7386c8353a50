import { addDays, endOfDay, startOfDay } from "date-fns";
import { Request, Response } from "express";
import { RoleCode } from "../db/models/User";
import { WorktimeType } from "../db/models/Worktime";
import CallRepo from "../db/repositories/CallRepo";
import WorktimeRepo from "../db/repositories/WortimeRepo";
import {
  AuthFailureError,
  BadRequestError,
  NotFoundError,
} from "../helpers/ApiError";
import { SuccessResponse } from "../helpers/ApiResponse";
import asyncHandler from "../middlewares/asyncHandler";
import { generateFilterCall } from "../utils/generateFilterCall";

export const createCall = asyncHandler(async (req: Request, res: Response) => {
  const currentUserId = req.user?._id;
  const callExist = await CallRepo.findOne({
    user: currentUserId,
    date: new Date(req.body.date),
  });
  if (callExist) {
    throw new BadRequestError("Already created call in this day");
  }
  req.body.user = currentUserId;
  req.body.office = req.user?.office;
  const newCall = await CallRepo.create(req.body);
  return new SuccessResponse("Call created successfully", newCall).send(res);
});

export const updateCall = asyncHandler(async (req: Request, res: Response) => {
  const { callId } = req.params;
  const { received, maked } = req.body.calls;
  const newCall = await CallRepo.findByIdAndUpdate(callId, {
    ...req.body,
    totalCalls: maked + received,
  });
  if (!newCall) {
    throw new NotFoundError("call not found!");
  }

  return new SuccessResponse("Call updated successfully", newCall).send(res);
});

export const getOneCall = asyncHandler(async (req: Request, res: Response) => {
  const { callId } = req.params;
  const call = await CallRepo.findOne({
    _id: callId,
  });
  if (!call) {
    throw new NotFoundError("call not found!");
  }
  if (
    req.user?.role !== RoleCode.SUPER_ADMIN &&
    call.user.toString() !== req.user?._id.toString()
  )
    throw new AuthFailureError("You can't access to this call");
  return new SuccessResponse("call returned successfully", call).send(res);
});

export const getByFilter = asyncHandler(async (req: Request, res: Response) => {
  let { startDate, endDate, user } = req.query;

  if (user) {
    let start = new Date(startDate.toString());
    let end = new Date(endDate.toString());

    const datePromises = [];

    for (
      let date = startOfDay(start);
      date <= endOfDay(end);
      date = addDays(date, 1)
    ) {
      const currentStartDate = startOfDay(date);
      const currentEndDate = endOfDay(date);
      const datePromise = (async () => {
        const call = await CallRepo.findOne({
          createdAt: {
            $gte: currentStartDate,
            $lte: currentEndDate,
          },
          user,
        });

        if (call) {
          return {
            date: currentEndDate.toISOString().split("T")[0],
            calls: call,
            forget: false,
          };
        } else {
          const workDocument = await WorktimeRepo.findOne({
            startDate: { $gte: currentStartDate },
            endDate: { $lte: currentEndDate },
            type: WorktimeType.WORK,
            user,
          });
          return {
            date: currentEndDate.toISOString().split("T")[0],
            calls: null,
            forget: !!workDocument,
          };
        }
      })();

      datePromises.push(datePromise);
    }

    const results = await Promise.all(datePromises);
    return new SuccessResponse("call returned successfully", results).send(res);
  }

  let filter = {};
  if (startDate && endDate) {
    filter = {
      date: {
        $gte: startDate,
        $lte: endDate,
      },
    };
  }
  const userCall = (await CallRepo.findWithPagination(
    filter,
    req.query
  )) as unknown as PaginationModel;
  const { docs, ...meta } = userCall;
  return new SuccessResponse("call returned successfully", { docs, meta }).send(
    res
  );
});

export const getMyCalls = asyncHandler(async (req: Request, res: Response) => {
  const calls = await CallRepo.findWithPagination(
    { user: req.user?._id },
    req.query
  );
  const { docs, ...meta } = calls;
  return new SuccessResponse("all calls are returned successfully", {
    docs,
    meta,
  }).send(res);
});

export const getByDate = asyncHandler(async (req: Request, res: Response) => {
  const currentUserId = req.user?._id;
  const { date } = req.query;
  const queryDate = new Date(date as string).toISOString();
  const startDate = startOfDay(queryDate);
  const endDate = endOfDay(queryDate);
  const call = await CallRepo.findOne({
    user: currentUserId,
    date: {
      $gte: startDate,
      $lte: endDate,
    },
  });
  return new SuccessResponse("call returned successfully", call).send(res);
});
export const getAllCalls = asyncHandler(async (req: Request, res: Response) => {
  let filter = generateFilterCall(req.query);
  const calls = await CallRepo.findWithPagination(filter, req.query);
  const { docs, ...meta } = calls;
  return new SuccessResponse("Calls returned successfully", {
    docs,
    meta,
  }).send(res);
});
