import { Request, Response } from "express";
import { BookSaleTransaction } from "../db/models/BookSaleTransaction";
import { RoleCode } from "../db/models/User";
import { SuccessMsgResponse, SuccessResponse } from "../helpers/ApiResponse";
import asyncHandler from "../middlewares/asyncHandler";
import BookSaleTransactionService from "../services/bookSaleTransactionService";
import BookService from "../services/bookService";

// create
export const create = asyncHandler(async (req: Request, res: Response) => {
  const bookSaleTransaction = await BookSaleTransactionService.create(
    req.body,
    req?.user
  );
  return new SuccessResponse(
    "Book Sale Transaction created successfully",
    bookSaleTransaction
  ).send(res);
});

// get one
export const getOne = asyncHandler(async (req: Request, res: Response) => {
  const { bookSaleTransactionId } = req.params;

  const foundBookSaleTransaction = await BookSaleTransactionService.findOne(
    {
      _id: bookSaleTransactionId,
    },
    req?.user
  );

  return new SuccessResponse(
    "Book Sale Transaction returned successfully",
    foundBookSaleTransaction
  ).send(res);
});

//Get all
export const getAll = asyncHandler(async (req: Request, res: Response) => {
  const bookSaleTransactions = await BookSaleTransactionService.find(
    req.query,
    req.user
  );
  new SuccessResponse(
    "Book Sale Transactions returned successfully",
    bookSaleTransactions
  ).send(res);
});

// Update one
export const updateOne = asyncHandler(async (req: Request, res: Response) => {
  const { bookSaleTransactionId } = req.params;

  const updatedBookSaleTransaction =
    await BookSaleTransactionService.findOneAndUpdate(
      { _id: bookSaleTransactionId },
      req.body
    );

  return new SuccessResponse("Book Sale Transaction updated successfully", {
    updatedBookSaleTransaction,
  }).send(res);
});

// Delete one
export const deleteOne = asyncHandler(async (req: Request, res: Response) => {
  const { bookSaleTransactionId } = req.params;

  await BookSaleTransactionService.deleteOne({ _id: bookSaleTransactionId });

  return new SuccessMsgResponse(
    "Book Sale Transaction deleted successfully"
  ).send(res);
});
