import { Request, Response } from "express";
import { RoleCode } from "../db/models/User";
import UserRepo from "../db/repositories/UserRepo";
import WorktimeRepo from "../db/repositories/WortimeRepo";
import { BadRequestError, NotFoundError } from "../helpers/ApiError";
import { SuccessMsgResponse, SuccessResponse } from "../helpers/ApiResponse";
import asyncHandler from "../middlewares/asyncHandler";
import DepartmentWorktimeService from "../services/departmentWorktimeService";
import workTimeService from "../services/worktimeService";
import { createDateForTime } from "../utils/time";

// create MY worktimes
export const createMyWorktimes = asyncHandler(
  async (req: Request, res: Response) => {
    const currentUserId = req.user._id;
    const { data } = req.body;

    //Check if not Super Admin & startDate is greater than today
    const youngerToday = workTimeService.youngerThanToday(data);
    if (req.user?.role !== RoleCode.SUPER_ADMIN && youngerToday) {
      throw new BadRequestError(
        "You cannot create worktime with a date earlier than today"
      );
    }

    // split the worktime period per hours
    const worktimes = workTimeService.appendTimestampsToObjects(
      data,
      currentUserId
    );

    // Check for overlapping instances in the body of the request
    if (workTimeService.hasOverlap(worktimes)) {
      throw new BadRequestError(
        "Cannot create worktime. Overlapping worktime periods found in the request."
      );
    }

    // Validate if there are any overlapping worktime periods
    const overlappingWorktimes = await WorktimeRepo.findAll({
      userId: req.user._id,
      $or: worktimes.map((wt) => ({
        $and: [
          {
            startDate: { $lt: new Date(wt.endDate) },
          },
          {
            endDate: { $gt: new Date(wt.startDate) },
          },
        ],
      })),
    });

    if (overlappingWorktimes.length > 0) {
      throw new BadRequestError(
        "Cannot create worktime. Overlapping worktime periods found."
      );
    }

    const startDateWorktimes = new Date(worktimes[0].startDate);

    if (req.user.department) {
      const year = startDateWorktimes.getFullYear();
      const month = startDateWorktimes.getMonth();
      const day = startDateWorktimes.getDate();

      const currentDate = new Date(Date.UTC(year, month, day));

      const departmentWorktime = await DepartmentWorktimeService.findOne({
        department: req.user.department,
        startDate: { $lte: currentDate },
        endDate: { $gte: currentDate },
      });

      if (!departmentWorktime) {
        throw new BadRequestError(
          "No Department Worktime found, Please contact your Admin !"
        );
      }

      const departmentWorktimeBaseDate = new Date(
        startDateWorktimes.getFullYear(),
        startDateWorktimes.getMonth(),
        startDateWorktimes.getDate()
      );

      // Convert startHour and endHour to Date objects
      const startLimit = createDateForTime(
        departmentWorktimeBaseDate,
        departmentWorktime.startHour
      );
      const endLimit = createDateForTime(
        departmentWorktimeBaseDate,
        departmentWorktime.endHour
      );

      // Check each worktimne
      worktimes.forEach((worktime) => {
        const { startDate, endDate } = worktime;
        if (
          new Date(startDate) < startLimit ||
          new Date(startDate) > endLimit ||
          new Date(endDate) > endLimit
        ) {
          throw new BadRequestError(
            "Worktime exceeds the allowed limits for your department !"
          );
        }
      });
    }

    // insert many worktimes
    const createdWorktimePeriod = await WorktimeRepo.createMany(worktimes);

    return new SuccessResponse(
      "Worktime created successfully",
      createdWorktimePeriod
    ).send(res);
  }
);

// create employee worktimes
export const createEmployeeWorktimes = asyncHandler(
  async (req: Request, res: Response) => {
    const { data } = req.body;
    const { userId } = req.params;

    // chekc if the employee exists or not
    const foundEmployee = await UserRepo.findOne({ _id: userId });
    if (!foundEmployee) {
      throw new BadRequestError("Employee not found");
    }

    // split the worktime period per hours
    const worktimes = workTimeService.appendTimestampsToObjects(data, userId);

    // Check for overlapping instances in the body of the request
    if (workTimeService.hasOverlap(worktimes)) {
      throw new BadRequestError(
        "Cannot create worktime. Overlapping worktime periods found in the request."
      );
    }

    // Validate if there are any overlapping worktime periods
    const overlappingWorktimes = await WorktimeRepo.findAll({
      userId: userId,
      $or: worktimes.map((wt) => ({
        $and: [
          {
            startDate: { $lt: new Date(wt.endDate) },
          },
          {
            endDate: { $gt: new Date(wt.startDate) },
          },
        ],
      })),
    });

    if (overlappingWorktimes.length > 0) {
      throw new BadRequestError(
        "Cannot create worktime. Overlapping worktime periods found."
      );
    }
    // insert many worktimes
    const createdWorktimePeriod = await WorktimeRepo.createMany(worktimes);

    return new SuccessResponse(
      "Worktime created successfully",
      createdWorktimePeriod
    ).send(res);
  }
);

export const getEmployeeWorktimes = asyncHandler(
  async (req: Request, res: Response) => {
    const { userId } = req.params;
    const worktimes = await WorktimeRepo.findWithPagination(
      { userId },
      req.query
    );
    const { docs, ...meta } = worktimes;
    return new SuccessResponse("Worktime periods returned successfully", {
      docs,
      meta,
    }).send(res);
  }
);

export const getMyWorktimes = asyncHandler(
  async (req: Request, res: Response) => {
    const myWorktimes = await WorktimeRepo.findWithPagination(
      { userId: req.user._id },
      req.query
    );
    const { docs, ...meta } = myWorktimes;
    return new SuccessResponse("worktimes returned successfully", {
      docs,
      meta,
    }).send(res);
  }
);

// update my worktime
export const updateMyWorktime = asyncHandler(
  async (req: Request, res: Response) => {
    const { worktimeId } = req.params;
    const { startDate, endDate } = req.body;
    const userId = req.user._id;
    // Validate if there are any overlapping worktime periods
    const overlappingWorktimes = await WorktimeRepo.findAll({
      $and: [
        { userId: req.user._id },
        { startDate: { $lt: new Date(endDate) } },
        { endDate: { $gt: new Date(startDate) } },
        { _id: { $ne: worktimeId } },
      ],
    });

    if (overlappingWorktimes.length > 0) {
      throw new BadRequestError(
        "Cannot create worktime. Overlapping worktime periods found."
      );
    }
    // update worktime
    const updatedWorktime = await WorktimeRepo.update(
      { _id: worktimeId, userId },
      req.body
    );

    if (!updatedWorktime) {
      throw new NotFoundError("Work Time Not Found !");
    }
    return new SuccessResponse("Work Time updated successfully", {
      updatedWorktime,
    }).send(res);
  }
);

// update Employee worktime
export const updateEmployeeWorktime = asyncHandler(
  async (req: Request, res: Response) => {
    const { worktimeId, userId } = req.params;
    const { startDate, endDate } = req.body;

    // chekc if the employee exists or not
    const foundEmployee = await UserRepo.findOne({ _id: userId });
    if (!foundEmployee) {
      throw new BadRequestError("Employee not found");
    }

    // Validate if there are any overlapping worktime periods
    const overlappingWorktimes = await WorktimeRepo.findAll({
      $and: [
        { userId: userId },
        { startDate: { $lt: new Date(endDate) } },
        { endDate: { $gt: new Date(startDate) } },
        { _id: { $ne: worktimeId } },
      ],
    });

    if (overlappingWorktimes.length > 0) {
      throw new BadRequestError(
        "Cannot create worktime. Overlapping worktime periods found."
      );
    }
    // update worktime
    const updatedWorktime = await WorktimeRepo.update(
      { _id: worktimeId, userId: userId },
      req.body
    );

    if (!updatedWorktime) {
      throw new NotFoundError("Work Time Not Found !");
    }
    return new SuccessResponse("Work Time updated successfully", {
      updatedWorktime,
    }).send(res);
  }
);

// delete my worktimes
export const deleteMyWorktime = asyncHandler(
  async (req: Request, res: Response) => {
    const { data } = req.body;
    const now = Date.now();
    await WorktimeRepo.updateMany(
      { _id: { $in: data }, userId: req.user._id },
      { deleted: true, deletedAt: now }
    );
    return new SuccessMsgResponse("Work Time successfully deleted").send(res);
  }
);

// delete employee worktimes
export const deleteEmployeeWorktime = asyncHandler(
  async (req: Request, res: Response) => {
    const { data } = req.body;
    const { userId } = req.params;

    const now = Date.now();
    await WorktimeRepo.updateMany(
      { _id: { $in: data }, userId: userId },
      { deleted: true, deletedAt: now }
    );
    return new SuccessMsgResponse("Work Times successfully deleted").send(res);
  }
);
