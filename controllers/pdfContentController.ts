import { Request, Response } from "express";
import PdfContentRepo from "../db/repositories/PdfContentRepo";
import { NotFoundError } from "../helpers/ApiError";
import { SuccessResponse } from "../helpers/ApiResponse";
import asyncHandler from "../middlewares/asyncHandler";

// Get All
export const getAll = asyncHandler(async (req: Request, res: Response) => {
  const pdfContents = await PdfContentRepo.findWithPagination({}, req.query);
  const { docs, ...meta } = pdfContents;
  new SuccessResponse("Pdf contents returned successfully", { docs, meta }).send(
    res
  );
});

export const getOne = asyncHandler(async (req: Request, res: Response) => {
  const { pdfContentId } = req.params;

  const pdfContent = await PdfContentRepo.findOne({ _id: pdfContentId });

  if (!pdfContent) {
    throw new NotFoundError("Pdf content not found");
  }

  return new SuccessResponse(
    "Pdf content is returned successfully",
    pdfContent
  ).send(res);
});

export const updateOne = asyncHandler(async (req: Request, res: Response) => {
  const { pdfContentId } = req.params;

  const foundPdfContent = await PdfContentRepo.findOne({
    _id: pdfContentId,
  });

  if (!foundPdfContent) {
    throw new NotFoundError("Pdf content not found");
  }

  // update
  const pdfContent = await PdfContentRepo.updateOne(
    { _id: pdfContentId },
    req.body
  );

  return new SuccessResponse(
    "Pdf content updated successfully",
    pdfContent
  ).send(res);
});
